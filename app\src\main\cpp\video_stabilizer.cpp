#include <jni.h>
#include <string>
#include <android/log.h>
#include <android/bitmap.h>
#include <opencv2/opencv.hpp>
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/video/tracking.hpp>
#include <opencv2/features2d.hpp>

#define LOG_TAG "VideoStabilizer"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

using namespace cv;
using namespace std;

// 全局变量，用于存储前一帧
Mat prevGray;
bool isFirstFrame = true;

// 最大平滑窗口大小 - 减小平滑窗口以减少跳动
const int SMOOTHING_RADIUS = 5;
// 存储变换矩阵的队列
deque<Mat> transformations;

/**
 * 计算两帧之间的变换矩阵
 */
Mat estimateTransform(const Mat& prevImg, const Mat& currImg) {
    // 检测特征点
    vector<Point2f> prevPts, currPts;
    goodFeaturesToTrack(prevImg, prevPts, 200, 0.01, 30);

    if (prevPts.size() < 10) {
        LOGI("Not enough features detected, returning identity transform");
        return Mat::eye(3, 3, CV_64F);
    }

    // 计算光流
    vector<uchar> status;
    vector<float> err;
    calcOpticalFlowPyrLK(prevImg, currImg, prevPts, currPts, status, err);

    // 筛选有效的特征点
    vector<Point2f> prevGood, currGood;
    for (size_t i = 0; i < status.size(); i++) {
        if (status[i]) {
            prevGood.push_back(prevPts[i]);
            currGood.push_back(currPts[i]);
        }
    }

    if (prevGood.size() < 4) {
        LOGI("Not enough good matches, returning identity transform");
        return Mat::eye(3, 3, CV_64F);
    }

    // 计算变换矩阵
    Mat transform = estimateAffinePartial2D(prevGood, currGood);

    // 转换为3x3矩阵
    Mat fullTransform = Mat::eye(3, 3, CV_64F);
    if (!transform.empty()) {
        transform.copyTo(fullTransform.rowRange(0, 2));

        // 计算平移量
        double tx = transform.at<double>(0, 2);
        double ty = transform.at<double>(1, 2);
        double translation = sqrt(tx*tx + ty*ty);

        // 如果平移量很小，忽略这个变换（返回单位矩阵）
        // 这可以减少小幅度抖动导致的画面跳动
        double min_translation = 2.0; // 最小平移量阈值
        if (translation < min_translation) {
            LOGI("Small translation (%.2f px) detected, ignoring", translation);
            return Mat::eye(3, 3, CV_64F);
        }

        LOGI("Translation: %.2f px", translation);
    }

    return fullTransform;
}

/**
 * 平滑变换矩阵
 */
Mat smoothTransformation(const Mat& transform) {
    // 添加当前变换到队列
    transformations.push_back(transform.clone());

    // 保持队列大小不超过平滑窗口
    if (transformations.size() > SMOOTHING_RADIUS * 2 + 1) {
        transformations.pop_front();
    }

    // 计算平滑后的变换
    Mat smoothTransform = Mat::eye(3, 3, CV_64F);
    if (transformations.size() > 0) {
        // 简单平均平滑
        for (const Mat& t : transformations) {
            smoothTransform += t;
        }
        smoothTransform /= static_cast<double>(transformations.size());

        // 限制变换幅度，防止大幅度跳动
        // 限制平移量
        double max_translation = 10.0; // 最大平移像素数
        double tx = smoothTransform.at<double>(0, 2);
        double ty = smoothTransform.at<double>(1, 2);

        // 限制平移量在合理范围内
        tx = std::max(-max_translation, std::min(tx, max_translation));
        ty = std::max(-max_translation, std::min(ty, max_translation));

        smoothTransform.at<double>(0, 2) = tx;
        smoothTransform.at<double>(1, 2) = ty;

        // 限制旋转和缩放
        // 提取旋转角度和缩放因子
        double a = smoothTransform.at<double>(0, 0);
        double b = smoothTransform.at<double>(0, 1);
        double c = smoothTransform.at<double>(1, 0);
        double d = smoothTransform.at<double>(1, 1);

        // 计算缩放因子
        double sx = sqrt(a*a + b*b);
        double sy = sqrt(c*c + d*d);

        // 限制缩放因子在合理范围内
        double min_scale = 0.9;
        double max_scale = 1.1;
        sx = std::max(min_scale, std::min(sx, max_scale));
        sy = std::max(min_scale, std::min(sy, max_scale));

        // 重新计算变换矩阵
        double angle = atan2(b, a);
        a = sx * cos(angle);
        b = sx * sin(angle);
        c = -sy * sin(angle);
        d = sy * cos(angle);

        smoothTransform.at<double>(0, 0) = a;
        smoothTransform.at<double>(0, 1) = b;
        smoothTransform.at<double>(1, 0) = c;
        smoothTransform.at<double>(1, 1) = d;
    }

    return smoothTransform;
}

/**
 * 应用变换到图像
 */
void applyTransform(const Mat& input, Mat& output, const Mat& transform) {
    warpAffine(input, output, transform.rowRange(0, 2), input.size());
}

extern "C" {

/**
 * 处理I420格式的视频帧
 */
JNIEXPORT jboolean JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_stabilizeI420Frame(
        JNIEnv *env, jobject thiz,
        jbyteArray y_data, jbyteArray u_data, jbyteArray v_data,
        jint width, jint height, jint y_stride, jint u_stride, jint v_stride,
        jboolean process_luma_only) {

    // 获取I420数据
    jbyte *yBytes = env->GetByteArrayElements(y_data, NULL);
    jbyte *uBytes = env->GetByteArrayElements(u_data, NULL);
    jbyte *vBytes = env->GetByteArrayElements(v_data, NULL);

    // 创建Mat对象
    Mat yMat(height, y_stride, CV_8UC1, (unsigned char *)yBytes);
    Mat uMat(height / 2, u_stride, CV_8UC1, (unsigned char *)uBytes);
    Mat vMat(height / 2, v_stride, CV_8UC1, (unsigned char *)vBytes);

    // 只使用Y通道进行稳定
    Mat currGray = yMat(Rect(0, 0, width, height)).clone();

    if (isFirstFrame) {
        // 第一帧，初始化
        prevGray = currGray.clone();
        isFirstFrame = false;

        // 释放数组
        env->ReleaseByteArrayElements(y_data, yBytes, 0);
        env->ReleaseByteArrayElements(u_data, uBytes, 0);
        env->ReleaseByteArrayElements(v_data, vBytes, 0);

        return JNI_FALSE;
    }

    // 估计变换
    Mat transform = estimateTransform(prevGray, currGray);

    // 平滑变换
    Mat smoothTransform = smoothTransformation(transform);

    // 创建完整的YUV图像进行处理
    try {
        // 应用变换到Y通道
        Mat stabilizedY(height, y_stride, CV_8UC1);
        applyTransform(yMat, stabilizedY, smoothTransform);

        // 安全地复制Y通道数据回原始数组
        int y_size = min(width * height, y_stride * height);
        memcpy(yBytes, stabilizedY.data, y_size);

        // 如果不是只处理亮度通道，则处理色度通道
        if (!process_luma_only) {
            try {
                // 获取实际的U和V通道尺寸
                int uv_height = height / 2;
                int uv_width = width / 2;

                // 使用更安全的方法处理UV通道
                // 创建新的UV通道图像，而不是直接修改原始数据
                Mat uMatSafe, vMatSafe;

                // 复制原始UV数据到安全的Mat对象
                uMat(Rect(0, 0, min(u_stride, width/2), min(uMat.rows, height/2))).copyTo(uMatSafe);
                vMat(Rect(0, 0, min(v_stride, width/2), min(vMat.rows, height/2))).copyTo(vMatSafe);

                // 缩小变换矩阵以适应U和V通道
                Mat scaledTransform = smoothTransform.clone();
                // 使用更小的平移量，减少色度通道的变化
                scaledTransform.at<double>(0, 2) /= 2.5; // 使用更小的缩放因子
                scaledTransform.at<double>(1, 2) /= 2.5;

                // 创建目标UV通道
                Mat stabilizedU, stabilizedV;

                // 应用变换，但使用更保守的参数
                warpAffine(uMatSafe, stabilizedU, scaledTransform.rowRange(0, 2),
                           Size(uMatSafe.cols, uMatSafe.rows),
                           INTER_LINEAR, BORDER_REPLICATE); // 使用边界复制模式

                warpAffine(vMatSafe, stabilizedV, scaledTransform.rowRange(0, 2),
                           Size(vMatSafe.cols, vMatSafe.rows),
                           INTER_LINEAR, BORDER_REPLICATE);

                // 安全地复制回原始数组
                if (!stabilizedU.empty() && !stabilizedV.empty()) {
                    // 确保尺寸匹配
                    int u_rows = min(stabilizedU.rows, uv_height);
                    int u_cols = min(stabilizedU.cols, u_stride);
                    int v_rows = min(stabilizedV.rows, uv_height);
                    int v_cols = min(stabilizedV.cols, v_stride);

                    // 逐行复制，更安全
                    for (int i = 0; i < u_rows; i++) {
                        memcpy(uBytes + i * u_stride, stabilizedU.ptr(i), u_cols);
                    }

                    for (int i = 0; i < v_rows; i++) {
                        memcpy(vBytes + i * v_stride, stabilizedV.ptr(i), v_cols);
                    }

                    LOGI("Successfully processed full YUV frame with safe method");
                } else {
                    LOGI("UV stabilization failed, keeping original UV data");
                }
            } catch (cv::Exception& e) {
                LOGE("Error processing UV channels: %s", e.what());
                LOGI("Falling back to luma-only processing");
                // 出错时只处理亮度通道
            }
        } else {
            // 只处理亮度通道，色度通道保持不变
            LOGI("Processed luma channel only (Y channel): %dx%d", width, height);
        }
    } catch (cv::Exception& e) {
        LOGE("OpenCV error: %s", e.what());

        // 出错时，保持原始数据不变
        isFirstFrame = true;
        prevGray.release();
        transformations.clear();

        // 释放数组
        env->ReleaseByteArrayElements(y_data, yBytes, 0);
        env->ReleaseByteArrayElements(u_data, uBytes, 0);
        env->ReleaseByteArrayElements(v_data, vBytes, 0);

        return JNI_FALSE;
    }

    // 更新前一帧
    prevGray = currGray.clone();

    // 释放数组
    env->ReleaseByteArrayElements(y_data, yBytes, 0);
    env->ReleaseByteArrayElements(u_data, uBytes, 0);
    env->ReleaseByteArrayElements(v_data, vBytes, 0);

    return JNI_TRUE;
}

/**
 * 处理纹理格式的视频帧
 * 注意：这个方法需要OpenGL环境支持，实现较为复杂
 * 这里提供一个简化版本，实际应用中可能需要更复杂的实现
 */
JNIEXPORT jint JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_stabilizeTextureFrame(
        JNIEnv *env, jobject thiz, jint texture_id, jint width, jint height) {

    // 纹理处理需要OpenGL环境，这里只返回原始纹理ID
    // 实际应用中，需要从纹理读取像素数据，进行稳定处理，然后创建新纹理
    LOGI("Texture stabilization not fully implemented, returning original texture");

    return texture_id;
}

/**
 * 重置稳定器状态
 */
JNIEXPORT void JNICALL
Java_com_srthinker_bbnice_recognition_VideoProcessor_resetStabilizer(
        JNIEnv *env, jobject thiz) {

    isFirstFrame = true;
    prevGray.release();
    transformations.clear();

    LOGI("Stabilizer reset");
}

} // extern "C"
