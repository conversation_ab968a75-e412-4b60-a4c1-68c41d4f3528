<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".test.RepositoryTestActivity">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Repository接口测试"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:id="@+id/scroll_view"
        android:background="#F5F5F5"
        android:padding="8dp">

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:fontFamily="monospace" />

    </ScrollView>

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_login"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设备登录"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_qr_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="获取二维码"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_device_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设备列表"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_report_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="上报设备状态"
                android:layout_marginEnd="8dp" />

        </LinearLayout>

    </HorizontalScrollView>

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_report_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="上报位置"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_report_command"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="上报命令状态"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_get_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="获取位置"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_clear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除日志"
                android:layout_marginEnd="8dp" />

        </LinearLayout>

    </HorizontalScrollView>

</LinearLayout>
