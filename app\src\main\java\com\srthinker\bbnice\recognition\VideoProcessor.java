package com.srthinker.bbnice.recognition;

import android.opengl.EGL14;
import android.opengl.Matrix;
import android.util.Log;

import com.faceunity.core.enumeration.FUInputBufferEnum;
import com.faceunity.core.enumeration.FUInputTextureEnum;
import com.faceunity.nama.FUConfig;
import com.faceunity.nama.FURenderer;
import com.faceunity.nama.utils.FuDeviceUtils;
import com.ss.bytertc.engine.data.VideoPixelFormat;
import com.ss.bytertc.engine.video.IVideoProcessor;
import com.ss.bytertc.engine.video.VideoFrame;
import com.ss.bytertc.engine.video.builder.CpuBufferVideoFrameBuilder;
import com.ss.bytertc.engine.video.builder.GLTextureVideoFrameBuilder;

import java.nio.ByteBuffer;

public class VideoProcessor extends IVideoProcessor {

    private static final String TAG = "SimpleVideoProcessor";

    // 加载本地库
    static {
        System.loadLibrary("bbnice");
    }

    // 声明本地方法
    private native boolean stabilizeI420Frame(byte[] yData, byte[] uData, byte[] vData,
                                             int width, int height,
                                             int yStride, int uStride, int vStride,
                                             boolean processLumaOnly);
    private native int stabilizeTextureFrame(int textureId, int width, int height);
    private native void resetStabilizer();



    // 是否启用视频防抖
    private boolean stabilizationEnabled = true;

    // 是否只处理亮度通道（Y通道），不处理色度通道（U和V通道）
    // 当出现色块问题时，可以设置为true
    private boolean processLumaOnly = false;

    // 防抖强度，范围0.0-1.0，0表示无防抖，1表示最强防抖
    // 当出现画面跳动时，可以降低此值
    private float stabilizationStrength = 0.5f;

    // 防抖模式
    public enum StabilizationMode {
        NONE,       // 不使用防抖
        BASIC,      // 基础防抖（原始实现）
        SIMPLE,     // 简单防抖（备选方案）
        ADVANCED    // 高级防抖（特征点匹配）
    }

    // 当前防抖模式
    private StabilizationMode stabilizationMode = StabilizationMode.SIMPLE;

    private final float[] texMatrix = new float[16];

    int mFrameCount = 0;

    public final FURenderer mFURenderer = FURenderer.getInstance();
    public boolean renderSwitch;

    private int skipFrame = 0;

    public VideoProcessor() {
        Matrix.setIdentityM(texMatrix, 0);
    }

    @Override
    public VideoFrame processVideoFrame(VideoFrame frame) {
//        Log.d(TAG, "processVideoFrame mFrameCount:" + frame.getTextureID() + ", glcontext: " + EGL14.eglGetCurrentContext());
        try {
            mFrameCount++;
            switch (frame.getPixelFormat()) {
                case I420:
                    return processI420Frame(frame);
                case TEXTURE_2D:
                    return processTextureFrame(frame);
            }
        } catch (Exception e) {
            Log.e("SimpleVideoProcessor", "processFrame Exception" + e.toString());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void onGLEnvInitiated() {
        initGL();
        mFURenderer.init();

        // 重置视频防抖状态
        if (stabilizationEnabled) {
            try {
                resetStabilizer();
                Log.d(TAG, "Video stabilizer initialized");
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize video stabilizer: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onGLEnvRelease() {
        releaseGl();
        mFURenderer.release();

        // 重置视频防抖状态
        if (stabilizationEnabled) {
            try {
                // 释放所有防抖资源
                switch (stabilizationMode) {
                    case NONE:
                        // 不需要释放资源
                        Log.d(TAG, "No stabilizer to release");
                        break;
                    case BASIC:
                        resetStabilizer();
                        Log.d(TAG, "Basic video stabilizer released");
                        break;


                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to release video stabilizer: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public void setRenderEnable(boolean enabled) {
        renderSwitch = enabled;
    }

    /**
     * 设置是否启用视频防抖
     * @param enabled 是否启用
     */
    public void setStabilizationEnabled(boolean enabled) {
        if (this.stabilizationEnabled != enabled) {
            this.stabilizationEnabled = enabled;
            if (enabled) {
                Log.d(TAG, "Video stabilization enabled");
            } else {
                Log.d(TAG, "Video stabilization disabled");
                // 重置稳定器状态
                resetStabilizer();
            }
        }
    }

    /**
     * 获取视频防抖是否启用
     * @return 是否启用
     */
    public boolean isStabilizationEnabled() {
        return stabilizationEnabled;
    }

    /**
     * 设置是否只处理亮度通道
     * 当出现色块问题时，可以设置为true，只处理亮度通道，不处理色度通道
     * @param lumaOnly 是否只处理亮度通道
     */
    public void setProcessLumaOnly(boolean lumaOnly) {
        if (this.processLumaOnly != lumaOnly) {
            this.processLumaOnly = lumaOnly;
            if (lumaOnly) {
                Log.d(TAG, "Processing luma channel only (Y channel)");
            } else {
                Log.d(TAG, "Processing all YUV channels");
            }
            // 重置稳定器状态
            resetStabilizer();
        }
    }

    /**
     * 获取是否只处理亮度通道
     * @return 是否只处理亮度通道
     */
    public boolean isProcessLumaOnly() {
        return processLumaOnly;
    }

    /**
     * 设置防抖强度
     * @param strength 防抖强度，范围0.0-1.0，0表示无防抖，1表示最强防抖
     */
    public void setStabilizationStrength(float strength) {
        // 限制范围在0.0-1.0之间
        strength = Math.max(0.0f, Math.min(strength, 1.0f));

        if (this.stabilizationStrength != strength) {
            this.stabilizationStrength = strength;
            Log.d(TAG, "Stabilization strength set to: " + strength);

            // 如果强度为0，禁用防抖
            if (strength == 0.0f) {
                setStabilizationEnabled(false);
            } else if (!stabilizationEnabled) {
                // 如果之前禁用了防抖，现在启用
                setStabilizationEnabled(true);
            } else {
                // 重置稳定器状态
                resetStabilizer();
            }
        }
    }

    /**
     * 获取当前防抖强度
     * @return 防抖强度，范围0.0-1.0
     */
    public float getStabilizationStrength() {
        return stabilizationStrength;
    }

    /**
     * 设置防抖模式
     * @param mode 防抖模式
     */
    public void setStabilizationMode(StabilizationMode mode) {
        if (this.stabilizationMode != mode) {
            StabilizationMode oldMode = this.stabilizationMode;
            this.stabilizationMode = mode;

            Log.d(TAG, "Changing stabilization mode from " + oldMode + " to " + mode);

            // 重置稳定器状态
            switch (mode) {
                case NONE:
                    stabilizationEnabled = false;
                    break;
                case BASIC:
                    stabilizationEnabled = true;
                    resetStabilizer();
                    break;


            }
        }
    }

    /**
     * 获取当前防抖模式
     * @return 防抖模式
     */
    public StabilizationMode getStabilizationMode() {
        return stabilizationMode;
    }

    public VideoFrame processI420Frame1(VideoFrame frame) {
//        LogUtil.d("SimpleVideoProcessor", "processI420Frame renderSwitch" + renderSwitch);
        if (!renderSwitch) {
            return frame;
        }

        int width = frame.getWidth();
        int height = frame.getHeight();
//        LogUtil.d("SimpleVideoProcessor", "processI420Frame textureID:" + frame.getTextureID() + ",width:" + width + ",height:" + height);
        if (skipFrame > 0) {
            skipFrame--;
            return frame;
        }
        mFURenderer.setInputOrientation(frame.getRotation().value());
        if (FUConfig.DEVICE_LEVEL > FuDeviceUtils.DEVICE_LEVEL_MID){
            //高性能设备
            mFURenderer.cheekFaceNum();
        }
        mFURenderer.setInputBufferType(FUInputBufferEnum.FU_FORMAT_I420_BUFFER);
        int texId = mFURenderer.onDrawFrameDualInput(null,
                frame.getTextureID(), frame.getWidth(),
                frame.getHeight());

        CpuBufferVideoFrameBuilder builder = new CpuBufferVideoFrameBuilder(VideoPixelFormat.I420);
        builder.setWidth(width)
                .setHeight(height)
                .setRotation(frame.getRotation()) // set rotation back if rotation has not been changed.
                .setTimeStampUs(frame.getTimeStampUs())
                .setColorSpace(frame.getColorSpace())
                .setPlaneData(0, frame.getPlaneData(0))
                .setPlaneData(1, frame.getPlaneData(1))
                .setPlaneData(2, frame.getPlaneData(2))
                .setPlaneStride(0, frame.getPlaneStride(0))
                .setPlaneStride(1, frame.getPlaneStride(1))
                .setPlaneStride(2, frame.getPlaneStride(2))
                .setReleaseCallback(() -> {
                });

        return builder.build();
    }

    public VideoFrame processI420Frame(VideoFrame frame) {
        Log.d(TAG, "processI420Frame renderSwitch: " + renderSwitch + ", stabilizationEnabled: " + stabilizationEnabled);

        // 如果不需要处理，直接返回原始帧
        if (!renderSwitch && !stabilizationEnabled) {
            return frame;
        }

        int width = frame.getWidth();
        int height = frame.getHeight();

        if (skipFrame > 0) {
            skipFrame--;
            return frame;
        }

        // 获取原始I420数据
        ByteBuffer yBuffer = frame.getPlaneData(0);
        ByteBuffer uBuffer = frame.getPlaneData(1);
        ByteBuffer vBuffer = frame.getPlaneData(2);

        int yStride = frame.getPlaneStride(0);
        int uStride = frame.getPlaneStride(1);
        int vStride = frame.getPlaneStride(2);

        // 转换为字节数组，以便传递给JNI
        byte[] yData = new byte[yBuffer.remaining()];
        byte[] uData = new byte[uBuffer.remaining()];
        byte[] vData = new byte[vBuffer.remaining()];

        yBuffer.get(yData);
        uBuffer.get(uData);
        vBuffer.get(vData);

        // 重置缓冲区位置
        yBuffer.position(0);
        uBuffer.position(0);
        vBuffer.position(0);

        // 应用视频防抖
        if (stabilizationEnabled) {
            try {
                // 记录原始位置
                int yPos = yBuffer.position();
                int uPos = uBuffer.position();
                int vPos = vBuffer.position();

                // 确保数组大小正确
                if (yData.length != yBuffer.remaining() ||
                    uData.length != uBuffer.remaining() ||
                    vData.length != vBuffer.remaining()) {

                    Log.w(TAG, "Buffer size mismatch, recreating arrays");
                    yData = new byte[yBuffer.remaining()];
                    uData = new byte[uBuffer.remaining()];
                    vData = new byte[vBuffer.remaining()];
                }

                // 获取数据
                yBuffer.get(yData);
                uBuffer.get(uData);
                vBuffer.get(vData);

                // 重置缓冲区位置以便后续操作
                yBuffer.position(yPos);
                uBuffer.position(uPos);
                vBuffer.position(vPos);

                // 根据设置选择防抖算法
                boolean stabilized = false;

                switch (stabilizationMode) {
                    case NONE:
                        // 不使用防抖
                        stabilized = false;
                        Log.d(TAG, "Stabilization disabled");
                        break;

                    case BASIC:
                        // 使用基础防抖算法
                        stabilized = stabilizeI420Frame(yData, uData, vData, width, height,
                                                      yStride, uStride, vStride, processLumaOnly);
                        Log.d(TAG, "Using basic stabilization algorithm");
                        break;


                }
                Log.d(TAG, "Frame stabilized: " + stabilized);

                if (stabilized) {
                    // 清空缓冲区
                    yBuffer.position(yPos);
                    uBuffer.position(uPos);
                    vBuffer.position(vPos);

                    // 将处理后的数据放回缓冲区
                    yBuffer.put(yData);
                    uBuffer.put(uData);
                    vBuffer.put(vData);

                    // 重置缓冲区位置
                    yBuffer.position(yPos);
                    uBuffer.position(uPos);
                    vBuffer.position(vPos);
                } else {
                    Log.w(TAG, "Frame stabilization failed, using original frame");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error stabilizing frame: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 应用美颜效果（如果启用）
        if (renderSwitch) {
            mFURenderer.setInputOrientation(frame.getRotation().value());
            if (FUConfig.DEVICE_LEVEL > FuDeviceUtils.DEVICE_LEVEL_MID){
                //高性能设备
                mFURenderer.cheekFaceNum();
            }
            mFURenderer.setInputBufferType(FUInputBufferEnum.FU_FORMAT_I420_BUFFER);
            int texId = mFURenderer.onDrawFrameDualInput(null,
                    frame.getTextureID(), width, height);
        }

        // 构建新的视频帧
        CpuBufferVideoFrameBuilder builder = new CpuBufferVideoFrameBuilder(VideoPixelFormat.I420);
        builder.setWidth(width)
                .setHeight(height)
                .setRotation(frame.getRotation())
                .setTimeStampUs(frame.getTimeStampUs())
                .setColorSpace(frame.getColorSpace())
                .setPlaneData(0, yBuffer)
                .setPlaneData(1, uBuffer)
                .setPlaneData(2, vBuffer)
                .setPlaneStride(0, yStride)
                .setPlaneStride(1, uStride)
                .setPlaneStride(2, vStride)
                .setReleaseCallback(() -> {
                    // 释放资源
                });

        return builder.build();
    }

    public synchronized VideoFrame processTextureFrame(VideoFrame frame) {
        Log.d(TAG, "processTextureFrame frame rotation: " + frame.getRotation().value());
        Log.d(TAG, "egl context: " + EGL14.eglGetCurrentContext());

        // 如果不需要处理，直接返回原始帧
        if (!renderSwitch && !stabilizationEnabled) {
            return frame;
        }

        if (skipFrame > 0) {
            skipFrame--;
            return frame;
        }

        int width = frame.getWidth();
        int height = frame.getHeight();
        int textureId = frame.getTextureID();

        // 应用视频防抖（纹理版本）
        if (stabilizationEnabled) {
            try {
                int stabilizedTexId = stabilizeTextureFrame(textureId, width, height);
                if (stabilizedTexId != 0 && stabilizedTexId != textureId) {
                    Log.d(TAG, "Texture stabilized: " + stabilizedTexId);
                    textureId = stabilizedTexId;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error stabilizing texture: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 应用美颜效果（如果启用）
        if (renderSwitch) {
            mFURenderer.setInputOrientation(frame.getRotation().value());
            if (FUConfig.DEVICE_LEVEL > FuDeviceUtils.DEVICE_LEVEL_MID){
                //高性能设备
                mFURenderer.cheekFaceNum();
            }
            mFURenderer.setInputTextureType(FUInputTextureEnum.FU_ADM_FLAG_COMMON_TEXTURE);
            int texId = mFURenderer.onDrawFrameDualInput(
                    null,
                    textureId, width, height
            );

            if (texId != 0) {
                textureId = texId;
            }
        }

        Log.d(TAG, "processTextureFrame-final texId: " + textureId);

        // 构建新的视频帧
        GLTextureVideoFrameBuilder builder = new GLTextureVideoFrameBuilder(VideoPixelFormat.TEXTURE_2D);
        builder.setEGLContext(EGL14.eglGetCurrentContext())
                .setTextureID(textureId)
                .setWidth(width)
                .setHeight(height)
                .setRotation(frame.getRotation())
                .setTimeStampUs(frame.getTimeStampUs());
        return builder.build();
    }

    void initGL() {
        Log.d(TAG, "initGL");
    }

    void releaseGl() {
        Log.d(TAG, "releaseGl");
    }
}
