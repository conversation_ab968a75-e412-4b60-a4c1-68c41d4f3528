<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_media" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\item_media.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_media_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="66" endOffset="35"/></Target><Target id="@+id/img_thumbnail" view="ImageView"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="59"/></Target><Target id="@+id/img_video_indicator" view="ImageView"><Expressions/><location startLine="24" startOffset="8" endLine="34" endOffset="40"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="52" endOffset="40"/></Target><Target id="@+id/checkbox" view="CheckBox"><Expressions/><location startLine="54" startOffset="8" endLine="63" endOffset="40"/></Target></Targets></Layout>