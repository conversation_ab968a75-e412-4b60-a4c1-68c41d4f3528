package com.srthinker.bbnice.common;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.lifecycle.Observer;

import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.repository.AIRepository;
import com.srthinker.bbnice.core.Result;
import com.ss.bytertc.engine.RTCRoom;
import com.ss.bytertc.engine.RTCRoomConfig;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.type.ChannelProfile;

public class RtcRoomHelper {

    private static final String TAG = "RtcRoomHelper";

    /**
     * 加入房间
     * @param rtcRoom 房间类实例
     * @param roomMessage 房间信息
     * @param autoSubscribeVideo 是否自动上传视频流
     * @param onFinish 加入房间后的的配置
     */
    public static void joinRoom(RTCRoom rtcRoom, RoomMessage roomMessage, boolean autoSubscribeVideo, Runnable onFinish) {

        AIServerUtils.getInstance().getToken(roomMessage, (getTokenSuccess, token, e) -> {
            if (getTokenSuccess) {
                roomMessage.token = token; // 更新token
                // 用户信息
                UserInfo userInfo = new UserInfo(roomMessage.userID, "");

                // 设置房间配置
                RTCRoomConfig roomConfig = new RTCRoomConfig(
                        ChannelProfile.CHANNEL_PROFILE_CHAT_ROOM,
                        true,
                        true,
                        autoSubscribeVideo
                );

                // 加入房间
                Log.d(TAG, "join room: " + rtcRoom.joinRoom(roomMessage.token, userInfo, roomConfig));
                AIServerUtils.getInstance().startAIAgent(roomMessage, (agentSuccess, taskId, ee) -> {
                    if (agentSuccess) {
                        roomMessage.taskID = taskId;
                        if (onFinish != null) {
                            onFinish.run();
                        }
                    }
                });
            }
        });
    }

    /**
     * 离开房间
     */
    public static void leaveRoom(RTCRoom rtcRoom, RoomMessage roomMessage) {
        if (rtcRoom != null) {
            rtcRoom.leaveRoom();
//            rtcRoom.destroy();
        }

        if (!TextUtils.isEmpty(roomMessage.taskID)) {
            AIServerUtils.getInstance().stopAIAgent(roomMessage);
        }
    }
}
