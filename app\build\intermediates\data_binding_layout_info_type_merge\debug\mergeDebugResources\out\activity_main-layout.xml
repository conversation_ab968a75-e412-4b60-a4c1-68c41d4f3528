<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.appcompat.widget.LinearLayoutCompat" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_main_0" view="androidx.appcompat.widget.LinearLayoutCompat"><Expressions/><location startLine="1" startOffset="0" endLine="65" endOffset="46"/></Target><Target id="@+id/btn_join_room" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="26" startOffset="12" endLine="31" endOffset="37"/></Target><Target id="@+id/btn_leave_room" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="33" startOffset="12" endLine="38" endOffset="37"/></Target><Target id="@+id/btn_start_chat" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="41" startOffset="8" endLine="45" endOffset="33"/></Target><Target id="@+id/btn_open_config" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="48" startOffset="8" endLine="52" endOffset="35"/></Target><Target id="@+id/btn_Change_Cam" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="55" startOffset="8" endLine="60" endOffset="34"/></Target></Targets></Layout>