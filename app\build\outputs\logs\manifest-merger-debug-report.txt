-- Merging decision tree log ---
manifest
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:2:1-144:12
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:2:1-144:12
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:2:1-144:12
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:2:1-144:12
MERGED from [androidx.databinding:viewbinding:8.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\df5f783d2a1a46af4974d42e8ee7d4cb\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ca3781b308d8b1267a86f75849421ed3\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f4a47d972c2507a33d2f0b760a83359b\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3e15530517cd3854e5544a89cd1b21c5\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\159d8e172c6b8737be1ec453195ecd5f\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\131f10619d340c0fef3711908f5a0bff\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] D:\Workspace\env\.gradle\caches\8.11.1\transforms\01403a99e1df4679ec1aac2779350aa3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:2:1-24:12
MERGED from [:faceunity] D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e325349e884b5ea75a49073ffb4035fe\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\73dd5e457f7b72c376df272f3861a0da\transformed\jetified-PhotoView-2.3.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\b2f4bc71ff812dc3565aaa91b5205c96\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\bfc74f1504facdc13ec82146aa98274a\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\37e0ccb331e0c689c674afc74208513c\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cfe606863be6628a58e31612274ada30\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2df9f7e105cb6abce8340028cb9ee7c6\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\14ccabe68581088a5d815f008811447d\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\b009f8d756e54c2a9ae3f987daafebba\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43313233f3fa37e54a614c2ed9dd880c\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\89d718e7cf8db33996898bae2869b5c7\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\00729341e56d5fb752aed46b23cff532\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-video:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\57cc230418486263960fe3dd31752a6b\transformed\jetified-camera-video-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\529f8c652fba9d27c07a98ccf8e8e199\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\51ca02bf4b0b6806c6204cadef53ed54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\0a1f3abd4b07dbc45d958d160529251b\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\76551bcb48173368096312a15ced360d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e268761b887127885040fe7a57aa76a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\372b94e2f165266282b355d0ef9f151e\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\781764e7f42549a37828d9c91b00adc5\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\bfaccddd53c23592cc6ee4149b4b3e43\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f6e3eff3c2573ede675c31f59255556b\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cfffa15b2e719e58d8052ceb15dbe9f2\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2afec29b4dcd84e918b470821fe0a0ad\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\52ee08455723093da0f94a4aa7fe1e76\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f65b3f3088283a3d569cfc42cc11fe03\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2e0c243ad887c9f9d431abb86d6e011f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2d4ee157c5802106b5ec1470e3b152b6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c09b88c93c2575ed5f917b00c2a09406\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\51de524e27d5e27b014c8533cfcaf41d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\7eee88f15e06fac0ffa8b655cfd4d573\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5e8191696405cc9c375b58c4cee375f0\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\fab4aa23da939511e1eeabee402ded92\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e39426db19b361e0ecaa9b3660ae99ad\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.4.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cf6520603f1bcb1e7f36932191897d1f\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.loader:loader:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3731ed96dc0792e0e6c54705fb2b9427\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\7d88f60975d8d55956a59220f3439263\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3ed41f61c8079e756f70962bcd23572a\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\dffee418e6ae2809d9f4829deb3fa43f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\0dd3eda6980c03b60390b9d956b36751\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f7ca035c54ab799a8cbfe77297a4b3a7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\885bc80d8e2c2ef098befb86a297ec1b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d3d0b63415a9a643f0d32dc54968f270\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6a85f9415518272b5f90bc4ec29b984c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a20276034dd96e2cf159b9175d4777d\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d31e0344da2fc3d66fc7a90b793a2208\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d43e9877297680a4ff8f7b442779b3e3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5b9a2b0d03d9d2d53d2ea399b5870d1e\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\89f9fe97df93af82d130e447bc59ba75\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6d33cd6c3aba1ace7bd4581e2809ba2d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6562b635e1b6b4a166133a445324caba\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:2:1-16:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e338997678fd33b4cceeb13b33cbf394\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.huawei.multimedia:audiokit:1.0.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\342fbf5dd0e83fd1b1655bc9dd4c3837\transformed\jetified-audiokit-1.0.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.faceunity:model:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.11.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\360eb2d887cb42b1d60e6ee9245b044b\transformed\jetified-exoplayer-core-2.11.3\AndroidManifest.xml:17:1-26:12
	package
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:4:5-35
		INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:10:5-71
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:10:5-71
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
uses-permission#android.permission.INTERNET
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:9:5-67
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:9:5-67
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:12:5-67
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:12:5-67
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
uses-permission#android.permission.CAMERA
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:11:5-65
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:11:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:11:5-65
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:11:5-65
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
uses-permission#android.permission.CALL_PHONE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:13:5-79
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:13:5-79
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.11.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\360eb2d887cb42b1d60e6ee9245b044b\transformed\jetified-exoplayer-core-2.11.3\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.11.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\360eb2d887cb42b1d60e6ee9245b044b\transformed\jetified-exoplayer-core-2.11.3\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
uses-permission#android.permission.READ_PHONE_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:18:5-75
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:18:5-75
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-77
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:19:5-77
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:19:5-77
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-79
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-85
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-82
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-80
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:14:5-80
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:14:5-80
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-81
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:15:5-81
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:15:5-81
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:13:5-81
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:13:5-81
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-76
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:5-75
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:22-72
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:5-76
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:17:5-76
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:17:5-76
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-79
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:14:5-79
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:14:5-79
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-76
uses-permission#android.permission.NEARBY_WIFI_DEVICES
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-145
	android:usesPermissionFlags
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:76-122
	tools:targetApi
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:123-142
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:22-75
uses-permission#android.permission.BLUETOOTH
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-68
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:16:5-68
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:16:5-68
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:5-74
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:5-76
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:5-73
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:22-70
uses-permission#android.permission.MODIFY_PHONE_STATE
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-77
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-74
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-80
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:12:5-80
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:12:5-80
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-77
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-85
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-82
uses-permission#android.permission.WRITE_SETTINGS
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-79
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-76
application
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-142:19
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-142:19
MERGED from [com.google.android.material:material:1.10.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\131f10619d340c0fef3711908f5a0bff\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\131f10619d340c0fef3711908f5a0bff\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] D:\Workspace\env\.gradle\caches\8.11.1\transforms\01403a99e1df4679ec1aac2779350aa3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] D:\Workspace\env\.gradle\caches\8.11.1\transforms\01403a99e1df4679ec1aac2779350aa3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:22:5-20
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:22:5-20
MERGED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6a85f9415518272b5f90bc4ec29b984c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6a85f9415518272b5f90bc4ec29b984c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-35
	android:label
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:43:9-41
	android:roundIcon
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:9-54
	android:icon
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:9-43
	android:allowBackup
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:41:9-35
	android:theme
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-59
	android:usesCleartextTraffic
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-44
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:9-28
activity#com.srthinker.bbnice.setting.PrivateActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:13-52
activity#com.srthinker.bbnice.setting.AboutActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-53:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:13-50
activity#com.srthinker.bbnice.setting.VoiceActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:9-56:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:13-50
activity#com.srthinker.bbnice.setting.DisplayActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:9-59:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:13-52
activity#com.srthinker.bbnice.setting.LocationActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:9-62:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:13-53
activity#com.srthinker.bbnice.setting.BluetoothActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:9-65:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:13-54
activity#com.srthinker.bbnice.setting.MobileNetworkActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:9-68:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:13-58
activity#com.srthinker.bbnice.setting.WifiActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:9-71:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:13-49
activity#com.srthinker.bbnice.setting.SettingActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:9-74:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:13-52
activity#com.srthinker.bbnice.call.CallActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:9-77:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:13-46
activity#com.srthinker.bbnice.capture.CaptureActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:9-80:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:13-52
activity#com.srthinker.bbnice.recognition.RecognitionActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:9-83:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:13-60
activity#com.srthinker.bbnice.learn.LearnActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:9-86:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:13-48
activity#com.srthinker.bbnice.home.HomeActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:9-95:20
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:13-46
activity#com.srthinker.bbnice.chat.ChatActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:9-98:40
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:13-46
activity#com.srthinker.bbnice.SplashActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:99:9-108:20
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:101:13-36
	android:theme
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:102:13-63
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:100:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:103:13-107:29
action#android.intent.action.MAIN
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:17-69
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:17-77
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:27-74
activity#com.srthinker.bbnice.registration.DeviceRegistrationActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:9-112:48
	android:label
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:13-45
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-68
activity#com.srthinker.bbnice.setting.LanguageSettingsActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:9-116:57
	android:label
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:13-54
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:115:13-37
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:13-61
service#org.eclipse.paho.android.service.MqttService
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:9-80
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:18-77
service#com.srthinker.bbnice.mqtt.MqttService
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:9-120:38
	android:enabled
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:120:13-35
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:119:13-45
service#com.srthinker.bbnice.location.LocationService
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:9-125:58
	android:enabled
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:13-35
	tools:ignore
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:13-55
	android:foregroundServiceType
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:13-53
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-53
activity#com.srthinker.bbnice.test.RepositoryTestActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:9-136:20
	android:label
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:13-41
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:13-36
	android:theme
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-71
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:13-56
activity#com.srthinker.bbnice.gallery.GalleryActivity
ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:137:9-141:66
	android:label
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:140:13-44
	android:exported
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:139:13-37
	android:theme
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:141:13-63
	android:name
		ADDED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:138:13-52
uses-sdk
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\df5f783d2a1a46af4974d42e8ee7d4cb\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\df5f783d2a1a46af4974d42e8ee7d4cb\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ca3781b308d8b1267a86f75849421ed3\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ca3781b308d8b1267a86f75849421ed3\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f4a47d972c2507a33d2f0b760a83359b\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f4a47d972c2507a33d2f0b760a83359b\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3e15530517cd3854e5544a89cd1b21c5\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3e15530517cd3854e5544a89cd1b21c5\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\159d8e172c6b8737be1ec453195ecd5f\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] D:\Workspace\env\.gradle\caches\8.11.1\transforms\159d8e172c6b8737be1ec453195ecd5f\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\131f10619d340c0fef3711908f5a0bff\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\131f10619d340c0fef3711908f5a0bff\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] D:\Workspace\env\.gradle\caches\8.11.1\transforms\01403a99e1df4679ec1aac2779350aa3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] D:\Workspace\env\.gradle\caches\8.11.1\transforms\01403a99e1df4679ec1aac2779350aa3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:5:5-7:41
MERGED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:5:5-7:41
MERGED from [:faceunity] D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:faceunity] D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e325349e884b5ea75a49073ffb4035fe\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e325349e884b5ea75a49073ffb4035fe\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\73dd5e457f7b72c376df272f3861a0da\transformed\jetified-PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\73dd5e457f7b72c376df272f3861a0da\transformed\jetified-PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\b2f4bc71ff812dc3565aaa91b5205c96\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\b2f4bc71ff812dc3565aaa91b5205c96\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\bfc74f1504facdc13ec82146aa98274a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\bfc74f1504facdc13ec82146aa98274a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\37e0ccb331e0c689c674afc74208513c\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\37e0ccb331e0c689c674afc74208513c\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cfe606863be6628a58e31612274ada30\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cfe606863be6628a58e31612274ada30\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2df9f7e105cb6abce8340028cb9ee7c6\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2df9f7e105cb6abce8340028cb9ee7c6\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\14ccabe68581088a5d815f008811447d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\14ccabe68581088a5d815f008811447d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\b009f8d756e54c2a9ae3f987daafebba\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\b009f8d756e54c2a9ae3f987daafebba\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43313233f3fa37e54a614c2ed9dd880c\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43313233f3fa37e54a614c2ed9dd880c\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\89d718e7cf8db33996898bae2869b5c7\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\89d718e7cf8db33996898bae2869b5c7\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\00729341e56d5fb752aed46b23cff532\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\00729341e56d5fb752aed46b23cff532\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\57cc230418486263960fe3dd31752a6b\transformed\jetified-camera-video-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\57cc230418486263960fe3dd31752a6b\transformed\jetified-camera-video-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\529f8c652fba9d27c07a98ccf8e8e199\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\529f8c652fba9d27c07a98ccf8e8e199\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\51ca02bf4b0b6806c6204cadef53ed54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\51ca02bf4b0b6806c6204cadef53ed54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\0a1f3abd4b07dbc45d958d160529251b\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\0a1f3abd4b07dbc45d958d160529251b\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\76551bcb48173368096312a15ced360d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\76551bcb48173368096312a15ced360d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e268761b887127885040fe7a57aa76a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e268761b887127885040fe7a57aa76a\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\372b94e2f165266282b355d0ef9f151e\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\372b94e2f165266282b355d0ef9f151e\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\781764e7f42549a37828d9c91b00adc5\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\781764e7f42549a37828d9c91b00adc5\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\bfaccddd53c23592cc6ee4149b4b3e43\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\bfaccddd53c23592cc6ee4149b4b3e43\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f6e3eff3c2573ede675c31f59255556b\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f6e3eff3c2573ede675c31f59255556b\transformed\jetified-lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cfffa15b2e719e58d8052ceb15dbe9f2\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cfffa15b2e719e58d8052ceb15dbe9f2\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2afec29b4dcd84e918b470821fe0a0ad\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2afec29b4dcd84e918b470821fe0a0ad\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\52ee08455723093da0f94a4aa7fe1e76\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\52ee08455723093da0f94a4aa7fe1e76\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f65b3f3088283a3d569cfc42cc11fe03\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f65b3f3088283a3d569cfc42cc11fe03\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2e0c243ad887c9f9d431abb86d6e011f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2e0c243ad887c9f9d431abb86d6e011f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2d4ee157c5802106b5ec1470e3b152b6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\2d4ee157c5802106b5ec1470e3b152b6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c09b88c93c2575ed5f917b00c2a09406\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c09b88c93c2575ed5f917b00c2a09406\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\51de524e27d5e27b014c8533cfcaf41d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\51de524e27d5e27b014c8533cfcaf41d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\7eee88f15e06fac0ffa8b655cfd4d573\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\7eee88f15e06fac0ffa8b655cfd4d573\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5e8191696405cc9c375b58c4cee375f0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5e8191696405cc9c375b58c4cee375f0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\fab4aa23da939511e1eeabee402ded92\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\fab4aa23da939511e1eeabee402ded92\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e39426db19b361e0ecaa9b3660ae99ad\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e39426db19b361e0ecaa9b3660ae99ad\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.4.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cf6520603f1bcb1e7f36932191897d1f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cf6520603f1bcb1e7f36932191897d1f\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3731ed96dc0792e0e6c54705fb2b9427\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3731ed96dc0792e0e6c54705fb2b9427\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\7d88f60975d8d55956a59220f3439263\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\7d88f60975d8d55956a59220f3439263\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3ed41f61c8079e756f70962bcd23572a\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3ed41f61c8079e756f70962bcd23572a\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\dffee418e6ae2809d9f4829deb3fa43f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\dffee418e6ae2809d9f4829deb3fa43f\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\0dd3eda6980c03b60390b9d956b36751\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\0dd3eda6980c03b60390b9d956b36751\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f7ca035c54ab799a8cbfe77297a4b3a7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\f7ca035c54ab799a8cbfe77297a4b3a7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\885bc80d8e2c2ef098befb86a297ec1b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\885bc80d8e2c2ef098befb86a297ec1b\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d3d0b63415a9a643f0d32dc54968f270\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d3d0b63415a9a643f0d32dc54968f270\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6a85f9415518272b5f90bc4ec29b984c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6a85f9415518272b5f90bc4ec29b984c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a20276034dd96e2cf159b9175d4777d\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a20276034dd96e2cf159b9175d4777d\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d31e0344da2fc3d66fc7a90b793a2208\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d31e0344da2fc3d66fc7a90b793a2208\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d43e9877297680a4ff8f7b442779b3e3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d43e9877297680a4ff8f7b442779b3e3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5b9a2b0d03d9d2d53d2ea399b5870d1e\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5b9a2b0d03d9d2d53d2ea399b5870d1e\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\89f9fe97df93af82d130e447bc59ba75\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\89f9fe97df93af82d130e447bc59ba75\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6d33cd6c3aba1ace7bd4581e2809ba2d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6d33cd6c3aba1ace7bd4581e2809ba2d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6562b635e1b6b4a166133a445324caba\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6562b635e1b6b4a166133a445324caba\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.faceunity:core:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ec077ccf08d73405be66a9d2be2b8504\transformed\jetified-core-8.7.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e338997678fd33b4cceeb13b33cbf394\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e338997678fd33b4cceeb13b33cbf394\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.huawei.multimedia:audiokit:1.0.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\342fbf5dd0e83fd1b1655bc9dd4c3837\transformed\jetified-audiokit-1.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.huawei.multimedia:audiokit:1.0.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\342fbf5dd0e83fd1b1655bc9dd4c3837\transformed\jetified-audiokit-1.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.faceunity:model:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.faceunity:model:8.7.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.11.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\360eb2d887cb42b1d60e6ee9245b044b\transformed\jetified-exoplayer-core-2.11.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.11.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\360eb2d887cb42b1d60e6ee9245b044b\transformed\jetified-exoplayer-core-2.11.3\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
	android:name
		ADDED from [com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\c144f4e1f623f1cf84ea9b68bbfb265b\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
uses-feature#android.hardware.camera
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ace91a5abcd4e1516442197c72bf0655\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
