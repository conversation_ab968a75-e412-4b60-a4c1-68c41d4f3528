<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">BBNice</string>
    <string name="error_network">Network Error</string>
    <string name="error_unknown">Unknown Error</string>

    <!-- Common -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Confirm</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="loading">Loading…</string>
    <string name="retry">Retry</string>
    <string name="settings">Settings</string>
    <string name="apply">Apply</string>

    <!-- Language -->
    <string name="language_settings">Language Settings</string>
    <string name="system_language">System Language</string>
    <string name="english">English</string>
    <string name="chinese">Chinese</string>
    <string name="language_changed">Language changed</string>
    <string name="restart_app_message">Please restart the app for the changes to take effect</string>

    <!-- Login and Registration -->
    <string name="login">Login</string>
    <string name="register">Register</string>
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="qr_code_expired">QR code has expired</string>
    <string name="qr_code_valid_for">Valid for %1$d minutes</string>
    <string name="device_id">Device ID</string>
    <string name="bind_success">Device bound successfully</string>
    <string name="bind_failed">Failed to bind device</string>
    <string name="unbind_device">Unbind Device</string>
    <string name="unbind_confirm">Are you sure you want to unbind this device?</string>
    <string name="unbind_success">Device unbound successfully</string>
    <string name="unbind_failed">Failed to unbind device</string>

    <!-- Home -->
    <string name="home">Home</string>
    <string name="chat">Chat</string>
    <string name="chat_cn">Chinese Chat</string>
    <string name="chat_en">English Chat</string>
    <string name="api_test">API Test</string>
    <string name="device_info">Device Info</string>
    <string name="battery_level">Battery Level</string>
    <string name="signal_strength">Signal Strength</string>
    <string name="network_status">Network Status</string>
    <string name="last_updated">Last Updated: %1$s</string>

    <!-- Chat -->
    <string name="press_to_talk">Press and hold to talk</string>
    <string name="release_to_stop">Release to stop</string>
    <string name="no_more_history">No more history</string>
    <string name="chat_history">Chat History</string>
    <string name="clear_history">Clear History</string>
    <string name="clear_history_confirm">Are you sure you want to clear all chat history?</string>
    <string name="history_cleared">Chat history cleared</string>
    <string name="upload_history">Upload History</string>
    <string name="upload_success">History uploaded successfully</string>
    <string name="upload_failed">Failed to upload history</string>

    <!-- Location -->
    <string name="location">Location</string>
    <string name="current_location">Current Location</string>
    <string name="location_permission_required">Location permission is required</string>
    <string name="location_settings">Location Settings</string>
    <string name="location_not_available">Location not available</string>
    <string name="location_updated">Location updated</string>

    <!-- Permissions -->
    <string name="permission_required">Permission Required</string>
    <string name="permission_denied">Permission Denied</string>
    <string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="camera">Camera</string>
    <string name="microphone">Microphone</string>
    <string name="phone_state">Phone State</string>

    <!-- Gallery -->
    <string name="gallery">Gallery</string>
    <string name="photos">Photos</string>
    <string name="videos">Videos</string>
    <string name="all_media">All Media</string>
    <string name="select_all">Select All</string>
    <string name="deselect_all">Deselect All</string>
    <string name="delete_selected">Delete Selected</string>
    <string name="delete_confirm">Are you sure you want to delete the selected items?</string>
    <string name="no_media_found">No media files found</string>
    <string name="storage_permission_required">Storage permission is required to access media files</string>
    <string name="play">Play</string>
    <string name="pause">Pause</string>
    <string name="loading_media">Loading media…</string>

    <!-- Errors -->
    <string name="error_connection">Connection error</string>
    <string name="error_timeout">Request timed out</string>
    <string name="error_server">Server error</string>
    <string name="error_authentication">Authentication error</string>
    <string name="error_invalid_input">Invalid input</string>
    <string name="error_device_not_found">Device not found</string>
    <string name="error_already_bound">Device already bound</string>
    <string name="error_not_bound">Device not bound</string>

    <!-- WiFi -->
    <string name="wifi_settings">WiFi Settings</string>
    <string name="wifi">WiFi</string>
    <string name="available_networks">Available Networks</string>
    <string name="no_wifi_networks">No WiFi networks available</string>
    <string name="scanning_wifi">Scanning for WiFi networks…</string>
    <string name="scan_failed">Failed to scan WiFi networks</string>
    <string name="wifi_connected">Connected</string>
    <string name="wifi_disconnected">Not connected</string>
    <string name="wifi_connecting">Connecting to %1$s</string>
    <string name="wifi_connection_failed">Failed to connect to WiFi</string>
    <string name="wifi_password">Password</string>
    <string name="wifi_show_password">Show password</string>
    <string name="wifi_connect">Connect</string>
    <string name="wifi_enter_password">Enter WiFi Password</string>
    <string name="wifi_password_empty">Please enter a password</string>
    <string name="wifi_already_connected">Already connected to this network</string>
    <string name="location_required">Location permission is required to scan WiFi networks</string>
    <string name="location_service_required">Location service must be enabled to scan WiFi networks on Android 8.0+</string>
    <string name="enable_location">Enable Location</string>

    <!-- Location -->
    <string name="location_switch">Location</string>

    <!-- Bluetooth -->
    <string name="bluetooth_settings">Bluetooth Settings</string>
    <string name="bluetooth">Bluetooth</string>
    <string name="available_devices">Available Devices</string>
    <string name="paired_devices">Paired Devices</string>
    <string name="no_bluetooth_devices">No Bluetooth devices available</string>
    <string name="scanning_bluetooth">Scanning for Bluetooth devices…</string>
    <string name="bluetooth_scan_failed">Failed to scan Bluetooth devices</string>
    <string name="bluetooth_connected">Connected</string>
    <string name="bluetooth_disconnected">Not connected</string>
    <string name="bluetooth_paired">Paired</string>
    <string name="bluetooth_pairing">Pairing with %1$s</string>
    <string name="bluetooth_connecting">Connecting to %1$s</string>
    <string name="bluetooth_connection_failed">Failed to connect to device</string>
    <string name="bluetooth_pairing_failed">Failed to pair with device</string>
    <string name="bluetooth_pair">Pair</string>
    <string name="bluetooth_unpair">Unpair</string>
    <string name="bluetooth_connect">Connect</string>
    <string name="bluetooth_disconnect">Disconnect</string>
    <string name="bluetooth_enter_pin">Enter PIN Code</string>
    <string name="bluetooth_pin_empty">Please enter a PIN code</string>
    <string name="bluetooth_already_connected">Already connected to this device</string>
    <string name="bluetooth_not_supported">Bluetooth is not supported on this device</string>
    <string name="bluetooth_permission_required">Bluetooth permission is required</string>
    <string name="enable_bluetooth">Enable Bluetooth</string>
    <string name="bluetooth_device_name">Device Name: %1$s</string>
    <string name="bluetooth_device_address">Device Address: %1$s</string>
    <string name="bluetooth_device_type">Device Type: %1$s</string>

</resources>