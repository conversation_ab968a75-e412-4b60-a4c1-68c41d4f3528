<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">BBNice</string>
    <string name="error_network">Network Error</string>
    <string name="error_unknown">Unknown Error</string>

    <!-- Common -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Confirm</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="loading">Loading…</string>
    <string name="retry">Retry</string>
    <string name="refresh">Refresh</string>
    <string name="settings">Settings</string>
    <string name="apply">Apply</string>
    <string name="unknown">Unknown</string>

    <!-- Language -->
    <string name="language_settings">Language Settings</string>
    <string name="system_language">System Language</string>
    <string name="english">English</string>
    <string name="chinese">Chinese</string>
    <string name="language_changed">Language changed</string>
    <string name="restart_app_message">Please restart the app for the changes to take effect</string>

    <!-- Login and Registration -->
    <string name="login">Login</string>
    <string name="register">Register</string>
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="qr_code_expired">QR code has expired</string>
    <string name="qr_code_valid_for">Valid for %1$d minutes</string>
    <string name="device_id">Device ID</string>
    <string name="bind_success">Device bound successfully</string>
    <string name="bind_failed">Failed to bind device</string>
    <string name="unbind_device">Unbind Device</string>
    <string name="unbind_confirm">Are you sure you want to unbind this device?</string>
    <string name="unbind_success">Device unbound successfully</string>
    <string name="unbind_failed">Failed to unbind device</string>

    <!-- Home -->
    <string name="home">Home</string>
    <string name="chat">Chat</string>
    <string name="chat_cn">Chinese Chat</string>
    <string name="chat_en">English Chat</string>
    <string name="api_test">API Test</string>
    <string name="device_info">Device Info</string>
    <string name="battery_level">Battery Level</string>
    <string name="signal_strength">Signal Strength</string>
    <string name="network_status">Network Status</string>
    <string name="last_updated">Last Updated: %1$s</string>

    <!-- Chat -->
    <string name="press_to_talk">Press and hold to talk</string>
    <string name="release_to_stop">Release to stop</string>
    <string name="no_more_history">No more history</string>
    <string name="chat_history">Chat History</string>
    <string name="clear_history">Clear History</string>
    <string name="clear_history_confirm">Are you sure you want to clear all chat history?</string>
    <string name="history_cleared">Chat history cleared</string>
    <string name="upload_history">Upload History</string>
    <string name="upload_success">History uploaded successfully</string>
    <string name="upload_failed">Failed to upload history</string>

    <!-- Location -->
    <string name="location">Location</string>
    <string name="current_location">Current Location</string>
    <string name="location_permission_required">Location permission is required</string>
    <string name="location_settings">Location Settings</string>
    <string name="location_not_available">Location not available</string>
    <string name="location_updated">Location updated</string>

    <!-- Permissions -->
    <string name="permission_required">Permission Required</string>
    <string name="permission_denied">Permission Denied</string>
    <string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="camera">Camera</string>
    <string name="microphone">Microphone</string>
    <string name="phone_state">Phone State</string>
    <string name="storage">Storage</string>
    <string name="network">Network</string>

    <!-- Gallery -->
    <string name="gallery">Gallery</string>
    <string name="photos">Photos</string>
    <string name="videos">Videos</string>
    <string name="all_media">All Media</string>
    <string name="select_all">Select All</string>
    <string name="deselect_all">Deselect All</string>
    <string name="delete_selected">Delete Selected</string>
    <string name="delete_confirm">Are you sure you want to delete the selected items?</string>
    <string name="no_media_found">No media files found</string>
    <string name="storage_permission_required">Storage permission is required to access media files</string>
    <string name="play">Play</string>
    <string name="pause">Pause</string>
    <string name="loading_media">Loading media…</string>

    <!-- Errors -->
    <string name="error_connection">Connection error</string>
    <string name="error_timeout">Request timed out</string>
    <string name="error_server">Server error</string>
    <string name="error_authentication">Authentication error</string>
    <string name="error_invalid_input">Invalid input</string>
    <string name="error_device_not_found">Device not found</string>
    <string name="error_already_bound">Device already bound</string>
    <string name="error_not_bound">Device not bound</string>

    <!-- WiFi -->
    <string name="wifi_settings">WiFi Settings</string>
    <string name="wifi">WiFi</string>
    <string name="available_networks">Available Networks</string>
    <string name="no_wifi_networks">No WiFi networks available</string>
    <string name="scanning_wifi">Scanning for WiFi networks…</string>
    <string name="scan_failed">Failed to scan WiFi networks</string>
    <string name="wifi_connected">Connected</string>
    <string name="wifi_disconnected">Not connected</string>
    <string name="wifi_connecting">Connecting to %1$s</string>
    <string name="wifi_connection_failed">Failed to connect to WiFi</string>
    <string name="wifi_password">Password</string>
    <string name="wifi_show_password">Show password</string>
    <string name="wifi_connect">Connect</string>
    <string name="wifi_enter_password">Enter WiFi Password</string>
    <string name="wifi_password_empty">Please enter a password</string>
    <string name="wifi_already_connected">Already connected to this network</string>
    <string name="location_required">Location permission is required to scan WiFi networks</string>
    <string name="location_service_required">Location service must be enabled to scan WiFi networks on Android 8.0+</string>
    <string name="enable_location">Enable Location</string>

    <!-- Location -->
    <string name="location_switch">Location</string>

    <!-- Displa -->
    <string name="display">Display</string>
    <string name="screen_luminance">Screen Luminance</string>
    <string name="screen_wake_up">Screen Wake Up</string>
    <string name="wake_up_duration">Wake Up Duration</string>
    <string name="second">secs</string>

    <!-- Voice -->
    <string name="voice_settings">Voice Settings</string>
    <string name="voice">Voice</string>
    <string name="volume_control">Volume Control</string>
    <string name="mute_mode">Mute Mode</string>
    <string name="mute_enabled">Mute mode is enabled</string>
    <string name="mute_disabled">Mute mode is disabled</string>
    <string name="volume_level">Volume Level: %1$d%%</string>
    <string name="media_volume">Media Volume</string>
    <string name="call_volume">Call Volume</string>
    <string name="ring_volume">Ring Volume</string>
    <string name="alarm_volume">Alarm Volume</string>
    <string name="notification_volume">Notification Volume</string>
    <string name="system_volume">System Volume</string>
    <string name="volume_up">Volume Up</string>
    <string name="volume_down">Volume Down</string>
    <string name="enable_mute">Enable Mute</string>
    <string name="disable_mute">Disable Mute</string>
    <string name="audio_permission_required">Audio permission is required to control volume</string>
    <string name="do_not_disturb_permission_required">Do Not Disturb permission is required to control mute mode</string>
    <string name="go_to_notification_settings">Go to Notification Settings</string>

    <!-- Bluetooth -->
    <string name="bluetooth_settings">Bluetooth Settings</string>
    <string name="bluetooth">Bluetooth</string>
    <string name="available_devices">Available Devices</string>
    <string name="paired_devices">Paired Devices</string>
    <string name="no_bluetooth_devices">No Bluetooth devices available</string>
    <string name="scanning_bluetooth">Scanning for Bluetooth devices…</string>
    <string name="bluetooth_scan_failed">Failed to scan Bluetooth devices</string>
    <string name="bluetooth_connected">Connected</string>
    <string name="bluetooth_disconnected">Not connected</string>
    <string name="bluetooth_paired">Paired</string>
    <string name="bluetooth_pairing">Pairing with %1$s</string>
    <string name="bluetooth_connecting">Connecting to %1$s</string>
    <string name="bluetooth_connection_failed">Failed to connect to device</string>
    <string name="bluetooth_pairing_failed">Failed to pair with device</string>
    <string name="bluetooth_pair">Pair</string>
    <string name="bluetooth_unpair">Unpair</string>
    <string name="bluetooth_connect">Connect</string>
    <string name="bluetooth_disconnect">Disconnect</string>
    <string name="bluetooth_enter_pin">Enter PIN Code</string>
    <string name="bluetooth_pin_empty">Please enter a PIN code</string>
    <string name="bluetooth_already_connected">Already connected to this device</string>
    <string name="bluetooth_not_supported">Bluetooth is not supported on this device</string>
    <string name="bluetooth_permission_required">Bluetooth permission is required</string>
    <string name="enable_bluetooth">Enable Bluetooth</string>
    <string name="bluetooth_device_name">Device Name: %1$s</string>
    <string name="bluetooth_device_address">Device Address: %1$s</string>
    <string name="bluetooth_device_type">Device Type: %1$s</string>

    <!-- Bluetooth Helper Error Messages -->
    <string name="bluetooth_disabled">Bluetooth is disabled</string>
    <string name="bluetooth_permissions_not_granted">Bluetooth permissions not granted</string>
    <string name="bluetooth_scan_start_failed">Failed to start Bluetooth scan</string>
    <string name="bluetooth_error_pairing">Error pairing device: %1$s</string>
    <string name="bluetooth_error_unpairing">Error unpairing device: %1$s</string>
    <string name="bluetooth_error_connecting">Failed to connect to device: %1$s</string>
    <string name="bluetooth_error_disconnecting">Failed to disconnect device: %1$s</string>
    <string name="bluetooth_initiate_pairing_failed">Failed to initiate pairing with device</string>
    <string name="bluetooth_unpair_failed">Failed to unpair device</string>
    <string name="bluetooth_gatt_not_supported">GATT is not supported on current Android version</string>
    <string name="bluetooth_a2dp_profile_unavailable">A2DP profile is not available</string>
    <string name="bluetooth_headset_profile_unavailable">HEADSET profile is not available</string>
    <string name="bluetooth_a2dp_connect_system_settings">A2DP connection needs to be completed in system Bluetooth settings. Please connect this device in system settings.</string>
    <string name="bluetooth_headset_connect_system_settings">HEADSET connection needs to be completed in system Bluetooth settings. Please connect this device in system settings.</string>
    <string name="bluetooth_a2dp_disconnect_system_settings">A2DP disconnection needs to be completed in system Bluetooth settings. Please disconnect this device in system settings.</string>
    <string name="bluetooth_headset_disconnect_system_settings">HEADSET disconnection needs to be completed in system Bluetooth settings. Please disconnect this device in system settings.</string>
    <string name="bluetooth_error_get_device_uuid">Failed to get device UUID: %1$s</string>
    <string name="bluetooth_error_socket_close">Failed to close socket: %1$s</string>
    <string name="bluetooth_error_a2dp_devices">Error getting A2DP connected devices: %1$s</string>
    <string name="bluetooth_error_headset_devices">Error getting HEADSET connected devices: %1$s</string>
    <string name="bluetooth_error_profile_proxy">Error getting profile proxy: %1$s</string>
    <string name="bluetooth_error_close_a2dp_proxy">Failed to close A2DP proxy: %1$s</string>
    <string name="bluetooth_error_close_headset_proxy">Failed to close HEADSET proxy: %1$s</string>
    <string name="bluetooth_error_check_connected">Error checking if device is connected: %1$s</string>
    <string name="bluetooth_error_get_connected_devices">Error getting connected devices: %1$s</string>

    <!-- Mobile Network -->
    <string name="mobile_network_settings">Mobile Network Settings</string>
    <string name="mobile_network">Mobile Network</string>
    <string name="mobile_data">Mobile Data</string>
    <string name="mobile_data_enabled">Mobile data is enabled</string>
    <string name="mobile_data_disabled">Mobile data is disabled</string>
    <string name="enable_mobile_data">Enable Mobile Data</string>
    <string name="disable_mobile_data">Disable Mobile Data</string>
    <string name="mobile_network_info">Mobile Network Information</string>
    <string name="network_type">Network Type: %1$s</string>
    <string name="network_operator">Network Operator: %1$s</string>
    <string name="roaming">Roaming: %1$s</string>
    <string name="roaming_enabled">Yes</string>
    <string name="roaming_disabled">No</string>
    <string name="data_roaming">Data Roaming</string>
    <string name="data_roaming_description">Allow data usage when roaming</string>
    <string name="mobile_network_not_available">Mobile network is not available</string>
    <string name="mobile_network_permission_required">Phone state permission is required to access mobile network settings</string>
    <string name="mobile_network_permission_denied">Cannot access mobile network settings without required permissions</string>
    <string name="mobile_network_error">Error accessing mobile network settings</string>
    <string name="mobile_network_status">Status: %1$s</string>
    <string name="mobile_network_connected">Connected</string>
    <string name="mobile_network_disconnected">Disconnected</string>

</resources>