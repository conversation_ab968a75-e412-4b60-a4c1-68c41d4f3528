package com.srthinker.bbnice.registration;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.QrCodeResponse;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.QRCodeUtils;

import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * 设备注册Activity
 * 用于显示设备注册二维码
 */
public class DeviceRegistrationActivity extends AppCompatActivity {
    private static final String TAG = "DeviceRegistration";
    private static final int QR_CODE_SIZE = 800; // 二维码大小
    private static final long REFRESH_INTERVAL = 30000; // 30秒检查一次注册状态

    private DeviceRepository deviceRepository;
    private TextView tvDeviceId;
    private TextView tvStatus;
    private ImageView ivQrCode;
    private TextView tvExpireTime;
    private TextView tvInstructions;
    private Button btnRefresh;
    private ProgressBar progressBar;
    private String deviceId;
    private CountDownTimer expiryTimer;
    private Handler handler;
    private Runnable statusCheckRunnable;
    private long expiryTime;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_device_registration);

        deviceRepository = ApiRepositoryProvider.getInstance(this).getDeviceRepository();
        // 初始化视图
        initViews();

        // 获取设备ID
        deviceId = DeviceIdUtils.getDeviceId(this);
        tvDeviceId.setText("设备ID: " + deviceId);

        // 初始化Handler
        handler = new Handler(Looper.getMainLooper());

        // 检查设备注册状态
        checkDeviceRegistration();
    }

    private void initViews() {
        tvDeviceId = findViewById(R.id.tvDeviceId);
        tvStatus = findViewById(R.id.tvStatus);
        ivQrCode = findViewById(R.id.ivQrCode);
        tvExpireTime = findViewById(R.id.tvExpireTime);
        tvInstructions = findViewById(R.id.tvInstructions);
        btnRefresh = findViewById(R.id.btnRefresh);
        progressBar = findViewById(R.id.progressBar);

        btnRefresh.setOnClickListener(v -> requestRegistrationQRCode());
    }

    /**
     * 检查设备注册状态
     */
    private void checkDeviceRegistration() {
        tvStatus.setText("状态: 正在检查...");

        deviceRepository.getDeviceQrCode().observe(this, new Observer<Result<QrCodeResponse>>() {
            @Override
            public void onChanged(Result<QrCodeResponse> qrCodeResponseResult) {
                if(qrCodeResponseResult.isLoading()) {
                    progressBar.setVisibility(View.VISIBLE);
                } else {
                    progressBar.setVisibility(View.GONE);
                    if (qrCodeResponseResult.isError()) {
                        showError(qrCodeResponseResult.getError().getMessage());
                    } else {
                        loadImage(qrCodeResponseResult.getData().getData().getQrcodeUrl());
                    }
                }
            }
        });
    }


    /**
     * 加载图片
     */
    private void loadImage(String url) {

        // 使用Glide加载高清图片
        Glide.with(this)
                .load(url)
                .apply(new RequestOptions()
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                .into(ivQrCode);
    }

    /**
     * 请求注册二维码
     */
    private void requestRegistrationQRCode() {
        progressBar.setVisibility(View.VISIBLE);
        hideQRCodeViews();

        DeviceRegistrationApi.requestRegistrationQRCode(deviceId, (success, response) -> {
            runOnUiThread(() -> {
                progressBar.setVisibility(View.GONE);

                if (!success || response == null || response.getData() == null) {
                    showError("获取二维码失败，请重试");
                    return;
                }

                // 获取注册URL和过期时间
                String registrationUrl = response.getData().getRegistrationUrl();
                expiryTime = response.getData().getExpireTime();

                if (registrationUrl == null || registrationUrl.isEmpty()) {
                    showError("获取二维码失败，请重试");
                    return;
                }

                // 尝试使用ZXing生成二维码
                Bitmap qrCodeBitmap = null;
                try {
                    qrCodeBitmap = QRCodeUtils.generateQRCode(registrationUrl, QR_CODE_SIZE, QR_CODE_SIZE);
                } catch (Exception e) {
                    Log.e(TAG, "ZXing生成二维码失败: " + e.getMessage());
                }

                if (qrCodeBitmap == null) {
                    showError("生成二维码失败，请重试");
                    return;
                }

                // 显示二维码
                ivQrCode.setImageBitmap(qrCodeBitmap);
                showQRCodeViews();

                // 启动倒计时
                startExpiryCountdown(expiryTime);

                // 定期检查注册状态
                startStatusCheck();
            });
        });
    }

    /**
     * 显示二维码相关视图
     */
    private void showQRCodeViews() {
        ivQrCode.setVisibility(View.VISIBLE);
        tvExpireTime.setVisibility(View.VISIBLE);
        tvInstructions.setVisibility(View.VISIBLE);
        btnRefresh.setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏二维码相关视图
     */
    private void hideQRCodeViews() {
        ivQrCode.setVisibility(View.GONE);
        tvExpireTime.setVisibility(View.GONE);
        tvInstructions.setVisibility(View.GONE);
        btnRefresh.setVisibility(View.GONE);

        // 取消倒计时
        if (expiryTimer != null) {
            expiryTimer.cancel();
        }

        // 取消状态检查
        if (statusCheckRunnable != null) {
            handler.removeCallbacks(statusCheckRunnable);
        }
    }

    /**
     * 启动过期倒计时
     */
    private void startExpiryCountdown(long expiryTimeMillis) {
        // 取消之前的倒计时
        if (expiryTimer != null) {
            expiryTimer.cancel();
        }

        // 计算剩余时间
        long currentTime = System.currentTimeMillis();
        long remainingTime = expiryTimeMillis - currentTime;

        if (remainingTime <= 0) {
            tvExpireTime.setText("二维码已过期，请刷新");
            return;
        }

        // 创建新的倒计时
        expiryTimer = new CountDownTimer(remainingTime, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                // 格式化剩余时间
                String remainingTimeStr = formatTime(millisUntilFinished);
                tvExpireTime.setText("有效期剩余: " + remainingTimeStr);
            }

            @Override
            public void onFinish() {
                tvExpireTime.setText("二维码已过期，请刷新");
                btnRefresh.setText("刷新二维码");
            }
        }.start();
    }

    /**
     * 格式化时间
     */
    private String formatTime(long millis) {
        long minutes = TimeUnit.MILLISECONDS.toMinutes(millis);
        long seconds = TimeUnit.MILLISECONDS.toSeconds(millis) -
                TimeUnit.MINUTES.toSeconds(minutes);
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
    }

    /**
     * 开始定期检查注册状态
     */
    private void startStatusCheck() {
        // 取消之前的检查
        if (statusCheckRunnable != null) {
            handler.removeCallbacks(statusCheckRunnable);
        }

        // 创建新的检查任务
        statusCheckRunnable = new Runnable() {
            @Override
            public void run() {
                DeviceRegistrationApi.checkDeviceRegistration(deviceId, (success, response) -> {
                    if (success && response != null && response.getData() != null) {
                        if (response.getData().isRegistered()) {
                            // 设备已注册
                            runOnUiThread(() -> {
                                tvStatus.setText("状态: 已注册");
                                hideQRCodeViews();
                                showToast("设备注册成功！");
                            });
                        } else {
                            // 设备仍未注册，继续检查
                            handler.postDelayed(this, REFRESH_INTERVAL);
                        }
                    } else {
                        // 检查失败，继续检查
                        handler.postDelayed(this, REFRESH_INTERVAL);
                    }
                });
            }
        };

        // 开始检查
        handler.postDelayed(statusCheckRunnable, REFRESH_INTERVAL);
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        showToast(message);
        btnRefresh.setVisibility(View.VISIBLE);
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 取消倒计时
        if (expiryTimer != null) {
            expiryTimer.cancel();
        }

        // 取消状态检查
        if (statusCheckRunnable != null) {
            handler.removeCallbacks(statusCheckRunnable);
        }
    }
}
