plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.srthinker.bbnice'
    compileSdk 34

    defaultConfig {
        applicationId "com.srthinker.bbnice"
        minSdk 26
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 启用NDK支持
        externalNativeBuild {
            cmake {
                cppFlags ""
                arguments "-DANDROID_STL=c++_shared"
            }
        }

        // 指定支持的ABI
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // 配置CMake
    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    // 启用ViewBinding
    buildFeatures {
        viewBinding true
    }

    // 明确指定源代码目录
    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
        }
        test {
            java.srcDirs = ['src/test/java']
            resources.srcDirs = ['src/test/resources']
        }
        androidTest {
            java.srcDirs = ['src/androidTest/java']
            resources.srcDirs = ['src/androidTest/resources']
        }
    }
}

def camerax_version = "1.2.3"

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.swiperefreshlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    implementation libs.volcenginertc
    implementation libs.okhttp
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation project(path: ':faceunity')


    implementation "androidx.camera:camera-core:${camerax_version}"
    implementation "androidx.camera:camera-camera2:${camerax_version}"
    implementation "androidx.camera:camera-lifecycle:${camerax_version}"
    implementation "androidx.camera:camera-view:${camerax_version}"
    implementation "androidx.camera:camera-video:${camerax_version}"

    // 二维码生成库
    implementation 'com.google.zxing:core:3.4.1'
    implementation ('com.journeyapps:zxing-android-embedded:4.3.0') {
        transitive = false
    }
    // 添加额外的支持库
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'

    // MQTT客户端库
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'
    implementation 'org.eclipse.paho:org.eclipse.paho.android.service:1.1.1'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'

    // 使用系统原生位置服务

    // WorkManager依赖
    implementation 'androidx.work:work-runtime:2.8.1'

    // Room依赖
    implementation 'androidx.room:room-runtime:2.6.1'
    annotationProcessor 'androidx.room:room-compiler:2.6.1'

    // MVVM架构组件
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-runtime:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2'

    // Navigation组件
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'

    // 协程支持
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'

    // 图片加载库
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // 图片缩放库
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    // 测试依赖
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.mockito:mockito-core:5.3.1'
    testImplementation 'org.mockito:mockito-inline:5.2.0'  // 用于模拟final类和静态方法
    testImplementation 'androidx.test:core:1.5.0'
    testImplementation 'androidx.test.ext:junit:1.1.5'
    testImplementation 'org.robolectric:robolectric:4.10.3'  // 用于Android环境模拟
}