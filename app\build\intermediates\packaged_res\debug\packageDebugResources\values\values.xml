<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorBackground">#F5F5F5</color>
    <color name="colorDivider">#BDBDBD</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="colorTextPrimary">#212121</color>
    <color name="colorTextSecondary">#757575</color>
    <color name="white">#FFFFFFFF</color>
    <string name="all_media">All Media</string>
    <string name="api_test">API Test</string>
    <string name="app_name">BBNice</string>
    <string name="apply">Apply</string>
    <string name="available_networks">Available Networks</string>
    <string name="battery_level">Battery Level</string>
    <string name="bind_failed">Failed to bind device</string>
    <string name="bind_success">Device bound successfully</string>
    <string name="camera">Camera</string>
    <string name="cancel">Cancel</string>
    <string name="chat">Chat</string>
    <string name="chat_cn">Chinese Chat</string>
    <string name="chat_en">English Chat</string>
    <string name="chat_history">Chat History</string>
    <string name="chinese">Chinese</string>
    <string name="clear_history">Clear History</string>
    <string name="clear_history_confirm">Are you sure you want to clear all chat history?</string>
    <string name="confirm">Confirm</string>
    <string name="current_location">Current Location</string>
    <string name="delete">Delete</string>
    <string name="delete_confirm">Are you sure you want to delete the selected items?</string>
    <string name="delete_selected">Delete Selected</string>
    <string name="deselect_all">Deselect All</string>
    <string name="device_id">Device ID</string>
    <string name="device_info">Device Info</string>
    <string name="enable_location">Enable Location</string>
    <string name="english">English</string>
    <string name="error_already_bound">Device already bound</string>
    <string name="error_authentication">Authentication error</string>
    <string name="error_connection">Connection error</string>
    <string name="error_device_not_found">Device not found</string>
    <string name="error_invalid_input">Invalid input</string>
    <string name="error_network">Network Error</string>
    <string name="error_not_bound">Device not bound</string>
    <string name="error_server">Server error</string>
    <string name="error_timeout">Request timed out</string>
    <string name="error_unknown">Unknown Error</string>
    <string name="gallery">Gallery</string>
    <string name="go_to_settings">Go to Settings</string>
    <string name="history_cleared">Chat history cleared</string>
    <string name="home">Home</string>
    <string name="language_changed">Language changed</string>
    <string name="language_settings">Language Settings</string>
    <string name="last_updated">Last Updated: %1$s</string>
    <string name="loading">Loading…</string>
    <string name="loading_media">Loading media…</string>
    <string name="location">Location</string>
    <string name="location_not_available">Location not available</string>
    <string name="location_permission_required">Location permission is required</string>
    <string name="location_required">Location permission is required to scan WiFi networks</string>
    <string name="location_service_required">Location service must be enabled to scan WiFi networks on Android 8.0+</string>
    <string name="location_settings">Location Settings</string>
    <string name="location_updated">Location updated</string>
    <string name="login">Login</string>
    <string name="microphone">Microphone</string>
    <string name="network_status">Network Status</string>
    <string name="no_media_found">No media files found</string>
    <string name="no_more_history">No more history</string>
    <string name="no_wifi_networks">No WiFi networks available</string>
    <string name="ok">OK</string>
    <string name="pause">Pause</string>
    <string name="permission_denied">Permission Denied</string>
    <string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string>
    <string name="permission_required">Permission Required</string>
    <string name="phone_state">Phone State</string>
    <string name="photos">Photos</string>
    <string name="play">Play</string>
    <string name="press_to_talk">Press and hold to talk</string>
    <string name="qr_code_expired">QR code has expired</string>
    <string name="qr_code_valid_for">Valid for %1$d minutes</string>
    <string name="register">Register</string>
    <string name="release_to_stop">Release to stop</string>
    <string name="restart_app_message">Please restart the app for the changes to take effect</string>
    <string name="retry">Retry</string>
    <string name="save">Save</string>
    <string name="scan_failed">Failed to scan WiFi networks</string>
    <string name="scan_qr_code">Scan QR Code</string>
    <string name="scanning_wifi">Scanning for WiFi networks…</string>
    <string name="select_all">Select All</string>
    <string name="settings">Settings</string>
    <string name="signal_strength">Signal Strength</string>
    <string name="storage_permission_required">Storage permission is required to access media files</string>
    <string name="system_language">System Language</string>
    <string name="unbind_confirm">Are you sure you want to unbind this device?</string>
    <string name="unbind_device">Unbind Device</string>
    <string name="unbind_failed">Failed to unbind device</string>
    <string name="unbind_success">Device unbound successfully</string>
    <string name="upload_failed">Failed to upload history</string>
    <string name="upload_history">Upload History</string>
    <string name="upload_success">History uploaded successfully</string>
    <string name="videos">Videos</string>
    <string name="wifi">WiFi</string>
    <string name="wifi_already_connected">Already connected to this network</string>
    <string name="wifi_connect">Connect</string>
    <string name="wifi_connected">Connected</string>
    <string name="wifi_connecting">Connecting to %1$s</string>
    <string name="wifi_connection_failed">Failed to connect to WiFi</string>
    <string name="wifi_disconnected">Not connected</string>
    <string name="wifi_enter_password">Enter WiFi Password</string>
    <string name="wifi_password">Password</string>
    <string name="wifi_password_empty">Please enter a password</string>
    <string name="wifi_settings">WiFi Settings</string>
    <string name="wifi_show_password">Show password</string>
    <style name="Base.Theme.BBNice" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="MyDialogStyle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="Theme.BBNice" parent="Base.Theme.BBNice"/>
</resources>