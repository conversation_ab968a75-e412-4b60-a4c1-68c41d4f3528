{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-lt/values-lt.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,3030,3127,3204,3260,3314,3380,3455,3534,3622,3702,3778,3856,3929,4006,4113,4200,4281,4371,4463,4535,4616,4708,4763,4845,4911,4996,5083,5145,5209,5272,5344,5455,5571,5672,5781,5841,5899", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,3025,3122,3199,3255,3309,3375,3450,3529,3617,3697,3773,3851,3924,4001,4108,4195,4276,4366,4458,4530,4611,4703,4758,4840,4906,4991,5078,5140,5204,5267,5339,5450,5566,5667,5776,5836,5894,5976"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3194,3273,3351,3434,3528,3618,3714,3832,3916,3982,4081,4159,4224,4334,4397,4469,4528,4602,4663,4717,4841,4902,4964,5018,5096,5230,5318,5402,5543,5622,5706,5849,5946,6023,6079,6133,6199,6274,6353,6441,6521,6597,6675,6748,6825,6932,7019,7100,7190,7282,7354,7435,7527,7582,7664,7730,7815,7902,7964,8028,8091,8163,8274,8390,8491,8600,8660,8947", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,104", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "420,3268,3346,3429,3523,3613,3709,3827,3911,3977,4076,4154,4219,4329,4392,4464,4523,4597,4658,4712,4836,4897,4959,5013,5091,5225,5313,5397,5538,5617,5701,5844,5941,6018,6074,6128,6194,6269,6348,6436,6516,6592,6670,6743,6820,6927,7014,7095,7185,7277,7349,7430,7522,7577,7659,7725,7810,7897,7959,8023,8086,8158,8269,8385,8486,8595,8655,8713,9024"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,9029", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,9108"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "8718,8828", "endColumns": "109,118", "endOffsets": "8823,8942"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9113", "endColumns": "100", "endOffsets": "9209"}}]}]}