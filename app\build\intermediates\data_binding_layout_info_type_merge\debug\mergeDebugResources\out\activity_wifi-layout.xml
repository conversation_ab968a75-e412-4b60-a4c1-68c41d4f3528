<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_wifi" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_wifi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_wifi_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="28"/></Target><Target id="@+id/wifi_status_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="50" endOffset="55"/></Target><Target id="@+id/tv_wifi_status_label" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="40" endOffset="55"/></Target><Target id="@+id/switch_wifi" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="55"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="52" startOffset="4" endLine="57" endOffset="74"/></Target><Target id="@+id/tv_available_networks" view="TextView"><Expressions/><location startLine="59" startOffset="4" endLine="67" endOffset="60"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="69" startOffset="4" endLine="83" endOffset="59"/></Target><Target id="@+id/rv_wifi_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="76" startOffset="8" endLine="81" endOffset="35"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="85" startOffset="4" endLine="93" endOffset="74"/></Target><Target id="@+id/tv_empty_state" view="TextView"><Expressions/><location startLine="95" startOffset="4" endLine="105" endOffset="74"/></Target></Targets></Layout>