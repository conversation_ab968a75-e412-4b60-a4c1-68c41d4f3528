<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/call_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/btn_finish"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:src="@mipmap/arrow_back"
        android:layout_marginEnd="20dp"
        app:layout_constraintEnd_toStartOf="@+id/tv_name"
        app:layout_constraintTop_toTopOf="@+id/tv_name"
        app:layout_constraintBottom_toBottomOf="@id/tv_name"
        />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="白居易"
        android:textSize="20sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/call_icon" />

    <ImageView
        android:id="@+id/call_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/learn_icon_libai"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_call"
        android:clickable="true"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/background_call_button"
        android:drawableStart="@mipmap/icon_call"
        android:drawablePadding="5dp"
        android:paddingStart="30dp"
        android:paddingEnd="30dp"

        android:text="打电话"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/call_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>