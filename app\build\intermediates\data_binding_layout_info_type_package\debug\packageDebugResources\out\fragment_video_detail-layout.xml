<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_video_detail" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\fragment_video_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_video_detail_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="70" endOffset="51"/></Target><Target id="@+id/video_view" view="VideoView"><Expressions/><location startLine="9" startOffset="4" endLine="14" endOffset="51"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="16" startOffset="4" endLine="23" endOffset="59"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="25" startOffset="4" endLine="34" endOffset="51"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="36" startOffset="4" endLine="68" endOffset="18"/></Target><Target id="@+id/btn_play_pause" view="ImageButton"><Expressions/><location startLine="45" startOffset="8" endLine="51" endOffset="59"/></Target><Target id="@+id/seek_bar" view="SeekBar"><Expressions/><location startLine="53" startOffset="8" endLine="58" endOffset="39"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="67" endOffset="40"/></Target></Targets></Layout>