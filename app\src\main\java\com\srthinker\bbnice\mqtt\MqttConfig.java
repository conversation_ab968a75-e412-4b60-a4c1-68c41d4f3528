package com.srthinker.bbnice.mqtt;

import android.content.Context;
import android.util.Log;

import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.SPUtils;

/**
 * MQTT配置类
 * 用于存储MQTT相关的配置信息
 */
public class MqttConfig {
    private static final String TAG = "MqttConfig";

    // MQTT服务器地址
//    private static final String DEFAULT_BROKER_URL = "tcp://mqtt.example.com:1883";
    private static final String DEFAULT_BROKER_URL = "tcp://broker.emqx.io:1883";
    private static final String PREFS_BROKER_URL = "mqtt_broker_url";

    // MQTT用户名
    private static final String DEFAULT_USERNAME = "bbnice";
    private static final String PREFS_USERNAME = "mqtt_username";

    // MQTT密码
    private static final String DEFAULT_PASSWORD = "bbnice-password";
    private static final String PREFS_PASSWORD = "mqtt_password";

    // MQTT客户端ID前缀
    private static final String CLIENT_ID_PREFIX = "bbnice-device-";

    // MQTT主题前缀
    private static final String TOPIC_PREFIX = "bbnice/devices/";
    private static final String TOPIC_SUFFIX = "/commands";

    // MQTT服务质量
    private static final int QOS = 1;

    // 单例实例
    private static volatile MqttConfig instance;

    // 设备ID
    private String deviceId;

    // MQTT服务器地址
    private String brokerUrl;

    // MQTT用户名
    private String username;

    // MQTT密码
    private String password;

    // MQTT客户端ID
    private String clientId;

    // MQTT主题
    private String topic;

    /**
     * 私有构造函数
     * @param context 上下文
     */
    private MqttConfig(Context context) {
        // 获取设备ID
        deviceId = DeviceIdUtils.getDeviceId(context);

        // 从SharedPreferences获取配置
        SPUtils spUtils = SPUtils.getInstance();
        brokerUrl = spUtils.getString(PREFS_BROKER_URL, DEFAULT_BROKER_URL);
        username = spUtils.getString(PREFS_USERNAME, DEFAULT_USERNAME);
        password = spUtils.getString(PREFS_PASSWORD, DEFAULT_PASSWORD);

        // 生成客户端ID和主题
        clientId = CLIENT_ID_PREFIX + deviceId;
        topic = TOPIC_PREFIX + deviceId + TOPIC_SUFFIX;

        Log.d(TAG, "MqttConfig initialized: " + this);
    }

    /**
     * 获取单例实例
     * @param context 上下文
     * @return MqttConfig实例
     */
    public static MqttConfig getInstance(Context context) {
        if (instance == null) {
            synchronized (MqttConfig.class) {
                if (instance == null) {
                    instance = new MqttConfig(context);
                }
            }
        }
        return instance;
    }

    /**
     * 更新MQTT服务器地址
     * @param brokerUrl MQTT服务器地址
     */
    public void updateBrokerUrl(String brokerUrl) {
        this.brokerUrl = brokerUrl;
        SPUtils.getInstance().saveString(PREFS_BROKER_URL, brokerUrl);
        Log.d(TAG, "Broker URL updated: " + brokerUrl);
    }

    /**
     * 更新MQTT用户名
     * @param username MQTT用户名
     */
    public void updateUsername(String username) {
        this.username = username;
        SPUtils.getInstance().saveString(PREFS_USERNAME, username);
        Log.d(TAG, "Username updated: " + username);
    }

    /**
     * 更新MQTT密码
     * @param password MQTT密码
     */
    public void updatePassword(String password) {
        this.password = password;
        SPUtils.getInstance().saveString(PREFS_PASSWORD, password);
        Log.d(TAG, "Password updated");
    }

    /**
     * 获取设备ID
     * @return 设备ID
     */
    public String getDeviceId() {
        return deviceId;
    }

    /**
     * 获取MQTT服务器地址
     * @return MQTT服务器地址
     */
    public String getBrokerUrl() {
        return brokerUrl;
    }

    /**
     * 获取MQTT用户名
     * @return MQTT用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 获取MQTT密码
     * @return MQTT密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 获取MQTT客户端ID
     * @return MQTT客户端ID
     */
    public String getClientId() {
        return clientId;
    }

    /**
     * 获取MQTT主题
     * @return MQTT主题
     */
    public String getTopic() {
        return topic;
    }

    /**
     * 获取MQTT服务质量
     * @return MQTT服务质量
     */
    public int getQos() {
        return QOS;
    }

    @Override
    public String toString() {
        return "MqttConfig{" +
                "deviceId='" + deviceId + '\'' +
                ", brokerUrl='" + brokerUrl + '\'' +
                ", username='" + username + '\'' +
                ", clientId='" + clientId + '\'' +
                ", topic='" + topic + '\'' +
                '}';
    }
}
