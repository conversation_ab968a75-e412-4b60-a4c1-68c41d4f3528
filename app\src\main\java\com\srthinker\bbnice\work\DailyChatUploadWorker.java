package com.srthinker.bbnice.work;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.api.repository.DeviceRepositoryImpl;
import com.srthinker.bbnice.api.repository.RepositoryProvider;
import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.db.ChatRepository;
import com.srthinker.bbnice.common.AIServerUtils;
import com.srthinker.bbnice.common.IAIServerRequestCallback;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.HttpUtils;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 每日聊天记录上传工作器，负责将前一天的聊天记录上传到服务器
 */
public class DailyChatUploadWorker extends Worker {
    private static final String TAG = "DailyChatUploadWorker";

    private DeviceRepository deviceRepository;

    public DailyChatUploadWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        deviceRepository = RepositoryProvider.getInstance(context).getDeviceRepository();

    }

    @NonNull
    @Override
    public Result doWork() {
        Log.d(TAG, "Starting daily chat history upload work");

        Context context = getApplicationContext();
        ChatRepository repository = ChatRepository.getInstance(context);
        String deviceId = DeviceIdUtils.getDeviceId(context);

        // 获取昨天的消息
        List<ChatMessage> yesterdayMessages = repository.getYesterdayMessages();

        if (yesterdayMessages.isEmpty()) {
            Log.d(TAG, "No messages from yesterday to upload");
            return Result.success();
        }

        Log.d(TAG, "Found " + yesterdayMessages.size() + " messages from yesterday");

        // 使用CountDownLatch等待上传完成
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicBoolean uploadSuccess = new AtomicBoolean(false);

        // 上传消息
        deviceRepository.reportChatHistory(yesterdayMessages, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                Log.d(TAG, "Yesterday's chat history upload successful: ");
                uploadSuccess.set(true);

                // 标记消息为已上传
                for (ChatMessage message : yesterdayMessages) {
                    repository.markMessageAsUploadedAsync(message.getMessageId());
                }
                latch.countDown();
            }

            @Override
            public void onFailure(String error) {
                Log.e(TAG, "Yesterday's chat history upload failed: " + error);
                latch.countDown();
            }
        });

        try {
            // 等待上传完成，最多等待60秒
            boolean completed = latch.await(60, TimeUnit.SECONDS);

            if (!completed) {
                Log.e(TAG, "Yesterday's chat history upload timed out");
                return Result.retry();
            }

            if (uploadSuccess.get()) {
                return Result.success();
            } else {
                return Result.retry();
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "Yesterday's chat history upload interrupted: " + e.getMessage());
            Thread.currentThread().interrupt();
            return Result.retry();
        }
    }
}
