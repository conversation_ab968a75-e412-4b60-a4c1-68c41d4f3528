<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_loading" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\dialog_loading.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/dialog_loading_view"><Targets><Target id="@+id/dialog_loading_view" tag="layout/dialog_loading_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="38" endOffset="14"/></Target><Target id="@+id/progressBar1" view="ProgressBar"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/tipTextView" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="35" endOffset="37"/></Target></Targets></Layout>