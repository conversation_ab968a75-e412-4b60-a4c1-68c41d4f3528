package com.srthinker.bbnice.api;

/**
 * API常量类
 * 定义API相关的常量
 */
public class ApiConstants {
    // API服务器URL
    public static final String API_SERVER_URL = "https://api.bbnice.net/";
//    public static final String API_SERVER_URL = "http://118.145.209.168:8080/";

    // API版本
    public static final String API_VERSION = "v1";

    // API基础路径
    public static final String API_BASE_PATH = "api/" + API_VERSION + "/";

    /**
     * 获取完整的API URL
     * @param endpoint API端点
     * @return 完整的API URL
     */
    public static String getApiUrl(String endpoint) {
        return API_SERVER_URL + API_BASE_PATH + endpoint;
    }

    /**
     * 业务码
     *
     * 业务码规则：
     * - 前3位表示HTTP状态码
     * - 第4位表示业务模块
     * - 后2位表示具体错误码
     */
    public static class BusinessCode {
        // ==================== 2xx 成功 ====================

        /**
         * 成功
         * 请求成功处理并返回结果
         */
        public static final int SUCCESS = 200000;

        // ==================== 4xx 客户端错误 ====================

        // ---------- 400xxx 请求错误 ----------

        /**
         * 通用客户端请求错误
         */
        public static final int BAD_REQUEST = 400000;

        /**
         * @deprecated 使用 {@link #BAD_REQUEST} 代替
         */
        @Deprecated
        public static final int COMMON_ERROR = BAD_REQUEST;

        /**
         * 请求参数缺失
         * 请求中缺少必要的参数
         */
        public static final int PARAM_MISSING = 400001;

        /**
         * 请求参数格式错误
         * 请求参数的格式不符合要求（例如：日期格式错误、邮箱格式错误等）
         */
        public static final int PARAM_FORMAT_ERROR = 400002;

        /**
         * 请求参数值无效
         * 请求参数的值不在允许的范围内
         */
        public static final int PARAM_VALUE_INVALID = 400003;

        /**
         * 请求体内容无法解析
         * 服务器无法解析请求体的内容（例如：Content-Type 不正确或内容格式错误）
         */
        public static final int REQUEST_BODY_INVALID = 400004;

        // ---------- 401xxx 未授权 ----------

        /**
         * 通用未授权错误
         */
        public static final int UNAUTHORIZED = 401000;

        /**
         * 用户未登录
         * 尝试访问需要身份验证的资源，但用户尚未登录
         */
        public static final int USER_NOT_LOGIN = 401001;

        /**
         * 认证凭证无效
         * 提供的认证凭证（例如：Token、Session ID）无效或已过期
         */
        public static final int INVALID_TOKEN = 401002;

        /**
         * 认证失败次数过多
         * 由于认证失败次数过多，账户已被临时锁定
         */
        public static final int TOO_MANY_AUTH_FAILURES = 401003;

        // ---------- 403xxx 禁止访问 ----------

        /**
         * 通用禁止访问错误
         */
        public static final int FORBIDDEN = 403000;

        /**
         * 用户无权访问该资源
         * 用户已通过身份验证，但其角色或权限不允许访问请求的资源
         */
        public static final int ACCESS_DENIED = 403001;

        /**
         * 资源被禁止访问
         * 服务器策略禁止当前用户访问该资源（例如：IP 地址被限制）
         */
        public static final int RESOURCE_FORBIDDEN = 403002;

        /**
         * 操作被拒绝
         * 服务器理解了请求，但拒绝执行该操作（例如：尝试修改只读资源）
         */
        public static final int OPERATION_DENIED = 403003;

        // ---------- 404xxx 资源不存在 ----------

        /**
         * 通用资源不存在错误
         */
        public static final int NOT_FOUND = 404000;

        /**
         * 请求的资源不存在
         * 服务器找不到与请求 URI 匹配的资源
         */
        public static final int RESOURCE_NOT_FOUND = 404001;

        /**
         * @deprecated 使用 {@link #NOT_FOUND} 或 {@link #RESOURCE_NOT_FOUND} 代替
         */
        @Deprecated
        public static final int RESOURCE_NOT_FOUND_OLD = 404000;

        /**
         * 指定的子资源不存在
         * 请求的资源存在，但其下的某个特定子资源不存在（例如：用户下的某个订单不存在）
         */
        public static final int SUB_RESOURCE_NOT_FOUND = 404002;

        /**
         * 用户不存在
         * 请求的用户资源不存在，用户尚未在系统中注册
         */
        public static final int USER_NOT_FOUND = 404003;

        /**
         * 设备不存在
         * 请求的设备资源不存在
         */
        public static final int DEVICE_NOT_FOUND = 404004;

        // ---------- 405xxx 方法不允许 ----------

        /**
         * 通用方法不允许错误
         */
        public static final int METHOD_NOT_ALLOWED = 405000;

        /**
         * 请求方法不被允许
         * 请求中指定的方法（例如：POST、DELETE）不适用于请求的资源
         */
        public static final int REQUEST_METHOD_NOT_ALLOWED = 405001;

        // ---------- 设备相关错误 ----------

        /**
         * 设备未绑定
         * 设备尚未绑定到任何用户账户
         */
        public static final int DEVICE_NOT_BOUND = 402201;

        /**
         * 设备已被绑定
         * 设备已经绑定到其他用户账户
         */
        public static final int DEVICE_ALREADY_BOUND = 402202;

        // ==================== 5xx 服务器错误 ====================

        /**
         * 通用服务器错误
         * 服务器遇到了一个未预期的情况，导致无法完成请求
         */
        public static final int SERVER_ERROR = 500000;

        /**
         * 服务不可用
         * 服务器当前不可用（例如：过载或维护）
         */
        public static final int SERVICE_UNAVAILABLE = 503000;

        // ==================== 6xx 客户端自定义错误 ====================

        /**
         * 网络错误
         * 客户端网络连接错误
         */
        public static final int NETWORK_ERROR = 600000;

        /**
         * JSON解析错误
         * 客户端解析JSON数据失败
         */
        public static final int JSON_PARSE_ERROR = 600001;

        /**
         * 参数错误
         * 客户端参数错误
         */
        public static final int PARAM_ERROR = 600002;

        /**
         * 获取业务码描述
         * @param code 业务码
         * @return 业务码描述
         */
        public static String getDescription(int code) {
            switch (code) {
                // 2xx 成功
                case SUCCESS: return "成功";

                // 400xxx 请求错误
                case BAD_REQUEST: return "请求错误";
                case PARAM_MISSING: return "请求参数缺失";
                case PARAM_FORMAT_ERROR: return "请求参数格式错误";
                case PARAM_VALUE_INVALID: return "请求参数值无效";
                case REQUEST_BODY_INVALID: return "请求体内容无法解析";

                // 401xxx 未授权
                case UNAUTHORIZED: return "未授权";
                case USER_NOT_LOGIN: return "用户未登录";
                case INVALID_TOKEN: return "认证凭证无效";
                case TOO_MANY_AUTH_FAILURES: return "认证失败次数过多";

                // 403xxx 禁止访问
                case FORBIDDEN: return "禁止访问";
                case ACCESS_DENIED: return "用户无权访问该资源";
                case RESOURCE_FORBIDDEN: return "资源被禁止访问";
                case OPERATION_DENIED: return "操作被拒绝";

                // 404xxx 资源不存在
                case NOT_FOUND: return "资源不存在";
                case RESOURCE_NOT_FOUND: return "请求的资源不存在";
                case SUB_RESOURCE_NOT_FOUND: return "指定的子资源不存在";
                case USER_NOT_FOUND: return "用户不存在";
                case DEVICE_NOT_FOUND: return "设备不存在";

                // 405xxx 方法不允许
                case METHOD_NOT_ALLOWED: return "方法不允许";
                case REQUEST_METHOD_NOT_ALLOWED: return "请求方法不被允许";

                // 设备相关错误
                case DEVICE_NOT_BOUND: return "设备未绑定";
                case DEVICE_ALREADY_BOUND: return "设备已被绑定";

                // 5xx 服务器错误
                case SERVER_ERROR: return "服务器错误";
                case SERVICE_UNAVAILABLE: return "服务不可用";

                // 6xx 客户端自定义错误
                case NETWORK_ERROR: return "网络错误";
                case JSON_PARSE_ERROR: return "JSON解析错误";
                case PARAM_ERROR: return "参数错误";

                default: return "未知错误";
            }
        }
    }
}
