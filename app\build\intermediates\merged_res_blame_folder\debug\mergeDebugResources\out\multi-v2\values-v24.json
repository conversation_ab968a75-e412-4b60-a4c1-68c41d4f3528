{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-v24/values-v24.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\a794018af75eb11e6bdc047000971445\\transformed\\appcompat-1.6.1\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\a5d503cf4cb712363b2485f01c369623\\transformed\\material-1.10.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5,6,9,12,15", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,140,239,326,427,626,839,1040", "endLines": "2,3,4,5,8,11,14,17", "endColumns": "84,98,86,100,10,10,10,10", "endOffsets": "135,234,321,422,621,834,1035,1250"}, "to": {"startLines": "4,5,6,7,8,11,14,17", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "347,432,531,618,719,918,1131,1332", "endLines": "4,5,6,7,10,13,16,19", "endColumns": "84,98,86,100,10,10,10,10", "endOffsets": "427,526,613,714,913,1126,1327,1542"}}]}]}