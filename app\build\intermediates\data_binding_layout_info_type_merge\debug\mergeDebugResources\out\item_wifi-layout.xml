<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_wifi" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\item_wifi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_wifi_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="68" endOffset="35"/></Target><Target id="@+id/iv_wifi_signal" view="ImageView"><Expressions/><location startLine="14" startOffset="8" endLine="21" endOffset="55"/></Target><Target id="@+id/tv_wifi_name" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="38" endOffset="63"/></Target><Target id="@+id/tv_wifi_status" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="54" endOffset="69"/></Target><Target id="@+id/iv_wifi_lock" view="ImageView"><Expressions/><location startLine="56" startOffset="8" endLine="64" endOffset="55"/></Target></Targets></Layout>