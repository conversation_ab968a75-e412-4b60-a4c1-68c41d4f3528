<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/x290"
    android:background="@color/primary_list"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <com.faceunity.nama.seekbar.DiscreteSeekBar
        android:id="@+id/seek_bar"
        android:layout_width="@dimen/x528"
        android:layout_height="@dimen/x48"
        android:layout_marginTop="@dimen/x24"
        android:layout_marginBottom="@dimen/x38"
        android:visibility="invisible"
        app:dsb_indicatorColor="@color/main_color"
        app:dsb_indicatorElevation="0dp"
        app:dsb_indicatorPopupEnabled="true"
        app:dsb_max="100"
        app:dsb_min="0"
        app:dsb_progressColor="@color/main_color"
        app:dsb_rippleColor="@color/main_color"
        app:dsb_scrubberHeight="@dimen/x4"
        app:dsb_thumbSize="@dimen/x32"
        app:dsb_trackBaseHeight="@dimen/x16"
        app:dsb_trackColor="@color/colorWhite"
        app:dsb_trackHeight="@dimen/x4"
        app:dsb_value="0" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/x180" />

</LinearLayout>