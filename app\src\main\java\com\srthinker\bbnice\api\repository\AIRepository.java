package com.srthinker.bbnice.api.repository;

import androidx.lifecycle.LiveData;

import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.ImageRecognizeResponse;
import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCStopResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.core.Repository;
import com.srthinker.bbnice.core.Result;

/**
 * 设备相关的Repository接口
 */
public interface AIRepository extends Repository {

    LiveData<Result<RTCTokenResponse>> aiTokens(String roomId);
    LiveData<Result<RTCStartResponse>> rtcStart(RTCStartRequestData requestData);

    LiveData<Result<RTCStopResponse>> rtcStop(String taskId, String roomId);

    LiveData<Result<ImageRecognizeResponse>> imageRecognize(byte[] imgData);



}
