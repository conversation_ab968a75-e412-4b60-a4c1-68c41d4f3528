# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 36ms
  [gap of 21ms]
generate_cxx_metadata completed in 69ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 48ms
  [gap of 17ms]
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 32ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 48ms

