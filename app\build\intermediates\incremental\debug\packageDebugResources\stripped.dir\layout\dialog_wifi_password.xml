<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/tv_wifi_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="WiFi"
        android:textSize="18sp"
        android:textStyle="bold" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/wifi_password"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:paddingStart="4dp"
        android:paddingEnd="4dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_wifi_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword" />

    </com.google.android.material.textfield.TextInputLayout>

    <CheckBox
        android:id="@+id/cb_show_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/wifi_show_password" />

</LinearLayout>
