package com.srthinker.bbnice.recognition;

import android.os.Bundle;
import android.util.Log;
import android.view.TextureView;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.home.AbstractBaseAIActivity;
import com.ss.bytertc.engine.VideoCanvas;
import com.ss.bytertc.engine.data.StreamIndex;
import com.ss.bytertc.engine.data.VideoPixelFormat;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.type.MediaStreamType;
import com.ss.bytertc.engine.video.VideoPreprocessorConfig;

public class RecognitionActivity extends AbstractBaseAIActivity {
    private FrameLayout localViewContainer;
    private ImageView btnSpeak;
    private boolean enableSpeak = false;
    private VideoProcessor customVideoProcessor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setContentView(R.layout.activity_recognition);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onStart() {
        super.onStart();
        setVideoProcessor();
        rtcVideo.startVideoCapture();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        rtcVideo.setLocalVideoCanvas(StreamIndex.STREAM_INDEX_MAIN, null);
        rtcVideo.stopVideoCapture();
    }

    private void setVideoProcessor(){
        VideoPreprocessorConfig config = new VideoPreprocessorConfig();
        config.requiredPixelFormat = VideoPixelFormat.I420;
        if(customVideoProcessor == null) {
            customVideoProcessor = new VideoProcessor();
            customVideoProcessor.setRenderEnable(false);

            // 启用视频防抖
            customVideoProcessor.setStabilizationEnabled(true);
            // 设置防抖强度 (0.0-1.0)
            customVideoProcessor.setStabilizationStrength(0.5f);
            // 设置防抖模式为高级模式
            customVideoProcessor.setStabilizationMode(VideoProcessor.StabilizationMode.ADVANCED);
            // 设置是否只处理亮度通道
            customVideoProcessor.setProcessLumaOnly(false);

            Log.d("RecognitionActivity", "Video processor initialized with ADVANCED stabilization mode");
        }
        rtcVideo.registerLocalVideoProcessor(customVideoProcessor, config);
    }

    @Override
    protected IRTCRoomEventHandler getIRTCRoomEventHandler() {
        return null;
    }


    @Override
    protected void initUI() {
        setLocalRenderView();
    }

    @Override
    protected void initData() {
        super.initData();
        roomMessage.enableVision = true;
    }

    @Override
    protected boolean enableAudioCapture() {
        return false;
    }

    @Override
    protected boolean enableVideoCapture() {
        return true;
    }


    private void setLocalRenderView() {
        TextureView textureView = new TextureView(this);
        VideoCanvas videoCanvas = new VideoCanvas();
        videoCanvas.renderView = textureView;
        videoCanvas.renderMode = VideoCanvas.RENDER_MODE_HIDDEN;
        // 设置本地视频渲染视图
        rtcVideo.setLocalVideoCanvas(StreamIndex.STREAM_INDEX_MAIN, videoCanvas);

        localViewContainer = findViewById(R.id.local_view_container);
        localViewContainer.removeAllViews();
        localViewContainer.addView(textureView);

        btnSpeak = findViewById(R.id.btn_speak);
        btnSpeak.setOnClickListener(v -> {
            if (!enableSpeak) {
                btnSpeak.setImageResource(R.mipmap.call_mute_off);
                rtcRoom.publishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
                enableSpeak = true;

            } else {
                btnSpeak.setImageResource(R.mipmap.call_mute_on);
                rtcRoom.unpublishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
                enableSpeak = false;
            }
        });
    }

    private void setRemoteRenderView(String uid) {
//        VideoCanvas videoCanvas = new VideoCanvas();
//        videoCanvas.renderMode = VideoCanvas.RENDER_MODE_HIDDEN;
//        RemoteStreamKey remoteStreamKey = new RemoteStreamKey(roomMessage.roomID, uid, StreamIndex.STREAM_INDEX_MAIN);
//        // 设置远端视频渲染视图
//        rtcVideo.setRemoteVideoCanvas(remoteStreamKey, videoCanvas);
    }
}