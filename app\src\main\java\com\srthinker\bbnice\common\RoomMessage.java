package com.srthinker.bbnice.common;

/**
 * Created by yzy on 2025/4/3.
 */
public class RoomMessage {
    public volatile String token = "";
    public volatile String roomID= "";
    public volatile String userID= "";
    public volatile String taskID= "";

    public volatile boolean enableVision = false;

    /**
     * 初始化AI助手的描述符
     */
    public String systemMessage = "";

    /**
     * 欢迎词
     */
    public String welcomeMessage = "";

    public RoomMessage() {
    }

    public RoomMessage(String roomID, String userID) {
        this.roomID = roomID;
        this.userID = userID;
    }

    @Override
    public String toString() {
        return "RoomMessage{" +
                "token='" + token + '\'' +
                ", roomID='" + roomID + '\'' +
                ", userID='" + userID + '\'' +
                ", taskID='" + taskID + '\'' +
                ", enableVision=" + enableVision +
                ", systemMessage='" + systemMessage + '\'' +
                ", welcomeMessage='" + welcomeMessage + '\'' +
                '}';
    }
}
