// Generated by view binder compiler. Do not edit!
package com.srthinker.bbnice.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.srthinker.bbnice.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLocationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout main;

  @NonNull
  public final SwitchCompat switchWifi;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvWifiStatusLabel;

  @NonNull
  public final ConstraintLayout wifiStatusContainer;

  private ActivityLocationBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout main, @NonNull SwitchCompat switchWifi, @NonNull Toolbar toolbar,
      @NonNull TextView tvWifiStatusLabel, @NonNull ConstraintLayout wifiStatusContainer) {
    this.rootView = rootView;
    this.main = main;
    this.switchWifi = switchWifi;
    this.toolbar = toolbar;
    this.tvWifiStatusLabel = tvWifiStatusLabel;
    this.wifiStatusContainer = wifiStatusContainer;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLocationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLocationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_location, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLocationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout main = (ConstraintLayout) rootView;

      id = R.id.switch_wifi;
      SwitchCompat switchWifi = ViewBindings.findChildViewById(rootView, id);
      if (switchWifi == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_wifi_status_label;
      TextView tvWifiStatusLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvWifiStatusLabel == null) {
        break missingId;
      }

      id = R.id.wifi_status_container;
      ConstraintLayout wifiStatusContainer = ViewBindings.findChildViewById(rootView, id);
      if (wifiStatusContainer == null) {
        break missingId;
      }

      return new ActivityLocationBinding((ConstraintLayout) rootView, main, switchWifi, toolbar,
          tvWifiStatusLabel, wifiStatusContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
