<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="end"
    android:gravity="end"
    android:orientation="horizontal">

    <LinearLayout
        android:padding="8dp"
        android:background="@drawable/background_sent_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_gravity="center_vertical"
            android:id="@+id/textViewMessageSent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="250dp"
            android:padding="8dp"
            android:text="sduihgfiudfhgigsdfuihg"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_marginTop="5dp"
            android:layout_marginEnd="5dp"
            android:id="@+id/iv_other_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@mipmap/my_icon"
            app:layout_constraintEnd_toStartOf="@+id/textViewMessageSent"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>