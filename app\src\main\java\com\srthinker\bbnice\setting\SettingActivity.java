package com.srthinker.bbnice.setting;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.home.HomeItemAdapter;
import com.srthinker.bbnice.home.HomeViewModel;

import java.util.ArrayList;

public class SettingActivity extends AppCompatActivity {
    private RecyclerView recyclerView;
    private SettingViewModel viewModel;
    private SettingItemAdapter adapter;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_setting);


        recyclerView = findViewById(R.id.recycler_view);
        viewModel = new ViewModelProvider(this).get(SettingViewModel.class);
        adapter = new SettingItemAdapter(this, new ArrayList<>());
        recyclerView.setAdapter(adapter);

        // 初始化UI
        setupRecyclerView();

        // 观察ViewModel中的数据变化
        observeViewModel();

        // 加载数据
        viewModel.loadItems();
    }


    /**
     * 初始化RecyclerView
     */
    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new SettingItemAdapter(this, new ArrayList<>());
        recyclerView.setAdapter(adapter);
    }

    /**
     * 观察ViewModel中的数据变化
     */
    private void observeViewModel() {
        // 观察首页项目列表
        viewModel.getItems().observe(this, items -> {
            if (items != null) {
                adapter.updateItems(items);
            }
        });
    }
}