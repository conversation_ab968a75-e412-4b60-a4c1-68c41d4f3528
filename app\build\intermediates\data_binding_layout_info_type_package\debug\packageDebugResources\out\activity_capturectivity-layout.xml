<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_capturectivity" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_capturectivity.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_capturectivity_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="51"/></Target><Target id="@+id/surface" view="TextureView"><Expressions/><location startLine="9" startOffset="4" endLine="12" endOffset="45"/></Target><Target id="@+id/result_bg_mask" view="View"><Expressions/><location startLine="14" startOffset="4" endLine="18" endOffset="45"/></Target><Target id="@+id/result_bg" view="ImageView"><Expressions/><location startLine="20" startOffset="4" endLine="25" endOffset="45"/></Target><Target id="@+id/tv_en_result" view="TextView"><Expressions/><location startLine="27" startOffset="4" endLine="37" endOffset="46"/></Target><Target id="@+id/btn_cap" view="ImageView"><Expressions/><location startLine="39" startOffset="4" endLine="49" endOffset="57"/></Target><Target id="@+id/tv_cn_result" view="TextView"><Expressions/><location startLine="51" startOffset="4" endLine="60" endOffset="55"/></Target><Target id="@+id/btn_back" view="Button"><Expressions/><location startLine="62" startOffset="4" endLine="75" endOffset="9"/></Target></Targets></Layout>