package com.srthinker.bbnice.home;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.repository.AIRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.common.Constants;
import com.srthinker.bbnice.common.GlobalRtcVideo;
import com.srthinker.bbnice.common.RoomMessage;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.ss.bytertc.engine.RTCRoom;
import com.ss.bytertc.engine.RTCRoomConfig;
import com.ss.bytertc.engine.RTCVideo;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.type.ChannelProfile;
import com.ss.bytertc.engine.type.MediaStreamType;

public abstract class AbstractBaseAIActivity extends AppCompatActivity {

    public static final String TAG = "AbstractBaseAIActivity";
    protected AIRepository aiRepository;
    protected RTCVideo rtcVideo;
    protected RTCRoom rtcRoom;
    protected final RoomMessage roomMessage = new RoomMessage();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        aiRepository = ApiRepositoryProvider.getInstance(this).getAiRepository();

        Intent intent = getIntent();
        roomMessage.systemMessage = intent.getStringExtra(Constants.INTENT_KEY_SYSTEM_MESSAGE);
        roomMessage.welcomeMessage = intent.getStringExtra(Constants.INTENT_KEY_WELCOME_MESSAGE);
//        roomMessage.roomID = UUID.randomUUID().toString();
        roomMessage.roomID = DeviceIdUtils.getDeviceId(this) + "_ai";
        roomMessage.userID = DeviceIdUtils.getDeviceId(this);
        rtcVideo = GlobalRtcVideo.getInstance().rtcVideo();
        rtcRoom = rtcVideo.createRTCRoom(roomMessage.roomID);
        rtcRoom.setRTCRoomEventHandler(getIRTCRoomEventHandler());

        initData();
        initUI();
        initAI();
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (enableAudioCapture() && rtcRoom != null) {
            rtcRoom.publishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
        }
        if (enableVideoCapture()) {
            rtcRoom.publishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_VIDEO);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (enableAudioCapture() && rtcRoom != null) {
            rtcRoom.unpublishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
        }
        if (enableVideoCapture()) {
            rtcRoom.unpublishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_VIDEO);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (rtcRoom != null) {
            rtcRoom.leaveRoom();
            rtcRoom.destroy();
            rtcRoom = null;
        }
        if (!TextUtils.isEmpty(roomMessage.taskID)) {
            aiRepository.rtcStop(roomMessage.taskID, roomMessage.roomID);
        }
    }

    protected void initData() {
    }

    abstract protected void initUI();

    protected void initAI() {
        aiRepository.aiTokens(roomMessage.roomID).observe(AbstractBaseAIActivity.this, rtcTokenResponseResult -> {
            Log.d(TAG, "aiTokens: " + rtcTokenResponseResult);
            if (rtcTokenResponseResult.isSuccess()) {
                RTCTokenResponse rtcTokenResponse = rtcTokenResponseResult.getData();
                if (rtcTokenResponse.isSuccess()) {
                    roomMessage.token = rtcTokenResponse.getData().getToken();

//                    rtcRoom.joinRoom(roomMessage.token, userInfo, roomConfig);

                    // 用户信息
                    UserInfo userInfo = new UserInfo(roomMessage.userID, "");

                    // 设置房间配置
                    RTCRoomConfig roomConfig = new RTCRoomConfig(
                            ChannelProfile.CHANNEL_PROFILE_CHAT_ROOM,
                            true,
                            true,
                            roomMessage.enableVision
                    );
                    Log.d(TAG, "join room: " + rtcRoom.joinRoom(roomMessage.token, userInfo, roomConfig));


                    RTCStartRequestData requestData = new RTCStartRequestData();
                    requestData.room_id = roomMessage.roomID;
                    requestData.enable_vision = roomMessage.enableVision;
                    requestData.enable_subtitle = true;
                    requestData.welcome_message = roomMessage.welcomeMessage;
                    requestData.system_message = roomMessage.systemMessage;
                    aiRepository.rtcStart(requestData).observe(AbstractBaseAIActivity.this, rtcStartResponseResult -> {
                        if (rtcStartResponseResult.isSuccess()) {
                            RTCStartResponse rtcStartResponse = rtcStartResponseResult.getData();
                            roomMessage.taskID = rtcStartResponse.getData().getTask_id();
                            Log.d(TAG, "rtcStart: " + rtcStartResponse);
                        }
                    });
                }
            }
        });
    }

    protected abstract IRTCRoomEventHandler getIRTCRoomEventHandler();

    protected abstract boolean enableAudioCapture();

    protected abstract boolean enableVideoCapture();

}
