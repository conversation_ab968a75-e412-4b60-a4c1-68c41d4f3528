package com.srthinker.bbnice.learn;

import android.os.Bundle;
import android.util.Log;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.repository.AIRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.common.GlobalRtcVideo;
import com.srthinker.bbnice.common.RoomMessage;
import com.srthinker.bbnice.common.ZoomOutPageTransformer;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.ss.bytertc.engine.RTCRoomConfig;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.type.ChannelProfile;

public class LearnActivity extends AppCompatActivity {
    private LearnPagerAdapter pagerAdapter;
    private ViewPager2 viewPager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_learn);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        viewPager = findViewById(R.id.viewPager);
        pagerAdapter = new LearnPagerAdapter(this);
        // 可选：设置页面切换动画
        viewPager.setPageTransformer(new ZoomOutPageTransformer());
        viewPager.setAdapter(pagerAdapter);
    }


    @Override
    protected void onStart() {
        super.onStart();
        GlobalRtcVideo.getInstance().rtcVideo().startAudioCapture();
    }

    @Override
    protected void onStop() {
        super.onStop();
        GlobalRtcVideo.getInstance().rtcVideo().stopAudioCapture();
    }
}