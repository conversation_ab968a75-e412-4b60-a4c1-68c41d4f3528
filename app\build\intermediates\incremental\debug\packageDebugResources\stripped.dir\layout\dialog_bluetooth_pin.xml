<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/tv_device_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/bluetooth"
        android:textSize="18sp"
        android:textStyle="bold" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/bluetooth_enter_pin"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:paddingStart="4dp"
        android:paddingEnd="4dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_pin_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLength="6" />

    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/tv_pin_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="PIN码通常为4-6位数字"
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray" />

</LinearLayout>
