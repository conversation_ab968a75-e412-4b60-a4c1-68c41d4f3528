package com.srthinker.bbnice.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.NetworkInterface;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

/**
 * 设备唯一标识工具类
 * 根据CPU信息、网卡MAC地址和其他设备信息生成唯一标识
 */
public class DeviceIdUtils {
    private static final String TAG = "DeviceIdUtils";
    private static final String PREFS_FILE = "device_id.xml";
    private static final String PREFS_DEVICE_ID = "device_id";
    private static String uuid = "device_test88";

    /**
     * 获取设备唯一标识
     * @param context 上下文
     * @return 设备唯一标识
     */
    @SuppressLint("HardwareIds")
    public static synchronized String getDeviceId(Context context) {
        if (uuid == null) {
            // 首先从SharedPreferences获取
            SPUtils spUtils = SPUtils.getInstance();
            uuid = spUtils.getString(PREFS_DEVICE_ID, null);

            if (uuid == null) {
                // 生成新的设备ID
                final String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
                final String cpuInfo = getCpuInfo();
                final String macAddress = getMacAddress();
                final String serial = getSerialNumber();
                
                // 组合信息
                String deviceInfo = androidId + cpuInfo + macAddress + serial + Build.BOARD + Build.BRAND + 
                        Build.DEVICE + Build.HARDWARE + Build.MANUFACTURER + Build.MODEL + Build.PRODUCT;
                
                // 生成UUID
                uuid = generateSHA256(deviceInfo);
                
                // 保存到SharedPreferences
                spUtils.saveString(PREFS_DEVICE_ID, uuid);
            }
        }
        return uuid;
    }

    /**
     * 获取CPU信息
     * @return CPU信息字符串
     */
    private static String getCpuInfo() {
        StringBuilder sb = new StringBuilder();
        try {
            // 读取CPU信息
            BufferedReader br = new BufferedReader(new FileReader("/proc/cpuinfo"));
            String line;
            while ((line = br.readLine()) != null) {
                // 只获取处理器ID和型号信息
                if (line.contains("Processor") || line.contains("Hardware") || line.contains("Serial")) {
                    sb.append(line.trim()).append(";");
                }
            }
            br.close();
        } catch (IOException e) {
            Log.e(TAG, "Error reading CPU info: " + e.getMessage());
        }
        return sb.toString();
    }

    /**
     * 获取MAC地址
     * 适配Android 6.0及以上版本
     * @return MAC地址
     */
    @SuppressLint("HardwareIds")
    private static String getMacAddress() {
        try {
            // 对于Android 6.0及以上版本，需要通过NetworkInterface获取MAC地址
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface nif : interfaces) {
                // 不考虑回环接口
                if (!nif.getName().equalsIgnoreCase("wlan0")) continue;

                byte[] macBytes = nif.getHardwareAddress();
                if (macBytes == null) {
                    return "";
                }

                StringBuilder sb = new StringBuilder();
                for (byte b : macBytes) {
                    sb.append(String.format("%02X:", b));
                }

                if (sb.length() > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                }
                return sb.toString();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting MAC address: " + e.getMessage());
        }
        return "02:00:00:00:00:00"; // 默认MAC地址
    }

    /**
     * 获取设备序列号
     * @return 设备序列号
     */
    @SuppressLint("HardwareIds")
    private static String getSerialNumber() {
        String serial = null;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0及以上需要权限
                serial = Build.getSerial();
            } else {
                serial = Build.SERIAL;
            }
        } catch (SecurityException e) {
            Log.e(TAG, "Error getting serial number: " + e.getMessage());
        }
        return serial != null ? serial : "";
    }

    /**
     * 生成SHA-256哈希
     * @param input 输入字符串
     * @return 哈希后的字符串
     */
    private static String generateSHA256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "Error generating SHA-256: " + e.getMessage());
            // 如果SHA-256不可用，则使用UUID作为备选
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    /**
     * 重置设备ID
     * 清除保存的设备ID，下次调用getDeviceId时将生成新的ID
     * @param context 上下文
     */
    public static void resetDeviceId(Context context) {
        SPUtils.getInstance().remove(PREFS_DEVICE_ID);
        uuid = null;
    }
}
