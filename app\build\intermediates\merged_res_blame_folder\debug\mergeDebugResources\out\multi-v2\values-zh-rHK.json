{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,7813", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,7887"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "7892", "endColumns": "100", "endOffsets": "7988"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2443,2520,2594,2644,2695,2761,2825,2893,2968,3040,3101,3172,3239,3299,3387,3467,3530,3613,3698,3772,3837,3913,3961,4035,4099,4175,4253,4315,4379,4442,4508,4588,4668,4744,4825,4879,4934", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2438,2515,2589,2639,2690,2756,2820,2888,2963,3035,3096,3167,3234,3294,3382,3462,3525,3608,3693,3767,3832,3908,3956,4030,4094,4170,4248,4310,4374,4437,4503,4583,4663,4739,4820,4874,4929,4998"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3195,3285,3392,3465,3527,3605,3664,3722,3800,3861,3918,3974,4033,4091,4145,4231,4287,4345,4399,4464,4557,4631,4709,4829,4892,4955,5054,5131,5205,5255,5306,5372,5436,5504,5579,5651,5712,5783,5850,5910,5998,6078,6141,6224,6309,6383,6448,6524,6572,6646,6710,6786,6864,6926,6990,7053,7119,7199,7279,7355,7436,7490,7744", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "292,2916,2977,3044,3113,3190,3280,3387,3460,3522,3600,3659,3717,3795,3856,3913,3969,4028,4086,4140,4226,4282,4340,4394,4459,4552,4626,4704,4824,4887,4950,5049,5126,5200,5250,5301,5367,5431,5499,5574,5646,5707,5778,5845,5905,5993,6073,6136,6219,6304,6378,6443,6519,6567,6641,6705,6781,6859,6921,6985,7048,7114,7194,7274,7350,7431,7485,7540,7808"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\cff9521c234b1cb1e27a4b1f3461c77d\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,244", "endColumns": "48,46,92,69", "endOffsets": "99,146,239,309"}, "to": {"startLines": "105,106,107,108", "startColumns": "4,4,4,4", "startOffsets": "7993,8042,8089,8182", "endColumns": "48,46,92,69", "endOffsets": "8037,8084,8177,8247"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,99", "endOffsets": "149,249"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "7545,7644", "endColumns": "98,99", "endOffsets": "7639,7739"}}]}]}