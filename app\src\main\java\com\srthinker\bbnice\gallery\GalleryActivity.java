package com.srthinker.bbnice.gallery;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.srthinker.bbnice.R;

import java.util.List;

/**
 * 相册Activity，用于查看和管理照片和视频
 */
public class GalleryActivity extends AppCompatActivity implements MediaListFragment.OnMediaItemSelectedListener {
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final String[] PERMISSIONS_BELOW_33 = {
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };
    private static final String[] PERMISSIONS_33_AND_ABOVE = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO
    };

    private ViewPager2 viewPager;
    private TabLayout tabLayout;
    private ImageButton btnBack;
    private ImageButton btnSelectMode;
    private LinearLayout bottomActionBar;
    private Button btnSelectAll;
    private Button btnDelete;
    private ProgressBar progressBar;
    private TextView tvEmpty;
    private boolean isSelectMode = false;

    private MediaListFragment[] fragments = new MediaListFragment[3];

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gallery);

        // 初始化视图
        initViews();

        // 检查权限
        if (checkPermissions()) {
            setupViewPager();
        } else {
            requestPermissions();
        }
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        viewPager = findViewById(R.id.view_pager);
        tabLayout = findViewById(R.id.tab_layout);
        btnBack = findViewById(R.id.btn_back);
        btnSelectMode = findViewById(R.id.btn_select_mode);
        bottomActionBar = findViewById(R.id.bottom_action_bar);
        btnSelectAll = findViewById(R.id.btn_select_all);
        btnDelete = findViewById(R.id.btn_delete);
        progressBar = findViewById(R.id.progress_bar);
        tvEmpty = findViewById(R.id.tv_empty);

        // 设置返回按钮点击事件
        btnBack.setOnClickListener(v -> finish());

        // 设置选择模式按钮点击事件
        btnSelectMode.setOnClickListener(v -> toggleSelectMode());

        // 设置全选按钮点击事件
        btnSelectAll.setOnClickListener(v -> {
            MediaListFragment currentFragment = getCurrentFragment();
            if (currentFragment != null) {
                if (btnSelectAll.getText().toString().equals(getString(R.string.select_all))) {
                    currentFragment.selectAll();
                    btnSelectAll.setText(R.string.deselect_all);
                } else {
                    currentFragment.deselectAll();
                    btnSelectAll.setText(R.string.select_all);
                }
            }
        });

        // 设置删除按钮点击事件
        btnDelete.setOnClickListener(v -> {
            MediaListFragment currentFragment = getCurrentFragment();
            if (currentFragment != null) {
                List<MediaItem> selectedItems = currentFragment.getSelectedItems();
                if (selectedItems.isEmpty()) {
                    Toast.makeText(this, R.string.no_media_found, Toast.LENGTH_SHORT).show();
                    return;
                }

                // 显示确认对话框
                new AlertDialog.Builder(this)
                        .setTitle(R.string.delete)
                        .setMessage(getString(R.string.delete_confirm_count, selectedItems.size()))
                        .setPositiveButton(R.string.delete, (dialog, which) -> {
                            // 显示进度提示
                            Toast.makeText(this, R.string.deleting, Toast.LENGTH_SHORT).show();

                            // 在后台线程中执行删除操作
                            new Thread(() -> {
                                boolean success = currentFragment.deleteSelectedItems();

                                // 在UI线程中更新界面
                                runOnUiThread(() -> {
                                    if (success) {
                                        Toast.makeText(this, R.string.delete_success, Toast.LENGTH_SHORT).show();
                                    } else {
                                        Toast.makeText(this, R.string.delete_failed, Toast.LENGTH_SHORT).show();
                                    }
                                    toggleSelectMode(); // 退出选择模式
                                });
                            }).start();
                        })
                        .setNegativeButton(R.string.cancel, null)
                        .show();
            }
        });
    }

    /**
     * 设置ViewPager
     */
    private void setupViewPager() {
        // 创建适配器
        GalleryPagerAdapter adapter = new GalleryPagerAdapter(this);
        viewPager.setAdapter(adapter);

        // 将TabLayout与ViewPager关联
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case 0:
                    tab.setText(R.string.all_media);
                    break;
                case 1:
                    tab.setText(R.string.photos);
                    break;
                case 2:
                    tab.setText(R.string.videos);
                    break;
            }
        }).attach();
    }

    /**
     * 切换选择模式
     */
    private void toggleSelectMode() {
        isSelectMode = !isSelectMode;

        // 更新UI
        bottomActionBar.setVisibility(isSelectMode ? View.VISIBLE : View.GONE);
        btnSelectAll.setText(R.string.select_all);

        // 更新当前Fragment的选择模式
        MediaListFragment currentFragment = getCurrentFragment();
        if (currentFragment != null) {
            currentFragment.setSelectMode(isSelectMode);
        }
    }

    /**
     * 获取当前显示的Fragment
     * @return 当前Fragment
     */
    private MediaListFragment getCurrentFragment() {
        int position = viewPager.getCurrentItem();
        return fragments[position];
    }

    /**
     * 检查权限
     * @return 是否已授权
     */
    private boolean checkPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED
                    && ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_VIDEO) == PackageManager.PERMISSION_GRANTED;
        } else {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        }
    }

    /**
     * 请求权限
     */
    private void requestPermissions() {
        String[] permissions = Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
                ? PERMISSIONS_33_AND_ABOVE
                : PERMISSIONS_BELOW_33;

        ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                setupViewPager();
            } else {
                Toast.makeText(this, R.string.storage_permission_required, Toast.LENGTH_LONG).show();
                finish();
            }
        }
    }

    @Override
    public void onMediaItemSelected(MediaItem item) {
        // 打开详情查看
        if (item.isImage()) {
            getSupportFragmentManager().beginTransaction()
                    .add(android.R.id.content, PhotoDetailFragment.newInstance(item))
                    .addToBackStack(null)
                    .commit();
        } else if (item.isVideo()) {
            getSupportFragmentManager().beginTransaction()
                    .add(android.R.id.content, VideoDetailFragment.newInstance(item))
                    .addToBackStack(null)
                    .commit();
        }
    }

    /**
     * ViewPager适配器
     */
    private class GalleryPagerAdapter extends FragmentStateAdapter {
        public GalleryPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        @NonNull
        @Override
        public Fragment createFragment(int position) {
            MediaListFragment fragment;
            switch (position) {
                case 0:
                    fragment = MediaListFragment.newInstance(MediaListFragment.TYPE_ALL);
                    break;
                case 1:
                    fragment = MediaListFragment.newInstance(MediaListFragment.TYPE_IMAGES);
                    break;
                case 2:
                    fragment = MediaListFragment.newInstance(MediaListFragment.TYPE_VIDEOS);
                    break;
                default:
                    fragment = MediaListFragment.newInstance(MediaListFragment.TYPE_ALL);
                    break;
            }
            fragments[position] = fragment;
            return fragment;
        }

        @Override
        public int getItemCount() {
            return 3;
        }
    }
}
