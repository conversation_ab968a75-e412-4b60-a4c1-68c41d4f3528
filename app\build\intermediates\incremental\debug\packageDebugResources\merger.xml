<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res"><file name="background_call_button" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\background_call_button.xml" qualifiers="" type="drawable"/><file name="background_received_message" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\background_received_message.xml" qualifiers="" type="drawable"/><file name="background_sent_message" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\background_sent_message.xml" qualifiers="" type="drawable"/><file name="bubble_other" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\bubble_other.xml" qualifiers="" type="drawable"/><file name="bubble_self" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\bubble_self.xml" qualifiers="" type="drawable"/><file name="circle_shape" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\circle_shape.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="loadingbgn" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\loadingbgn.xml" qualifiers="" type="drawable"/><file name="activity_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_bluetooth.xml" qualifiers="" type="layout"/><file name="activity_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_call.xml" qualifiers="" type="layout"/><file name="activity_capturectivity" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_capturectivity.xml" qualifiers="" type="layout"/><file name="activity_chat" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_chat.xml" qualifiers="" type="layout"/><file name="activity_chat_cnactivity" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_chat_cnactivity.xml" qualifiers="" type="layout"/><file name="activity_config" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_config.xml" qualifiers="" type="layout"/><file name="activity_device_registration" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_device_registration.xml" qualifiers="" type="layout"/><file name="activity_display" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_display.xml" qualifiers="" type="layout"/><file name="activity_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_gallery.xml" qualifiers="" type="layout"/><file name="activity_home" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_home.xml" qualifiers="" type="layout"/><file name="activity_language_settings" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_language_settings.xml" qualifiers="" type="layout"/><file name="activity_learn" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_learn.xml" qualifiers="" type="layout"/><file name="activity_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_location.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_mobile_network" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_mobile_network.xml" qualifiers="" type="layout"/><file name="activity_recognition" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_recognition.xml" qualifiers="" type="layout"/><file name="activity_repository_test" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_repository_test.xml" qualifiers="" type="layout"/><file name="activity_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_setting.xml" qualifiers="" type="layout"/><file name="activity_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_voice.xml" qualifiers="" type="layout"/><file name="activity_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_wifi.xml" qualifiers="" type="layout"/><file name="call_fragment_page" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\call_fragment_page.xml" qualifiers="" type="layout"/><file name="dialog_bluetooth_pin" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\dialog_bluetooth_pin.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="dialog_wifi_password" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\dialog_wifi_password.xml" qualifiers="" type="layout"/><file name="fragment_media_list" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\fragment_media_list.xml" qualifiers="" type="layout"/><file name="fragment_photo_detail" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\fragment_photo_detail.xml" qualifiers="" type="layout"/><file name="fragment_video_detail" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\fragment_video_detail.xml" qualifiers="" type="layout"/><file name="home_list_item" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\home_list_item.xml" qualifiers="" type="layout"/><file name="item_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_bluetooth.xml" qualifiers="" type="layout"/><file name="item_media" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_media.xml" qualifiers="" type="layout"/><file name="item_message_received" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_message_received.xml" qualifiers="" type="layout"/><file name="item_message_sent" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_message_sent.xml" qualifiers="" type="layout"/><file name="item_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_wifi.xml" qualifiers="" type="layout"/><file name="learn_fragment_page" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\learn_fragment_page.xml" qualifiers="" type="layout"/><file name="setting_list_item" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\setting_list_item.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\arrow_back.png" qualifiers="hdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\btn_capture.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_accept.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_mianti_off.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_mute_off.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_mute_on.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_refuse.png" qualifiers="hdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\camera_switch.png" qualifiers="hdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\chat_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\dialog_loading_img.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_4g.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_about.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_bind.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_bluetooth.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_call.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_location.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_private.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_resume.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_shutdwon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_voice.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_voice_minus" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_voice_minus.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_voice_pluse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_voice_pluse.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_wifi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_wifi_lock.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\iocn_wifi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_da.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_gallery.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_ji.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_liao_en.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_liao_zh.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_pai.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_setting.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_shi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_xue.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_bg_baijuyi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_bg_dufu.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_bg_libai.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_icon_baijuyi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_icon_dufu.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_icon_libai.png" qualifiers="hdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\my_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\recognize_result.png" qualifiers="hdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\shibie_bg.png" qualifiers="hdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\talking_static.png" qualifiers="hdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\arrow_back.png" qualifiers="mdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\btn_capture.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_accept.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_mianti_off.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_mute_off.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_mute_on.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_refuse.png" qualifiers="mdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\camera_switch.png" qualifiers="mdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\chat_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\dialog_loading_img.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_4g.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_about.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_bind.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_bluetooth.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_call.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_location.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_private.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_resume.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_shutdwon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_voice.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_voice_minus" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_voice_minus.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_voice_pluse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_voice_pluse.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_wifi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_wifi_lock.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\iocn_wifi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_da.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_gallery.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_ji.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_liao_en.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_liao_zh.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_pai.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_setting.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_shi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_xue.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_bg_baijuyi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_bg_dufu.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_bg_libai.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_icon_baijuyi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_icon_dufu.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_icon_libai.png" qualifiers="mdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\my_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\recognize_result.png" qualifiers="mdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\shibie_bg.png" qualifiers="mdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\talking_static.png" qualifiers="mdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\arrow_back.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\btn_capture.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_accept.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_mianti_off.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_mute_off.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_mute_on.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_refuse.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\camera_switch.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\chat_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\dialog_loading_img.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_4g.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_about.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_bind.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_bluetooth.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_call.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_location.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_private.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_resume.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_shutdwon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_voice.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_voice_minus" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_voice_minus.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_voice_pluse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_voice_pluse.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_wifi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_wifi_lock.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\iocn_wifi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_da.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_gallery.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_ji.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_liao_en.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_liao_zh.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_pai.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_setting.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_shi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_xue.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_bg_baijuyi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_bg_dufu.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_bg_libai.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_icon_baijuyi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_icon_dufu.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_icon_libai.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\my_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\recognize_result.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\shibie_bg.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\talking_static.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\arrow_back.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\btn_capture.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_accept.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_mianti_off.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_mute_off.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_mute_on.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_refuse.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\camera_switch.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\chat_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\dialog_loading_img.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_4g.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_about.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_bind.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_bluetooth.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_call.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_location.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_private.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_resume.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_shutdwon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_voice.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_voice_minus" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_voice_minus.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_voice_pluse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_voice_pluse.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_wifi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_wifi_lock.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\iocn_wifi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_da.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_gallery.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_ji.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_liao_en.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_liao_zh.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_pai.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_setting.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_shi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_xue.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_bg_baijuyi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_bg_dufu.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_bg_libai.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_icon_baijuyi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_icon_dufu.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_icon_libai.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\my_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\recognize_result.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\shibie_bg.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\talking_static.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\arrow_back.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\btn_capture.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_accept.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_mianti_off.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_mute_off.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_mute_on.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_refuse.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\camera_switch.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\chat_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\dialog_loading_img.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_4g.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_about.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_bind.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_bluetooth.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_call.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_location.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_private.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_resume.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_shutdwon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_voice.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_voice_minus" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_voice_minus.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_voice_pluse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_voice_pluse.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_wifi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_wifi_lock.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\iocn_wifi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_da.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_gallery.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_ji.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_liao_en.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_liao_zh.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_pai.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_setting.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_shi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_xue.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_bg_baijuyi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_bg_dufu.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_bg_libai.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_icon_baijuyi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_icon_dufu.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_icon_libai.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\my_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\recognize_result.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\shibie_bg.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\talking_static.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color><color name="colorBackground">#F5F5F5</color><color name="colorTextPrimary">#212121</color><color name="colorTextSecondary">#757575</color><color name="colorDivider">#BDBDBD</color></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">BBNice</string><string name="error_network">Network Error</string><string name="error_unknown">Unknown Error</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="save">Save</string><string name="delete">Delete</string><string name="loading">Loading…</string><string name="retry">Retry</string><string name="refresh">Refresh</string><string name="settings">Settings</string><string name="apply">Apply</string><string name="unknown">Unknown</string><string name="language_settings">Language Settings</string><string name="system_language">System Language</string><string name="english">English</string><string name="chinese">Chinese</string><string name="language_changed">Language changed</string><string name="restart_app_message">Please restart the app for the changes to take effect</string><string name="login">Login</string><string name="register">Register</string><string name="scan_qr_code">Scan QR Code</string><string name="qr_code_expired">QR code has expired</string><string name="qr_code_valid_for">Valid for %1$d minutes</string><string name="device_id">Device ID</string><string name="bind_success">Device bound successfully</string><string name="bind_failed">Failed to bind device</string><string name="unbind_device">Unbind Device</string><string name="unbind_confirm">Are you sure you want to unbind this device?</string><string name="unbind_success">Device unbound successfully</string><string name="unbind_failed">Failed to unbind device</string><string name="home">Home</string><string name="chat">Chat</string><string name="chat_cn">Chinese Chat</string><string name="chat_en">English Chat</string><string name="api_test">API Test</string><string name="device_info">Device Info</string><string name="battery_level">Battery Level</string><string name="signal_strength">Signal Strength</string><string name="network_status">Network Status</string><string name="last_updated">Last Updated: %1$s</string><string name="press_to_talk">Press and hold to talk</string><string name="release_to_stop">Release to stop</string><string name="no_more_history">No more history</string><string name="chat_history">Chat History</string><string name="clear_history">Clear History</string><string name="clear_history_confirm">Are you sure you want to clear all chat history?</string><string name="history_cleared">Chat history cleared</string><string name="upload_history">Upload History</string><string name="upload_success">History uploaded successfully</string><string name="upload_failed">Failed to upload history</string><string name="location">Location</string><string name="current_location">Current Location</string><string name="location_permission_required">Location permission is required</string><string name="location_settings">Location Settings</string><string name="location_not_available">Location not available</string><string name="location_updated">Location updated</string><string name="permission_required">Permission Required</string><string name="permission_denied">Permission Denied</string><string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string><string name="go_to_settings">Go to Settings</string><string name="camera">Camera</string><string name="microphone">Microphone</string><string name="phone_state">Phone State</string><string name="storage">Storage</string><string name="network">Network</string><string name="gallery">Gallery</string><string name="photos">Photos</string><string name="videos">Videos</string><string name="all_media">All Media</string><string name="select_all">Select All</string><string name="deselect_all">Deselect All</string><string name="delete_selected">Delete Selected</string><string name="delete_confirm">Are you sure you want to delete the selected items?</string><string name="no_media_found">No media files found</string><string name="storage_permission_required">Storage permission is required to access media files</string><string name="play">Play</string><string name="pause">Pause</string><string name="loading_media">Loading media…</string><string name="error_connection">Connection error</string><string name="error_timeout">Request timed out</string><string name="error_server">Server error</string><string name="error_authentication">Authentication error</string><string name="error_invalid_input">Invalid input</string><string name="error_device_not_found">Device not found</string><string name="error_already_bound">Device already bound</string><string name="error_not_bound">Device not bound</string><string name="wifi_settings">WiFi Settings</string><string name="wifi">WiFi</string><string name="available_networks">Available Networks</string><string name="no_wifi_networks">No WiFi networks available</string><string name="scanning_wifi">Scanning for WiFi networks…</string><string name="scan_failed">Failed to scan WiFi networks</string><string name="wifi_connected">Connected</string><string name="wifi_disconnected">Not connected</string><string name="wifi_connecting">Connecting to %1$s</string><string name="wifi_connection_failed">Failed to connect to WiFi</string><string name="wifi_password">Password</string><string name="wifi_show_password">Show password</string><string name="wifi_connect">Connect</string><string name="wifi_enter_password">Enter WiFi Password</string><string name="wifi_password_empty">Please enter a password</string><string name="wifi_already_connected">Already connected to this network</string><string name="location_required">Location permission is required to scan WiFi networks</string><string name="location_service_required">Location service must be enabled to scan WiFi networks on Android 8.0+</string><string name="enable_location">Enable Location</string><string name="location_switch">Location</string><string name="voice_settings">Voice Settings</string><string name="voice">Voice</string><string name="volume_control">Volume Control</string><string name="mute_mode">Mute Mode</string><string name="mute_enabled">Mute mode is enabled</string><string name="mute_disabled">Mute mode is disabled</string><string name="volume_level">Volume Level: %1$d%%</string><string name="media_volume">Media Volume</string><string name="call_volume">Call Volume</string><string name="ring_volume">Ring Volume</string><string name="alarm_volume">Alarm Volume</string><string name="notification_volume">Notification Volume</string><string name="system_volume">System Volume</string><string name="volume_up">Volume Up</string><string name="volume_down">Volume Down</string><string name="enable_mute">Enable Mute</string><string name="disable_mute">Disable Mute</string><string name="audio_permission_required">Audio permission is required to control volume</string><string name="do_not_disturb_permission_required">Do Not Disturb permission is required to control mute mode</string><string name="go_to_notification_settings">Go to Notification Settings</string><string name="bluetooth_settings">Bluetooth Settings</string><string name="bluetooth">Bluetooth</string><string name="available_devices">Available Devices</string><string name="paired_devices">Paired Devices</string><string name="no_bluetooth_devices">No Bluetooth devices available</string><string name="scanning_bluetooth">Scanning for Bluetooth devices…</string><string name="bluetooth_scan_failed">Failed to scan Bluetooth devices</string><string name="bluetooth_connected">Connected</string><string name="bluetooth_disconnected">Not connected</string><string name="bluetooth_paired">Paired</string><string name="bluetooth_pairing">Pairing with %1$s</string><string name="bluetooth_connecting">Connecting to %1$s</string><string name="bluetooth_connection_failed">Failed to connect to device</string><string name="bluetooth_pairing_failed">Failed to pair with device</string><string name="bluetooth_pair">Pair</string><string name="bluetooth_unpair">Unpair</string><string name="bluetooth_connect">Connect</string><string name="bluetooth_disconnect">Disconnect</string><string name="bluetooth_enter_pin">Enter PIN Code</string><string name="bluetooth_pin_empty">Please enter a PIN code</string><string name="bluetooth_already_connected">Already connected to this device</string><string name="bluetooth_not_supported">Bluetooth is not supported on this device</string><string name="bluetooth_permission_required">Bluetooth permission is required</string><string name="enable_bluetooth">Enable Bluetooth</string><string name="bluetooth_device_name">Device Name: %1$s</string><string name="bluetooth_device_address">Device Address: %1$s</string><string name="bluetooth_device_type">Device Type: %1$s</string><string name="bluetooth_disabled">Bluetooth is disabled</string><string name="bluetooth_permissions_not_granted">Bluetooth permissions not granted</string><string name="bluetooth_scan_start_failed">Failed to start Bluetooth scan</string><string name="bluetooth_error_pairing">Error pairing device: %1$s</string><string name="bluetooth_error_unpairing">Error unpairing device: %1$s</string><string name="bluetooth_error_connecting">Failed to connect to device: %1$s</string><string name="bluetooth_error_disconnecting">Failed to disconnect device: %1$s</string><string name="bluetooth_initiate_pairing_failed">Failed to initiate pairing with device</string><string name="bluetooth_unpair_failed">Failed to unpair device</string><string name="bluetooth_gatt_not_supported">GATT is not supported on current Android version</string><string name="bluetooth_a2dp_profile_unavailable">A2DP profile is not available</string><string name="bluetooth_headset_profile_unavailable">HEADSET profile is not available</string><string name="bluetooth_a2dp_connect_system_settings">A2DP connection needs to be completed in system Bluetooth settings. Please connect this device in system settings.</string><string name="bluetooth_headset_connect_system_settings">HEADSET connection needs to be completed in system Bluetooth settings. Please connect this device in system settings.</string><string name="bluetooth_a2dp_disconnect_system_settings">A2DP disconnection needs to be completed in system Bluetooth settings. Please disconnect this device in system settings.</string><string name="bluetooth_headset_disconnect_system_settings">HEADSET disconnection needs to be completed in system Bluetooth settings. Please disconnect this device in system settings.</string><string name="bluetooth_error_get_device_uuid">Failed to get device UUID: %1$s</string><string name="bluetooth_error_socket_close">Failed to close socket: %1$s</string><string name="bluetooth_error_a2dp_devices">Error getting A2DP connected devices: %1$s</string><string name="bluetooth_error_headset_devices">Error getting HEADSET connected devices: %1$s</string><string name="bluetooth_error_profile_proxy">Error getting profile proxy: %1$s</string><string name="bluetooth_error_close_a2dp_proxy">Failed to close A2DP proxy: %1$s</string><string name="bluetooth_error_close_headset_proxy">Failed to close HEADSET proxy: %1$s</string><string name="bluetooth_error_check_connected">Error checking if device is connected: %1$s</string><string name="bluetooth_error_get_connected_devices">Error getting connected devices: %1$s</string><string name="mobile_network_settings">Mobile Network Settings</string><string name="mobile_network">Mobile Network</string><string name="mobile_data">Mobile Data</string><string name="mobile_data_enabled">Mobile data is enabled</string><string name="mobile_data_disabled">Mobile data is disabled</string><string name="enable_mobile_data">Enable Mobile Data</string><string name="disable_mobile_data">Disable Mobile Data</string><string name="mobile_network_info">Mobile Network Information</string><string name="network_type">Network Type: %1$s</string><string name="network_operator">Network Operator: %1$s</string><string name="roaming">Roaming: %1$s</string><string name="roaming_enabled">Yes</string><string name="roaming_disabled">No</string><string name="data_roaming">Data Roaming</string><string name="data_roaming_description">Allow data usage when roaming</string><string name="mobile_network_not_available">Mobile network is not available</string><string name="mobile_network_permission_required">Phone state permission is required to access mobile network settings</string><string name="mobile_network_permission_denied">Cannot access mobile network settings without required permissions</string><string name="mobile_network_error">Error accessing mobile network settings</string><string name="mobile_network_status">Status: %1$s</string><string name="mobile_network_connected">Connected</string><string name="mobile_network_disconnected">Disconnected</string><string name="screen_luminance">Screen Luminance</string><string name="display">Display</string><string name="second">secs</string><string name="wake_up_duration">Wake Up Duration</string><string name="screen_wake_up">Screen Wake Up</string><string name="go_to_settings_permission">Go to Settings Permission</string><string name="brightness_down">Brightness Down</string><string name="brightness_up">Brightness Up</string><string name="brightness_permission_required">Write settings permission is required to control brightness</string><string name="auto_brightness">Auto Brightness</string><string name="brightness_level">Brightness Level: %1$d%%</string><string name="auto_brightness_enabled">Auto brightness is enabled</string><string name="auto_brightness_disabled">Auto brightness is disabled</string></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\styles.xml" qualifiers=""><style name="MyDialogStyle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.BBNice" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.BBNice" parent="Base.Theme.BBNice"/></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">BBNice</string><string name="error_network">Network Error</string><string name="error_unknown">Unknown Error</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="save">Save</string><string name="delete">Delete</string><string name="loading">Loading…</string><string name="retry">Retry</string><string name="refresh">Refresh</string><string name="settings">Settings</string><string name="apply">Apply</string><string name="unknown">Unknown</string><string name="language_settings">Language Settings</string><string name="system_language">System Language</string><string name="english">English</string><string name="chinese">Chinese</string><string name="language_changed">Language changed</string><string name="restart_app_message">Please restart the app for the changes to take effect</string><string name="login">Login</string><string name="register">Register</string><string name="scan_qr_code">Scan QR Code</string><string name="qr_code_expired">QR code has expired</string><string name="qr_code_valid_for">Valid for %1$d minutes</string><string name="device_id">Device ID</string><string name="bind_success">Device bound successfully</string><string name="bind_failed">Failed to bind device</string><string name="unbind_device">Unbind Device</string><string name="unbind_confirm">Are you sure you want to unbind this device?</string><string name="unbind_success">Device unbound successfully</string><string name="unbind_failed">Failed to unbind device</string><string name="home">Home</string><string name="chat">Chat</string><string name="chat_cn">Chinese Chat</string><string name="chat_en">English Chat</string><string name="api_test">API Test</string><string name="device_info">Device Info</string><string name="battery_level">Battery Level</string><string name="signal_strength">Signal Strength</string><string name="network_status">Network Status</string><string name="last_updated">Last Updated: %1$s</string><string name="press_to_talk">Press and hold to talk</string><string name="release_to_stop">Release to stop</string><string name="no_more_history">No more history</string><string name="chat_history">Chat History</string><string name="clear_history">Clear History</string><string name="clear_history_confirm">Are you sure you want to clear all chat history?</string><string name="history_cleared">Chat history cleared</string><string name="upload_history">Upload History</string><string name="upload_success">History uploaded successfully</string><string name="upload_failed">Failed to upload history</string><string name="location">Location</string><string name="current_location">Current Location</string><string name="location_permission_required">Location permission is required</string><string name="location_settings">Location Settings</string><string name="location_not_available">Location not available</string><string name="location_updated">Location updated</string><string name="permission_denied">Permission Denied</string><string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string><string name="go_to_settings">Go to Settings</string><string name="camera">Camera</string><string name="microphone">Microphone</string><string name="phone_state">Phone State</string><string name="storage">Storage</string><string name="network">Network</string><string name="gallery">Gallery</string><string name="photos">Photos</string><string name="videos">Videos</string><string name="all_media">All Media</string><string name="select_all">Select All</string><string name="deselect_all">Deselect All</string><string name="delete_selected">Delete Selected</string><string name="delete_confirm">Are you sure you want to delete the selected items?</string><string name="no_media_found">No media files found</string><string name="storage_permission_required">Storage permission is required to access media files</string><string name="play">Play</string><string name="pause">Pause</string><string name="loading_media">Loading media…</string><string name="error_connection">Connection error</string><string name="error_timeout">Request timed out</string><string name="error_server">Server error</string><string name="error_authentication">Authentication error</string><string name="error_invalid_input">Invalid input</string><string name="error_device_not_found">Device not found</string><string name="error_already_bound">Device already bound</string><string name="error_not_bound">Device not bound</string><string name="wifi_settings">WiFi Settings</string><string name="wifi">WiFi</string><string name="available_networks">Available Networks</string><string name="no_wifi_networks">No WiFi networks available</string><string name="scanning_wifi">Scanning for WiFi networks…</string><string name="scan_failed">Failed to scan WiFi networks</string><string name="wifi_connected">Connected</string><string name="wifi_disconnected">Not connected</string><string name="wifi_connecting">Connecting to %1$s</string><string name="wifi_connection_failed">Failed to connect to WiFi</string><string name="wifi_password">Password</string><string name="wifi_show_password">Show password</string><string name="wifi_connect">Connect</string><string name="wifi_enter_password">Enter WiFi Password</string><string name="wifi_password_empty">Please enter a password</string><string name="wifi_already_connected">Already connected to this network</string><string name="location_required">Location permission is required to scan WiFi networks</string><string name="location_service_required">Location service must be enabled to scan WiFi networks on Android 8.0+</string><string name="enable_location">Enable Location</string><string name="location_switch">Location</string><string name="voice_settings">Voice Settings</string><string name="voice">Voice</string><string name="volume_control">Volume Control</string><string name="mute_mode">Mute Mode</string><string name="mute_enabled">Mute mode is enabled</string><string name="mute_disabled">Mute mode is disabled</string><string name="volume_level">Volume Level: %1$d%%</string><string name="media_volume">Media Volume</string><string name="call_volume">Call Volume</string><string name="ring_volume">Ring Volume</string><string name="alarm_volume">Alarm Volume</string><string name="notification_volume">Notification Volume</string><string name="system_volume">System Volume</string><string name="volume_up">Volume Up</string><string name="volume_down">Volume Down</string><string name="enable_mute">Enable Mute</string><string name="disable_mute">Disable Mute</string><string name="audio_permission_required">Audio permission is required to control volume</string><string name="do_not_disturb_permission_required">Do Not Disturb permission is required to control mute mode</string><string name="go_to_notification_settings">Go to Notification Settings</string><string name="permission_required">Permission Required</string><string name="bluetooth_settings">Bluetooth Settings</string><string name="bluetooth">Bluetooth</string><string name="available_devices">Available Devices</string><string name="paired_devices">Paired Devices</string><string name="no_bluetooth_devices">No Bluetooth devices available</string><string name="scanning_bluetooth">Scanning for Bluetooth devices…</string><string name="bluetooth_scan_failed">Failed to scan Bluetooth devices</string><string name="bluetooth_connected">Connected</string><string name="bluetooth_disconnected">Not connected</string><string name="bluetooth_paired">Paired</string><string name="bluetooth_pairing">Pairing with %1$s</string><string name="bluetooth_connecting">Connecting to %1$s</string><string name="bluetooth_connection_failed">Failed to connect to device</string><string name="bluetooth_pairing_failed">Failed to pair with device</string><string name="bluetooth_pair">Pair</string><string name="bluetooth_unpair">Unpair</string><string name="bluetooth_connect">Connect</string><string name="bluetooth_disconnect">Disconnect</string><string name="bluetooth_enter_pin">Enter PIN Code</string><string name="bluetooth_pin_empty">Please enter a PIN code</string><string name="bluetooth_already_connected">Already connected to this device</string><string name="bluetooth_not_supported">Bluetooth is not supported on this device</string><string name="bluetooth_permission_required">Bluetooth permission is required</string><string name="enable_bluetooth">Enable Bluetooth</string><string name="bluetooth_device_name">Device Name: %1$s</string><string name="bluetooth_device_address">Device Address: %1$s</string><string name="bluetooth_device_type">Device Type: %1$s</string><string name="bluetooth_disabled">Bluetooth is disabled</string><string name="bluetooth_permissions_not_granted">Bluetooth permissions not granted</string><string name="bluetooth_scan_start_failed">Failed to start Bluetooth scan</string><string name="bluetooth_error_pairing">Error pairing device: %1$s</string><string name="bluetooth_error_unpairing">Error unpairing device: %1$s</string><string name="bluetooth_error_connecting">Failed to connect to device: %1$s</string><string name="bluetooth_error_disconnecting">Failed to disconnect device: %1$s</string><string name="bluetooth_initiate_pairing_failed">Failed to initiate pairing with device</string><string name="bluetooth_unpair_failed">Failed to unpair device</string><string name="bluetooth_gatt_not_supported">GATT is not supported on current Android version</string><string name="bluetooth_a2dp_profile_unavailable">A2DP profile is not available</string><string name="bluetooth_headset_profile_unavailable">HEADSET profile is not available</string><string name="bluetooth_a2dp_connect_system_settings">A2DP connection needs to be completed in system Bluetooth settings. Please connect this device in system settings.</string><string name="bluetooth_headset_connect_system_settings">HEADSET connection needs to be completed in system Bluetooth settings. Please connect this device in system settings.</string><string name="bluetooth_a2dp_disconnect_system_settings">A2DP disconnection needs to be completed in system Bluetooth settings. Please disconnect this device in system settings.</string><string name="bluetooth_headset_disconnect_system_settings">HEADSET disconnection needs to be completed in system Bluetooth settings. Please disconnect this device in system settings.</string><string name="bluetooth_error_get_device_uuid">Failed to get device UUID: %1$s</string><string name="bluetooth_error_socket_close">Failed to close socket: %1$s</string><string name="bluetooth_error_a2dp_devices">Error getting A2DP connected devices: %1$s</string><string name="bluetooth_error_headset_devices">Error getting HEADSET connected devices: %1$s</string><string name="bluetooth_error_profile_proxy">Error getting profile proxy: %1$s</string><string name="bluetooth_error_close_a2dp_proxy">Failed to close A2DP proxy: %1$s</string><string name="bluetooth_error_close_headset_proxy">Failed to close HEADSET proxy: %1$s</string><string name="bluetooth_error_check_connected">Error checking if device is connected: %1$s</string><string name="bluetooth_error_get_connected_devices">Error getting connected devices: %1$s</string><string name="mobile_network_settings">Mobile Network Settings</string><string name="mobile_network">Mobile Network</string><string name="mobile_data">Mobile Data</string><string name="mobile_data_enabled">Mobile data is enabled</string><string name="mobile_data_disabled">Mobile data is disabled</string><string name="enable_mobile_data">Enable Mobile Data</string><string name="disable_mobile_data">Disable Mobile Data</string><string name="mobile_network_info">Mobile Network Information</string><string name="network_type">Network Type: %1$s</string><string name="network_operator">Network Operator: %1$s</string><string name="roaming">Roaming: %1$s</string><string name="roaming_enabled">Yes</string><string name="roaming_disabled">No</string><string name="data_roaming">Data Roaming</string><string name="data_roaming_description">Allow data usage when roaming</string><string name="mobile_network_not_available">Mobile network is not available</string><string name="mobile_network_permission_required">Phone state permission is required to access mobile network settings</string><string name="mobile_network_permission_denied">Cannot access mobile network settings without required permissions</string><string name="mobile_network_error">Error accessing mobile network settings</string><string name="mobile_network_status">Status: %1$s</string><string name="mobile_network_connected">Connected</string><string name="mobile_network_disconnected">Disconnected</string><string name="display">Display</string><string name="screen_luminance">Screen Luminance</string><string name="second">secs</string><string name="wake_up_duration">Wake Up Duration</string><string name="screen_wake_up">Screen Wake Up</string></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.BBNice" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-zh\strings.xml" qualifiers="zh"><string name="app_name">BBNice</string><string name="error_network">网络错误</string><string name="error_unknown">未知错误</string><string name="ok">确定</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="save">保存</string><string name="delete">删除</string><string name="loading">加载中…</string><string name="retry">重试</string><string name="refresh">刷新</string><string name="settings">设置</string><string name="apply">应用</string><string name="unknown">未知</string><string name="language_settings">语言设置</string><string name="system_language">系统语言</string><string name="english">英语</string><string name="chinese">中文</string><string name="language_changed">语言已更改</string><string name="restart_app_message">请重启应用以使更改生效</string><string name="login">登录</string><string name="register">注册</string><string name="scan_qr_code">扫描二维码</string><string name="qr_code_expired">二维码已过期</string><string name="qr_code_valid_for">有效期 %1$d 分钟</string><string name="device_id">设备ID</string><string name="bind_success">设备绑定成功</string><string name="bind_failed">设备绑定失败</string><string name="unbind_device">解绑设备</string><string name="unbind_confirm">确定要解绑此设备吗？</string><string name="unbind_success">设备解绑成功</string><string name="unbind_failed">设备解绑失败</string><string name="home">首页</string><string name="chat">聊天</string><string name="chat_cn">中文聊天</string><string name="chat_en">英文聊天</string><string name="api_test">接口测试</string><string name="device_info">设备信息</string><string name="battery_level">电量</string><string name="signal_strength">信号强度</string><string name="network_status">网络状态</string><string name="last_updated">最后更新: %1$s</string><string name="press_to_talk">按住说话</string><string name="release_to_stop">松开结束</string><string name="no_more_history">没有更多历史记录</string><string name="chat_history">聊天记录</string><string name="clear_history">清空历史</string><string name="clear_history_confirm">确定要清空所有聊天记录吗？</string><string name="history_cleared">聊天记录已清空</string><string name="upload_history">上传历史</string><string name="upload_success">历史上传成功</string><string name="upload_failed">历史上传失败</string><string name="location">位置</string><string name="current_location">当前位置</string><string name="location_permission_required">需要位置权限</string><string name="location_settings">位置设置</string><string name="location_not_available">位置不可用</string><string name="location_updated">位置已更新</string><string name="permission_required">需要权限</string><string name="permission_denied">权限被拒绝</string><string name="permission_denied_message">此功能需要已被拒绝的权限。请在设置中启用它们。</string><string name="go_to_settings">前往设置</string><string name="camera">相机</string><string name="microphone">麦克风</string><string name="phone_state">电话状态</string><string name="storage">存储</string><string name="network">网络</string><string name="error_connection">连接错误</string><string name="error_timeout">请求超时</string><string name="error_server">服务器错误</string><string name="error_authentication">认证错误</string><string name="error_invalid_input">无效输入</string><string name="error_device_not_found">设备未找到</string><string name="error_already_bound">设备已绑定</string><string name="error_not_bound">设备未绑定</string><string name="gallery">相册</string><string name="photos">照片</string><string name="videos">视频</string><string name="all_media">所有媒体</string><string name="select_all">全选</string><string name="deselect_all">取消全选</string><string name="delete_selected">删除所选</string><string name="delete_confirm">确定要删除所选项目吗？</string><string name="no_media_found">未找到媒体文件</string><string name="storage_permission_required">需要存储权限才能访问媒体文件</string><string name="play">播放</string><string name="pause">暂停</string><string name="loading_media">加载媒体中…</string><string name="wifi_settings">WiFi设置</string><string name="wifi">WiFi</string><string name="available_networks">可用网络</string><string name="no_wifi_networks">没有可用的WiFi网络</string><string name="scanning_wifi">正在扫描WiFi网络…</string><string name="scan_failed">扫描WiFi网络失败</string><string name="wifi_connected">已连接</string><string name="wifi_disconnected">未连接</string><string name="wifi_connecting">正在连接到 %1$s</string><string name="wifi_connection_failed">连接WiFi失败</string><string name="wifi_password">密码</string><string name="wifi_show_password">显示密码</string><string name="wifi_connect">连接</string><string name="wifi_enter_password">输入WiFi密码</string><string name="wifi_password_empty">请输入密码</string><string name="wifi_already_connected">已连接到该网络</string><string name="location_required">需要位置权限才能扫描WiFi网络</string><string name="location_service_required">Android 8.0及以上版本需要开启位置服务才能扫描WiFi网络</string><string name="enable_location">开启位置服务</string><string name="location_switch">定位</string><string name="voice_settings">音量设置</string><string name="voice">音量</string><string name="volume_control">音量控制</string><string name="mute_mode">静音模式</string><string name="mute_enabled">静音模式已开启</string><string name="mute_disabled">静音模式已关闭</string><string name="volume_level">音量大小: %1$d%%</string><string name="media_volume">媒体音量</string><string name="call_volume">通话音量</string><string name="ring_volume">铃声音量</string><string name="alarm_volume">闹钟音量</string><string name="notification_volume">通知音量</string><string name="system_volume">系统音量</string><string name="volume_up">增大音量</string><string name="volume_down">减小音量</string><string name="enable_mute">开启静音</string><string name="disable_mute">关闭静音</string><string name="audio_permission_required">需要音频权限才能控制音量</string><string name="do_not_disturb_permission_required">需要勿扰模式权限才能控制静音模式</string><string name="go_to_notification_settings">前往通知设置</string><string name="bluetooth_settings">蓝牙设置</string><string name="bluetooth">蓝牙</string><string name="available_devices">可用设备</string><string name="paired_devices">已配对设备</string><string name="no_bluetooth_devices">没有可用的蓝牙设备</string><string name="scanning_bluetooth">正在扫描蓝牙设备…</string><string name="bluetooth_scan_failed">扫描蓝牙设备失败</string><string name="bluetooth_connected">已连接</string><string name="bluetooth_disconnected">未连接</string><string name="bluetooth_paired">已配对</string><string name="bluetooth_pairing">正在与 %1$s 配对</string><string name="bluetooth_connecting">正在连接到 %1$s</string><string name="bluetooth_connection_failed">连接设备失败</string><string name="bluetooth_pairing_failed">配对设备失败</string><string name="bluetooth_pair">配对</string><string name="bluetooth_unpair">取消配对</string><string name="bluetooth_connect">连接</string><string name="bluetooth_disconnect">断开连接</string><string name="bluetooth_enter_pin">输入PIN码</string><string name="bluetooth_pin_empty">请输入PIN码</string><string name="bluetooth_already_connected">已连接到该设备</string><string name="bluetooth_not_supported">此设备不支持蓝牙</string><string name="bluetooth_permission_required">需要蓝牙权限</string><string name="enable_bluetooth">开启蓝牙</string><string name="bluetooth_device_name">设备名称: %1$s</string><string name="bluetooth_device_address">设备地址: %1$s</string><string name="bluetooth_device_type">设备类型: %1$s</string><string name="bluetooth_disabled">蓝牙已禁用</string><string name="bluetooth_permissions_not_granted">未授予蓝牙权限</string><string name="bluetooth_scan_start_failed">启动蓝牙扫描失败</string><string name="bluetooth_error_pairing">配对设备错误: %1$s</string><string name="bluetooth_error_unpairing">取消配对设备错误: %1$s</string><string name="bluetooth_error_connecting">连接设备失败: %1$s</string><string name="bluetooth_error_disconnecting">断开设备连接失败: %1$s</string><string name="bluetooth_initiate_pairing_failed">启动与设备配对失败</string><string name="bluetooth_unpair_failed">取消配对设备失败</string><string name="bluetooth_gatt_not_supported">当前Android版本不支持GATT</string><string name="bluetooth_a2dp_profile_unavailable">A2DP配置文件不可用</string><string name="bluetooth_headset_profile_unavailable">HEADSET配置文件不可用</string><string name="bluetooth_a2dp_connect_system_settings">A2DP连接需要在系统蓝牙设置中完成。请在系统设置中连接此设备。</string><string name="bluetooth_headset_connect_system_settings">HEADSET连接需要在系统蓝牙设置中完成。请在系统设置中连接此设备。</string><string name="bluetooth_a2dp_disconnect_system_settings">A2DP断开连接需要在系统蓝牙设置中完成。请在系统设置中断开此设备。</string><string name="bluetooth_headset_disconnect_system_settings">HEADSET断开连接需要在系统蓝牙设置中完成。请在系统设置中断开此设备。</string><string name="bluetooth_error_get_device_uuid">获取设备UUID失败: %1$s</string><string name="bluetooth_error_socket_close">关闭Socket失败: %1$s</string><string name="bluetooth_error_a2dp_devices">获取A2DP连接设备错误: %1$s</string><string name="bluetooth_error_headset_devices">获取HEADSET连接设备错误: %1$s</string><string name="bluetooth_error_profile_proxy">获取配置文件代理错误: %1$s</string><string name="bluetooth_error_close_a2dp_proxy">关闭A2DP代理失败: %1$s</string><string name="bluetooth_error_close_headset_proxy">关闭HEADSET代理失败: %1$s</string><string name="bluetooth_error_check_connected">检查设备是否已连接错误: %1$s</string><string name="bluetooth_error_get_connected_devices">获取已连接设备错误: %1$s</string><string name="mobile_network_settings">移动网络设置</string><string name="mobile_network">移动网络</string><string name="mobile_data">移动数据</string><string name="mobile_data_enabled">移动数据已开启</string><string name="mobile_data_disabled">移动数据已关闭</string><string name="enable_mobile_data">开启移动数据</string><string name="disable_mobile_data">关闭移动数据</string><string name="mobile_network_info">移动网络信息</string><string name="network_type">网络类型: %1$s</string><string name="network_operator">网络运营商: %1$s</string><string name="roaming">漫游: %1$s</string><string name="roaming_enabled">是</string><string name="roaming_disabled">否</string><string name="data_roaming">数据漫游</string><string name="data_roaming_description">允许漫游时使用数据</string><string name="mobile_network_not_available">移动网络不可用</string><string name="mobile_network_permission_required">需要电话状态权限才能访问移动网络设置</string><string name="mobile_network_permission_denied">没有所需权限，无法访问移动网络设置</string><string name="mobile_network_error">访问移动网络设置时出错</string><string name="mobile_network_status">状态: %1$s</string><string name="mobile_network_connected">已连接</string><string name="mobile_network_disconnected">未连接</string><string name="display">显示</string><string name="screen_luminance">屏幕亮度</string><string name="second">秒</string><string name="wake_up_duration">唤醒时长</string><string name="screen_wake_up">点击屏幕唤醒</string><string name="auto_brightness_disabled">自动亮度已关闭</string><string name="brightness_permission_required">需要修改系统设置权限才能控制亮度</string><string name="brightness_up">增加亮度</string><string name="brightness_level">亮度级别: %1$d%%</string><string name="brightness_down">降低亮度</string><string name="auto_brightness">自动亮度</string><string name="auto_brightness_enabled">自动亮度已开启</string><string name="go_to_settings_permission">前往设置权限</string></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-zh-rCN\strings.xml" qualifiers="zh-rCN"><string name="app_name">BBNice</string><string name="error_network">网络错误</string><string name="error_unknown">未知错误</string><string name="ok">确定</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="save">保存</string><string name="delete">删除</string><string name="loading">加载中…</string><string name="retry">重试</string><string name="refresh">刷新</string><string name="settings">设置</string><string name="apply">应用</string><string name="unknown">未知</string><string name="language_settings">语言设置</string><string name="system_language">系统语言</string><string name="english">英语</string><string name="chinese">中文</string><string name="language_changed">语言已更改</string><string name="restart_app_message">请重启应用以使更改生效</string><string name="login">登录</string><string name="register">注册</string><string name="scan_qr_code">扫描二维码</string><string name="qr_code_expired">二维码已过期</string><string name="qr_code_valid_for">有效期 %1$d 分钟</string><string name="device_id">设备ID</string><string name="bind_success">设备绑定成功</string><string name="bind_failed">设备绑定失败</string><string name="unbind_device">解绑设备</string><string name="unbind_confirm">确定要解绑此设备吗？</string><string name="unbind_success">设备解绑成功</string><string name="unbind_failed">设备解绑失败</string><string name="home">首页</string><string name="chat">聊天</string><string name="chat_cn">中文聊天</string><string name="chat_en">英文聊天</string><string name="api_test">接口测试</string><string name="device_info">设备信息</string><string name="battery_level">电量</string><string name="signal_strength">信号强度</string><string name="network_status">网络状态</string><string name="last_updated">最后更新: %1$s</string><string name="press_to_talk">按住说话</string><string name="release_to_stop">松开结束</string><string name="no_more_history">没有更多历史记录</string><string name="chat_history">聊天记录</string><string name="clear_history">清空历史</string><string name="clear_history_confirm">确定要清空所有聊天记录吗？</string><string name="history_cleared">聊天记录已清空</string><string name="upload_history">上传历史</string><string name="upload_success">历史上传成功</string><string name="upload_failed">历史上传失败</string><string name="location">位置</string><string name="current_location">当前位置</string><string name="location_permission_required">需要位置权限</string><string name="location_settings">位置设置</string><string name="location_not_available">位置不可用</string><string name="location_updated">位置已更新</string><string name="permission_required">需要权限</string><string name="permission_denied">权限被拒绝</string><string name="permission_denied_message">此功能需要已被拒绝的权限。请在设置中启用它们。</string><string name="go_to_settings">前往设置</string><string name="camera">相机</string><string name="microphone">麦克风</string><string name="phone_state">电话状态</string><string name="storage">存储</string><string name="network">网络</string><string name="error_connection">连接错误</string><string name="error_timeout">请求超时</string><string name="error_server">服务器错误</string><string name="error_authentication">认证错误</string><string name="error_invalid_input">无效输入</string><string name="error_device_not_found">设备未找到</string><string name="error_already_bound">设备已绑定</string><string name="error_not_bound">设备未绑定</string><string name="gallery">相册</string><string name="photos">照片</string><string name="videos">视频</string><string name="all_media">所有媒体</string><string name="select_all">全选</string><string name="deselect_all">取消全选</string><string name="delete_selected">删除所选</string><string name="delete_confirm">确定要删除所选项目吗？</string><string name="no_media_found">未找到媒体文件</string><string name="storage_permission_required">需要存储权限才能访问媒体文件</string><string name="play">播放</string><string name="pause">暂停</string><string name="loading_media">加载媒体中…</string><string name="wifi_settings">WiFi设置</string><string name="wifi">WiFi</string><string name="available_networks">可用网络</string><string name="no_wifi_networks">没有可用的WiFi网络</string><string name="scanning_wifi">正在扫描WiFi网络…</string><string name="scan_failed">扫描WiFi网络失败</string><string name="wifi_connected">已连接</string><string name="wifi_disconnected">未连接</string><string name="wifi_connecting">正在连接到 %1$s</string><string name="wifi_connection_failed">连接WiFi失败</string><string name="wifi_password">密码</string><string name="wifi_show_password">显示密码</string><string name="wifi_connect">连接</string><string name="wifi_enter_password">输入WiFi密码</string><string name="wifi_password_empty">请输入密码</string><string name="wifi_already_connected">已连接到该网络</string><string name="location_required">需要位置权限才能扫描WiFi网络</string><string name="location_service_required">Android 8.0及以上版本需要开启位置服务才能扫描WiFi网络</string><string name="enable_location">开启位置服务</string><string name="location_switch">定位</string><string name="bluetooth_settings">蓝牙设置</string><string name="bluetooth">蓝牙</string><string name="available_devices">可用设备</string><string name="paired_devices">已配对设备</string><string name="no_bluetooth_devices">没有可用的蓝牙设备</string><string name="scanning_bluetooth">正在扫描蓝牙设备…</string><string name="bluetooth_scan_failed">扫描蓝牙设备失败</string><string name="bluetooth_connected">已连接</string><string name="bluetooth_disconnected">未连接</string><string name="bluetooth_paired">已配对</string><string name="bluetooth_pairing">正在与 %1$s 配对</string><string name="bluetooth_connecting">正在连接到 %1$s</string><string name="bluetooth_connection_failed">连接设备失败</string><string name="bluetooth_pairing_failed">配对设备失败</string><string name="bluetooth_pair">配对</string><string name="bluetooth_unpair">取消配对</string><string name="bluetooth_connect">连接</string><string name="bluetooth_disconnect">断开连接</string><string name="bluetooth_enter_pin">输入PIN码</string><string name="bluetooth_pin_empty">请输入PIN码</string><string name="bluetooth_already_connected">已连接到该设备</string><string name="bluetooth_not_supported">此设备不支持蓝牙</string><string name="bluetooth_permission_required">需要蓝牙权限</string><string name="enable_bluetooth">开启蓝牙</string><string name="bluetooth_device_name">设备名称: %1$s</string><string name="bluetooth_device_address">设备地址: %1$s</string><string name="bluetooth_device_type">设备类型: %1$s</string><string name="bluetooth_disabled">蓝牙已禁用</string><string name="bluetooth_permissions_not_granted">未授予蓝牙权限</string><string name="bluetooth_scan_start_failed">启动蓝牙扫描失败</string><string name="bluetooth_error_pairing">配对设备错误: %1$s</string><string name="bluetooth_error_unpairing">取消配对设备错误: %1$s</string><string name="bluetooth_error_connecting">连接设备失败: %1$s</string><string name="bluetooth_error_disconnecting">断开设备连接失败: %1$s</string><string name="bluetooth_initiate_pairing_failed">启动与设备配对失败</string><string name="bluetooth_unpair_failed">取消配对设备失败</string><string name="bluetooth_gatt_not_supported">当前Android版本不支持GATT</string><string name="bluetooth_a2dp_profile_unavailable">A2DP配置文件不可用</string><string name="bluetooth_headset_profile_unavailable">HEADSET配置文件不可用</string><string name="bluetooth_a2dp_connect_system_settings">A2DP连接需要在系统蓝牙设置中完成。请在系统设置中连接此设备。</string><string name="bluetooth_headset_connect_system_settings">HEADSET连接需要在系统蓝牙设置中完成。请在系统设置中连接此设备。</string><string name="bluetooth_a2dp_disconnect_system_settings">A2DP断开连接需要在系统蓝牙设置中完成。请在系统设置中断开此设备。</string><string name="bluetooth_headset_disconnect_system_settings">HEADSET断开连接需要在系统蓝牙设置中完成。请在系统设置中断开此设备。</string><string name="bluetooth_error_get_device_uuid">获取设备UUID失败: %1$s</string><string name="bluetooth_error_socket_close">关闭Socket失败: %1$s</string><string name="bluetooth_error_a2dp_devices">获取A2DP连接设备错误: %1$s</string><string name="bluetooth_error_headset_devices">获取HEADSET连接设备错误: %1$s</string><string name="bluetooth_error_profile_proxy">获取配置文件代理错误: %1$s</string><string name="bluetooth_error_close_a2dp_proxy">关闭A2DP代理失败: %1$s</string><string name="bluetooth_error_close_headset_proxy">关闭HEADSET代理失败: %1$s</string><string name="bluetooth_error_check_connected">检查设备是否已连接错误: %1$s</string><string name="bluetooth_error_get_connected_devices">获取已连接设备错误: %1$s</string><string name="mobile_network_settings">移动网络设置</string><string name="mobile_network">移动网络</string><string name="mobile_data">移动数据</string><string name="mobile_data_enabled">移动数据已开启</string><string name="mobile_data_disabled">移动数据已关闭</string><string name="enable_mobile_data">开启移动数据</string><string name="disable_mobile_data">关闭移动数据</string><string name="mobile_network_info">移动网络信息</string><string name="network_type">网络类型: %1$s</string><string name="network_operator">网络运营商: %1$s</string><string name="roaming">漫游: %1$s</string><string name="roaming_enabled">是</string><string name="roaming_disabled">否</string><string name="data_roaming">数据漫游</string><string name="data_roaming_description">允许漫游时使用数据</string><string name="mobile_network_not_available">移动网络不可用</string><string name="mobile_network_permission_required">需要电话状态权限才能访问移动网络设置</string><string name="mobile_network_permission_denied">没有所需权限，无法访问移动网络设置</string><string name="mobile_network_error">访问移动网络设置时出错</string><string name="mobile_network_status">状态: %1$s</string><string name="mobile_network_connected">已连接</string><string name="mobile_network_disconnected">未连接</string><string name="display">显示</string><string name="screen_luminance">屏幕亮度</string><string name="screen_wake_up">屏幕唤醒</string><string name="wake_up_duration">唤醒时长</string><string name="second">秒</string></file><file name="backup_rules" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_luminance_down" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_luminance_down.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_luminance_up" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_luminance_up.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_luminance_down" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_luminance_down.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_luminance_up" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_luminance_up.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_luminance_down" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_luminance_down.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_luminance_up" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_luminance_up.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_luminance_down" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_luminance_down.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_luminance_up" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_luminance_up.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_luminance_down" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_luminance_down.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_luminance_up" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_luminance_up.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_wake_up_duration" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_wake_up_duration.xml" qualifiers="" type="layout"/><file name="ic_ensure" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_ensure.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_ensure" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_ensure.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_ensure" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_ensure.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_ensure" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_ensure.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_ensure" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_ensure.png" qualifiers="xxxhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>