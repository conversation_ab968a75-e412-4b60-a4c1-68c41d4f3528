<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res"><file name="background_call_button" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\background_call_button.xml" qualifiers="" type="drawable"/><file name="background_received_message" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\background_received_message.xml" qualifiers="" type="drawable"/><file name="background_sent_message" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\background_sent_message.xml" qualifiers="" type="drawable"/><file name="bubble_other" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\bubble_other.xml" qualifiers="" type="drawable"/><file name="bubble_self" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\bubble_self.xml" qualifiers="" type="drawable"/><file name="circle_shape" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\circle_shape.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="loadingbgn" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\drawable\loadingbgn.xml" qualifiers="" type="drawable"/><file name="activity_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_call.xml" qualifiers="" type="layout"/><file name="activity_capturectivity" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_capturectivity.xml" qualifiers="" type="layout"/><file name="activity_chat" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_chat.xml" qualifiers="" type="layout"/><file name="activity_chat_cnactivity" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_chat_cnactivity.xml" qualifiers="" type="layout"/><file name="activity_config" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_config.xml" qualifiers="" type="layout"/><file name="activity_device_registration" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_device_registration.xml" qualifiers="" type="layout"/><file name="activity_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_gallery.xml" qualifiers="" type="layout"/><file name="activity_home" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_home.xml" qualifiers="" type="layout"/><file name="activity_language_settings" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_language_settings.xml" qualifiers="" type="layout"/><file name="activity_learn" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_learn.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_recognition" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_recognition.xml" qualifiers="" type="layout"/><file name="activity_repository_test" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_repository_test.xml" qualifiers="" type="layout"/><file name="activity_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_setting.xml" qualifiers="" type="layout"/><file name="activity_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\activity_wifi.xml" qualifiers="" type="layout"/><file name="call_fragment_page" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\call_fragment_page.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="dialog_wifi_password" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\dialog_wifi_password.xml" qualifiers="" type="layout"/><file name="fragment_media_list" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\fragment_media_list.xml" qualifiers="" type="layout"/><file name="fragment_photo_detail" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\fragment_photo_detail.xml" qualifiers="" type="layout"/><file name="fragment_video_detail" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\fragment_video_detail.xml" qualifiers="" type="layout"/><file name="home_list_item" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\home_list_item.xml" qualifiers="" type="layout"/><file name="item_media" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_media.xml" qualifiers="" type="layout"/><file name="item_message_received" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_message_received.xml" qualifiers="" type="layout"/><file name="item_message_sent" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_message_sent.xml" qualifiers="" type="layout"/><file name="item_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\item_wifi.xml" qualifiers="" type="layout"/><file name="learn_fragment_page" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\learn_fragment_page.xml" qualifiers="" type="layout"/><file name="setting_list_item" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\layout\setting_list_item.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\arrow_back.png" qualifiers="hdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\btn_capture.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_accept.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_mianti_off.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_mute_off.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_mute_on.png" qualifiers="hdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\call_refuse.png" qualifiers="hdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\camera_switch.png" qualifiers="hdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\chat_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\dialog_loading_img.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_4g.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_about.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_bind.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_bluetooth.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_call.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_location.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_private.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_resume.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_shutdwon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_voice.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_wifi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\iocn_wifi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_da.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_gallery.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_ji.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_liao_en.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_liao_zh.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_pai.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_setting.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_shi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\item_bg_xue.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_bg_baijuyi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_bg_dufu.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_bg_libai.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_icon_baijuyi.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_icon_dufu.png" qualifiers="hdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\learn_icon_libai.png" qualifiers="hdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\my_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\recognize_result.png" qualifiers="hdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\shibie_bg.png" qualifiers="hdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\talking_static.png" qualifiers="hdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\arrow_back.png" qualifiers="mdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\btn_capture.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_accept.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_mianti_off.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_mute_off.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_mute_on.png" qualifiers="mdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\call_refuse.png" qualifiers="mdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\camera_switch.png" qualifiers="mdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\chat_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\dialog_loading_img.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_4g.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_about.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_bind.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_bluetooth.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_call.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_location.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_private.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_resume.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_shutdwon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_voice.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_wifi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\iocn_wifi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_da.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_gallery.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_ji.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_liao_en.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_liao_zh.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_pai.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_setting.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_shi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\item_bg_xue.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_bg_baijuyi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_bg_dufu.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_bg_libai.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_icon_baijuyi.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_icon_dufu.png" qualifiers="mdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\learn_icon_libai.png" qualifiers="mdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\my_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\recognize_result.png" qualifiers="mdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\shibie_bg.png" qualifiers="mdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\talking_static.png" qualifiers="mdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\arrow_back.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\btn_capture.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_accept.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_mianti_off.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_mute_off.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_mute_on.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\call_refuse.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\camera_switch.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\chat_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\dialog_loading_img.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_4g.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_about.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_bind.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_bluetooth.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_call.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_location.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_private.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_resume.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_shutdwon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_voice.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_wifi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\iocn_wifi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_da.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_gallery.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_ji.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_liao_en.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_liao_zh.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_pai.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_setting.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_shi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\item_bg_xue.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_bg_baijuyi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_bg_dufu.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_bg_libai.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_icon_baijuyi.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_icon_dufu.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\learn_icon_libai.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\my_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\recognize_result.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\shibie_bg.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\talking_static.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\arrow_back.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\btn_capture.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_accept.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_mianti_off.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_mute_off.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_mute_on.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\call_refuse.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\camera_switch.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\chat_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\dialog_loading_img.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_4g.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_about.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_bind.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_bluetooth.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_call.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_location.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_private.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_resume.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_shutdwon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_voice.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_wifi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\iocn_wifi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_da.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_gallery.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_ji.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_liao_en.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_liao_zh.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_pai.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_setting.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_shi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\item_bg_xue.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_bg_baijuyi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_bg_dufu.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_bg_libai.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_icon_baijuyi.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_icon_dufu.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\learn_icon_libai.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\my_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\recognize_result.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\shibie_bg.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\talking_static.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="arrow_back" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\arrow_back.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="btn_capture" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\btn_capture.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_accept" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_accept.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_mianti_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_mianti_off.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_mute_off" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_mute_off.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_mute_on" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_mute_on.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="call_refuse" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\call_refuse.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="camera_switch" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\camera_switch.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="chat_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\chat_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="dialog_loading_img" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\dialog_loading_img.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_4g" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_4g.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_about" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_about.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_bind" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_bind.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_bluetooth" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_bluetooth.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_call" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_call.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_location" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_location.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_private" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_private.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_resume" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_resume.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_shutdwon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_shutdwon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_voice" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_voice.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="icon_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_wifi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="iocn_wifi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\iocn_wifi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_da" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_da.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_gallery" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_gallery.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_ji" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_ji.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_liao_en" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_liao_en.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_liao_zh" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_liao_zh.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_pai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_pai.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_setting" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_setting.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_shi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_shi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="item_bg_xue" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\item_bg_xue.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_bg_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_bg_baijuyi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_bg_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_bg_dufu.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_bg_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_bg_libai.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_icon_baijuyi" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_icon_baijuyi.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_icon_dufu" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_icon_dufu.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="learn_icon_libai" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\learn_icon_libai.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="my_icon" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\my_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="recognize_result" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\recognize_result.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="shibie_bg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\shibie_bg.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="talking_static" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\talking_static.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color><color name="colorBackground">#F5F5F5</color><color name="colorTextPrimary">#212121</color><color name="colorTextSecondary">#757575</color><color name="colorDivider">#BDBDBD</color></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">BBNice</string><string name="error_network">Network Error</string><string name="error_unknown">Unknown Error</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="save">Save</string><string name="delete">Delete</string><string name="loading">Loading…</string><string name="retry">Retry</string><string name="settings">Settings</string><string name="apply">Apply</string><string name="language_settings">Language Settings</string><string name="system_language">System Language</string><string name="english">English</string><string name="chinese">Chinese</string><string name="language_changed">Language changed</string><string name="restart_app_message">Please restart the app for the changes to take effect</string><string name="login">Login</string><string name="register">Register</string><string name="scan_qr_code">Scan QR Code</string><string name="qr_code_expired">QR code has expired</string><string name="qr_code_valid_for">Valid for %1$d minutes</string><string name="device_id">Device ID</string><string name="bind_success">Device bound successfully</string><string name="bind_failed">Failed to bind device</string><string name="unbind_device">Unbind Device</string><string name="unbind_confirm">Are you sure you want to unbind this device?</string><string name="unbind_success">Device unbound successfully</string><string name="unbind_failed">Failed to unbind device</string><string name="home">Home</string><string name="chat">Chat</string><string name="chat_cn">Chinese Chat</string><string name="chat_en">English Chat</string><string name="api_test">API Test</string><string name="device_info">Device Info</string><string name="battery_level">Battery Level</string><string name="signal_strength">Signal Strength</string><string name="network_status">Network Status</string><string name="last_updated">Last Updated: %1$s</string><string name="press_to_talk">Press and hold to talk</string><string name="release_to_stop">Release to stop</string><string name="no_more_history">No more history</string><string name="chat_history">Chat History</string><string name="clear_history">Clear History</string><string name="clear_history_confirm">Are you sure you want to clear all chat history?</string><string name="history_cleared">Chat history cleared</string><string name="upload_history">Upload History</string><string name="upload_success">History uploaded successfully</string><string name="upload_failed">Failed to upload history</string><string name="location">Location</string><string name="current_location">Current Location</string><string name="location_permission_required">Location permission is required</string><string name="location_settings">Location Settings</string><string name="location_not_available">Location not available</string><string name="location_updated">Location updated</string><string name="permission_required">Permission Required</string><string name="permission_denied">Permission Denied</string><string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string><string name="go_to_settings">Go to Settings</string><string name="camera">Camera</string><string name="microphone">Microphone</string><string name="phone_state">Phone State</string><string name="gallery">Gallery</string><string name="photos">Photos</string><string name="videos">Videos</string><string name="all_media">All Media</string><string name="select_all">Select All</string><string name="deselect_all">Deselect All</string><string name="delete_selected">Delete Selected</string><string name="delete_confirm">Are you sure you want to delete the selected items?</string><string name="no_media_found">No media files found</string><string name="storage_permission_required">Storage permission is required to access media files</string><string name="play">Play</string><string name="pause">Pause</string><string name="loading_media">Loading media…</string><string name="error_connection">Connection error</string><string name="error_timeout">Request timed out</string><string name="error_server">Server error</string><string name="error_authentication">Authentication error</string><string name="error_invalid_input">Invalid input</string><string name="error_device_not_found">Device not found</string><string name="error_already_bound">Device already bound</string><string name="error_not_bound">Device not bound</string><string name="wifi_connected">Connected</string><string name="enable_location">Enable Location</string><string name="location_service_required">Location service must be enabled to scan WiFi networks on Android 8.0+</string><string name="wifi_password_empty">Please enter a password</string><string name="wifi_already_connected">Already connected to this network</string><string name="wifi">WiFi</string><string name="scanning_wifi">Scanning for WiFi networks…</string><string name="location_required">Location permission is required to scan WiFi networks</string><string name="no_wifi_networks">No WiFi networks available</string><string name="wifi_enter_password">Enter WiFi Password</string><string name="wifi_disconnected">Not connected</string><string name="wifi_connection_failed">Failed to connect to WiFi</string><string name="wifi_connecting">Connecting to %1$s</string><string name="wifi_settings">WiFi Settings</string><string name="scan_failed">Failed to scan WiFi networks</string><string name="wifi_password">Password</string><string name="wifi_show_password">Show password</string><string name="available_networks">Available Networks</string><string name="wifi_connect">Connect</string></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\styles.xml" qualifiers=""><style name="MyDialogStyle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.BBNice" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.BBNice" parent="Base.Theme.BBNice"/></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">BBNice</string><string name="error_network">Network Error</string><string name="error_unknown">Unknown Error</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="save">Save</string><string name="delete">Delete</string><string name="loading">Loading…</string><string name="retry">Retry</string><string name="settings">Settings</string><string name="apply">Apply</string><string name="language_settings">Language Settings</string><string name="system_language">System Language</string><string name="english">English</string><string name="chinese">Chinese</string><string name="language_changed">Language changed</string><string name="restart_app_message">Please restart the app for the changes to take effect</string><string name="login">Login</string><string name="register">Register</string><string name="scan_qr_code">Scan QR Code</string><string name="qr_code_expired">QR code has expired</string><string name="qr_code_valid_for">Valid for %1$d minutes</string><string name="device_id">Device ID</string><string name="bind_success">Device bound successfully</string><string name="bind_failed">Failed to bind device</string><string name="unbind_device">Unbind Device</string><string name="unbind_confirm">Are you sure you want to unbind this device?</string><string name="unbind_success">Device unbound successfully</string><string name="unbind_failed">Failed to unbind device</string><string name="home">Home</string><string name="chat">Chat</string><string name="chat_cn">Chinese Chat</string><string name="chat_en">English Chat</string><string name="device_info">Device Info</string><string name="battery_level">Battery Level</string><string name="signal_strength">Signal Strength</string><string name="network_status">Network Status</string><string name="last_updated">Last Updated: %1$s</string><string name="press_to_talk">Press and hold to talk</string><string name="release_to_stop">Release to stop</string><string name="no_more_history">No more history</string><string name="chat_history">Chat History</string><string name="clear_history">Clear History</string><string name="clear_history_confirm">Are you sure you want to clear all chat history?</string><string name="history_cleared">Chat history cleared</string><string name="upload_history">Upload History</string><string name="upload_success">History uploaded successfully</string><string name="upload_failed">Failed to upload history</string><string name="location">Location</string><string name="current_location">Current Location</string><string name="location_permission_required">Location permission is required</string><string name="location_settings">Location Settings</string><string name="location_not_available">Location not available</string><string name="location_updated">Location updated</string><string name="permission_required">Permission Required</string><string name="permission_denied">Permission Denied</string><string name="permission_denied_message">This feature requires permissions that have been denied. Please enable them in settings.</string><string name="go_to_settings">Go to Settings</string><string name="camera">Camera</string><string name="microphone">Microphone</string><string name="phone_state">Phone State</string><string name="error_connection">Connection error</string><string name="error_timeout">Request timed out</string><string name="error_server">Server error</string><string name="error_authentication">Authentication error</string><string name="error_invalid_input">Invalid input</string><string name="error_device_not_found">Device not found</string><string name="error_already_bound">Device already bound</string><string name="error_not_bound">Device not bound</string></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.BBNice" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-zh\strings.xml" qualifiers="zh"><string name="app_name">BBNice</string><string name="error_network">网络错误</string><string name="error_unknown">未知错误</string><string name="ok">确定</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="save">保存</string><string name="delete">删除</string><string name="loading">加载中…</string><string name="retry">重试</string><string name="settings">设置</string><string name="apply">应用</string><string name="language_settings">语言设置</string><string name="system_language">系统语言</string><string name="english">英语</string><string name="chinese">中文</string><string name="language_changed">语言已更改</string><string name="restart_app_message">请重启应用以使更改生效</string><string name="login">登录</string><string name="register">注册</string><string name="scan_qr_code">扫描二维码</string><string name="qr_code_expired">二维码已过期</string><string name="qr_code_valid_for">有效期 %1$d 分钟</string><string name="device_id">设备ID</string><string name="bind_success">设备绑定成功</string><string name="bind_failed">设备绑定失败</string><string name="unbind_device">解绑设备</string><string name="unbind_confirm">确定要解绑此设备吗？</string><string name="unbind_success">设备解绑成功</string><string name="unbind_failed">设备解绑失败</string><string name="home">首页</string><string name="chat">聊天</string><string name="chat_cn">中文聊天</string><string name="chat_en">英文聊天</string><string name="api_test">接口测试</string><string name="device_info">设备信息</string><string name="battery_level">电量</string><string name="signal_strength">信号强度</string><string name="network_status">网络状态</string><string name="last_updated">最后更新: %1$s</string><string name="press_to_talk">按住说话</string><string name="release_to_stop">松开结束</string><string name="no_more_history">没有更多历史记录</string><string name="chat_history">聊天记录</string><string name="clear_history">清空历史</string><string name="clear_history_confirm">确定要清空所有聊天记录吗？</string><string name="history_cleared">聊天记录已清空</string><string name="upload_history">上传历史</string><string name="upload_success">历史上传成功</string><string name="upload_failed">历史上传失败</string><string name="location">位置</string><string name="current_location">当前位置</string><string name="location_permission_required">需要位置权限</string><string name="location_settings">位置设置</string><string name="location_not_available">位置不可用</string><string name="location_updated">位置已更新</string><string name="permission_required">需要权限</string><string name="permission_denied">权限被拒绝</string><string name="permission_denied_message">此功能需要已被拒绝的权限。请在设置中启用它们。</string><string name="go_to_settings">前往设置</string><string name="camera">相机</string><string name="microphone">麦克风</string><string name="phone_state">电话状态</string><string name="error_connection">连接错误</string><string name="error_timeout">请求超时</string><string name="error_server">服务器错误</string><string name="error_authentication">认证错误</string><string name="error_invalid_input">无效输入</string><string name="error_device_not_found">设备未找到</string><string name="error_already_bound">设备已绑定</string><string name="error_not_bound">设备未绑定</string><string name="no_wifi_networks">没有可用的WiFi网络</string><string name="delete_selected">删除所选</string><string name="storage_permission_required">需要存储权限才能访问媒体文件</string><string name="wifi_settings">WiFi设置</string><string name="location_service_required">Android 8.0及以上版本需要开启位置服务才能扫描WiFi网络</string><string name="select_all">全选</string><string name="delete_confirm">确定要删除所选项目吗？</string><string name="wifi_show_password">显示密码</string><string name="wifi">WiFi</string><string name="deselect_all">取消全选</string><string name="wifi_enter_password">输入WiFi密码</string><string name="scanning_wifi">正在扫描WiFi网络…</string><string name="enable_location">开启位置服务</string><string name="gallery">相册</string><string name="pause">暂停</string><string name="wifi_password">密码</string><string name="photos">照片</string><string name="loading_media">加载媒体中…</string><string name="wifi_connection_failed">连接WiFi失败</string><string name="wifi_disconnected">未连接</string><string name="wifi_connecting">正在连接到 %1$s</string><string name="location_required">需要位置权限才能扫描WiFi网络</string><string name="wifi_already_connected">已连接到该网络</string><string name="play">播放</string><string name="all_media">所有媒体</string><string name="wifi_connected">已连接</string><string name="wifi_connect">连接</string><string name="wifi_password_empty">请输入密码</string><string name="videos">视频</string><string name="available_networks">可用网络</string><string name="scan_failed">扫描WiFi网络失败</string><string name="no_media_found">未找到媒体文件</string></file><file path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\values-zh-rCN\strings.xml" qualifiers="zh-rCN"><string name="app_name">BBNice</string><string name="error_network">网络错误</string><string name="error_unknown">未知错误</string><string name="ok">确定</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="save">保存</string><string name="delete">删除</string><string name="loading">加载中…</string><string name="retry">重试</string><string name="settings">设置</string><string name="apply">应用</string><string name="language_settings">语言设置</string><string name="system_language">系统语言</string><string name="english">英文</string><string name="chinese">中文</string><string name="language_changed">语言已更改</string><string name="restart_app_message">请重启应用以使更改生效</string><string name="login">登录</string><string name="register">注册</string><string name="scan_qr_code">扫描二维码</string><string name="qr_code_expired">二维码已过期</string><string name="qr_code_valid_for">有效期 %1$d 分钟</string><string name="device_id">设备ID</string><string name="bind_success">设备绑定成功</string><string name="bind_failed">设备绑定失败</string><string name="unbind_device">解绑设备</string><string name="unbind_confirm">确定要解绑此设备吗？</string><string name="unbind_success">设备解绑成功</string><string name="unbind_failed">设备解绑失败</string><string name="home">首页</string><string name="chat">聊天</string><string name="chat_cn">中文聊天</string><string name="chat_en">英文聊天</string><string name="device_info">设备信息</string><string name="battery_level">电池电量</string><string name="signal_strength">信号强度</string><string name="network_status">网络状态</string><string name="last_updated">最后更新：%1$s</string><string name="press_to_talk">按住说话</string><string name="release_to_stop">松开结束</string><string name="no_more_history">没有更多历史记录</string><string name="chat_history">聊天记录</string><string name="clear_history">清空记录</string><string name="clear_history_confirm">确定要清空所有聊天记录吗？</string><string name="history_cleared">聊天记录已清空</string><string name="upload_history">上传记录</string><string name="upload_success">记录上传成功</string><string name="upload_failed">记录上传失败</string><string name="location">位置</string><string name="current_location">当前位置</string><string name="location_permission_required">需要位置权限</string><string name="location_settings">位置设置</string><string name="location_not_available">位置不可用</string><string name="location_updated">位置已更新</string><string name="permission_required">需要权限</string><string name="permission_denied">权限被拒绝</string><string name="permission_denied_message">此功能需要已被拒绝的权限。请在设置中启用它们。</string><string name="go_to_settings">前往设置</string><string name="camera">相机</string><string name="microphone">麦克风</string><string name="phone_state">电话状态</string><string name="error_connection">连接错误</string><string name="error_timeout">请求超时</string><string name="error_server">服务器错误</string><string name="error_authentication">认证错误</string><string name="error_invalid_input">输入无效</string><string name="error_device_not_found">设备未找到</string><string name="error_already_bound">设备已绑定</string><string name="error_not_bound">设备未绑定</string></file><file name="backup_rules" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-hdpi\icon_wifi_lock.png" qualifiers="hdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-mdpi\icon_wifi_lock.png" qualifiers="mdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xhdpi\icon_wifi_lock.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxhdpi\icon_wifi_lock.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="icon_wifi_lock" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\res\mipmap-xxxhdpi\icon_wifi_lock.png" qualifiers="xxxhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>