<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_call" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_call.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_call_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="16" endOffset="51"/></Target><Target id="@+id/viewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="8" startOffset="4" endLine="15" endOffset="51"/></Target></Targets></Layout>