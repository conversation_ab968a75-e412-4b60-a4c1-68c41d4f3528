{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-es/values-es.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,174", "endColumns": "118,119", "endOffsets": "169,289"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8622,8741", "endColumns": "118,119", "endOffsets": "8736,8856"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,8944", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,9022"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "9027", "endColumns": "100", "endOffsets": "9123"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\cff9521c234b1cb1e27a4b1f3461c77d\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,331", "endColumns": "58,46,169,135", "endOffsets": "109,156,326,462"}, "to": {"startLines": "105,106,107,108", "startColumns": "4,4,4,4", "startOffsets": "9128,9187,9234,9404", "endColumns": "58,46,169,135", "endOffsets": "9182,9229,9399,9535"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,3503,3607,3729,3810,3875,3970,4051,4114,4203,4267,4336,4399,4473,4537,4593,4711,4769,4831,4887,4967,5106,5195,5277,5418,5499,5579,5730,5820,5900,5956,6012,6078,6157,6239,6327,6416,6490,6567,6637,6716,6816,6900,6984,7076,7176,7250,7331,7433,7486,7571,7638,7731,7820,7882,7946,8009,8077,8190,8297,8401,8502,8562,8861", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "323,3135,3214,3301,3402,3498,3602,3724,3805,3870,3965,4046,4109,4198,4262,4331,4394,4468,4532,4588,4706,4764,4826,4882,4962,5101,5190,5272,5413,5494,5574,5725,5815,5895,5951,6007,6073,6152,6234,6322,6411,6485,6562,6632,6711,6811,6895,6979,7071,7171,7245,7326,7428,7481,7566,7633,7726,7815,7877,7941,8004,8072,8185,8292,8396,8497,8557,8617,8939"}}]}]}