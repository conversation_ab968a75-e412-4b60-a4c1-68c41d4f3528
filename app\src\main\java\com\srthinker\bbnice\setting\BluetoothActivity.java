package com.srthinker.bbnice.setting;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.BluetoothSocket;
import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothHeadset;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelUuid;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.InputType;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.textfield.TextInputEditText;
import com.srthinker.bbnice.R;

import java.lang.reflect.Method;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class BluetoothActivity extends AppCompatActivity implements BluetoothDeviceAdapter.OnDeviceClickListener {

    private static final String TAG = "BluetoothActivity";
    private static final int PERMISSIONS_REQUEST_CODE = 200;
    private static final int REQUEST_ENABLE_BT = 1;

    private Toolbar toolbar;
    private SwitchCompat switchBluetooth;
    private RecyclerView rvPairedDevices;
    private RecyclerView rvAvailableDevices;
    private SwipeRefreshLayout swipeRefresh;
    private ProgressBar progressBar;
    private TextView tvEmptyState;

    private BluetoothManager bluetoothManager;
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothDeviceAdapter pairedDevicesAdapter;
    private BluetoothDeviceAdapter availableDevicesAdapter;
    private List<BluetoothDevice> pairedDevicesList = new ArrayList<>();
    private List<BluetoothDevice> availableDevicesList = new ArrayList<>();
    private List<BluetoothDevice> connectedDevicesList = new ArrayList<>();
    private BroadcastReceiver bluetoothReceiver;

    // 蓝牙连接相关
    private BluetoothSocket bluetoothSocket;
    private BluetoothGatt bluetoothGatt;
    private BluetoothProfile.ServiceListener profileListener;
    private BluetoothA2dp bluetoothA2dp;
    private BluetoothHeadset bluetoothHeadset;
    private boolean isConnecting = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bluetooth);

        // 初始化视图
        initViews();

        // 初始化蓝牙管理器
        bluetoothManager = (BluetoothManager) getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager.getAdapter();

        // 检查设备是否支持蓝牙
        if (bluetoothAdapter == null) {
            Toast.makeText(this, R.string.bluetooth_not_supported, Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 初始化适配器
        pairedDevicesAdapter = new BluetoothDeviceAdapter(this, true);
        rvPairedDevices.setLayoutManager(new LinearLayoutManager(this));
        rvPairedDevices.setAdapter(pairedDevicesAdapter);

        availableDevicesAdapter = new BluetoothDeviceAdapter(this, false);
        rvAvailableDevices.setLayoutManager(new LinearLayoutManager(this));
        rvAvailableDevices.setAdapter(availableDevicesAdapter);

        // 设置下拉刷新监听器
        swipeRefresh.setOnRefreshListener(this::scanBluetoothDevices);

        // 设置蓝牙开关监听器
        switchBluetooth.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                enableBluetooth();
            } else {
                disableBluetooth();
            }
        });

        // 初始化蓝牙广播接收器
        initBluetoothReceiver();

        // 初始化蓝牙配置文件监听器
        initProfileListener();

        // 检查蓝牙状态
        updateBluetoothState();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.bluetooth_settings));

        switchBluetooth = findViewById(R.id.switch_bluetooth);
        rvPairedDevices = findViewById(R.id.rv_paired_devices);
        rvAvailableDevices = findViewById(R.id.rv_available_devices);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        progressBar = findViewById(R.id.progress_bar);
        tvEmptyState = findViewById(R.id.tv_empty_state);
    }

    private void initBluetoothReceiver() {
        bluetoothReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action != null) {
                    switch (action) {
                        case BluetoothAdapter.ACTION_STATE_CHANGED:
                            int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
                            updateBluetoothState(state);
                            break;
                        case BluetoothDevice.ACTION_FOUND:
                            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                            if (device != null && device.getName() != null && !pairedDevicesList.contains(device)) {
                                availableDevicesAdapter.addDevice(device);
                                hideEmptyState();
                            }
                            break;
                        case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                            showLoading();
                            availableDevicesAdapter.clearDevices();
                            break;
                        case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                            hideLoading();
                            if (availableDevicesList.isEmpty()) {
                                showEmptyState();
                            }
                            break;
                        case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                            int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR);
                            BluetoothDevice bondDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                            if (bondDevice != null) {
                                if (bondState == BluetoothDevice.BOND_BONDED) {
                                    // 设备已配对
                                    Toast.makeText(context, getString(R.string.bluetooth_paired), Toast.LENGTH_SHORT).show();
                                    updateDeviceLists();
                                } else if (bondState == BluetoothDevice.BOND_NONE) {
                                    // 设备未配对
                                    updateDeviceLists();
                                }
                            }
                            break;
                    }
                }
            }
        };
    }

    private void updateBluetoothState() {
        if (bluetoothAdapter != null) {
            int state = bluetoothAdapter.getState();
            updateBluetoothState(state);
        }
    }

    private void updateBluetoothState(int state) {
        switch (state) {
            case BluetoothAdapter.STATE_ON:
                switchBluetooth.setChecked(true);
                updateDeviceLists();
                break;
            case BluetoothAdapter.STATE_OFF:
                switchBluetooth.setChecked(false);
                clearDeviceLists();
                break;
            case BluetoothAdapter.STATE_TURNING_ON:
                switchBluetooth.setChecked(true);
                showLoading();
                break;
            case BluetoothAdapter.STATE_TURNING_OFF:
                switchBluetooth.setChecked(false);
                showLoading();
                break;
        }
    }

    private void enableBluetooth() {
        if (bluetoothAdapter != null && !bluetoothAdapter.isEnabled()) {
            if (checkBluetoothPermissions()) {
                Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
            }
        }
    }

    private void disableBluetooth() {
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            if (checkBluetoothPermissions()) {
                bluetoothAdapter.disable();
                clearDeviceLists();
            }
        }
    }

    private boolean checkBluetoothPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED ||
                checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.BLUETOOTH_SCAN
                }, PERMISSIONS_REQUEST_CODE);
                return false;
            }
        }
        return true;
    }

    private void scanBluetoothDevices() {
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            hideLoading();
            return;
        }

        // 检查蓝牙权限
        if (!checkBluetoothPermissions()) {
            hideLoading();
            return;
        }

        // 如果正在扫描，先停止
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }

        // 开始扫描
        showLoading();
        availableDevicesAdapter.clearDevices();
        boolean success = bluetoothAdapter.startDiscovery();
        if (!success) {
            hideLoading();
            Toast.makeText(this, R.string.bluetooth_scan_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void updateDeviceLists() {
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            return;
        }

        if (!checkBluetoothPermissions()) {
            return;
        }

        // 获取已配对设备
        pairedDevicesList.clear();
        Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
        if (pairedDevices != null && !pairedDevices.isEmpty()) {
            pairedDevicesList.addAll(pairedDevices);
        }

        // 获取已连接设备
        connectedDevicesList.clear();
        List<BluetoothDevice> connectedDevices = getConnectedDevices();
        if (connectedDevices != null && !connectedDevices.isEmpty()) {
            connectedDevicesList.addAll(connectedDevices);
        }

        // 更新适配器
        pairedDevicesAdapter.updateDeviceList(pairedDevicesList, connectedDevicesList);
        availableDevicesAdapter.updateDeviceList(availableDevicesList, connectedDevicesList);

        // 扫描可用设备
        scanBluetoothDevices();
    }

    private List<BluetoothDevice> getConnectedDevices() {
        List<BluetoothDevice> connectedDevices = new ArrayList<>();
        if (bluetoothManager != null && bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            if (checkBluetoothPermissions()) {
                // 使用安全的方式获取连接的设备
                try {
                    // 检查设备是否支持HEADSET配置文件
                    if (bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEADSET) != BluetoothProfile.STATE_DISCONNECTED) {
                        try {
                            List<BluetoothDevice> devices = bluetoothManager.getConnectedDevices(BluetoothProfile.HEADSET);
                            if (devices != null) {
                                connectedDevices.addAll(devices);
                            }
                        } catch (IllegalArgumentException e) {
                            Log.e(TAG, "HEADSET profile not supported: " + e.getMessage());
                        }
                    }

                    // 检查设备是否支持A2DP配置文件
                    if (bluetoothAdapter.getProfileConnectionState(BluetoothProfile.A2DP) != BluetoothProfile.STATE_DISCONNECTED) {
                        try {
                            List<BluetoothDevice> devices = bluetoothManager.getConnectedDevices(BluetoothProfile.A2DP);
                            if (devices != null) {
                                connectedDevices.addAll(devices);
                            }
                        } catch (IllegalArgumentException e) {
                            Log.e(TAG, "A2DP profile not supported: " + e.getMessage());
                        }
                    }

                    // 尝试获取已配对设备中的已连接设备
                    Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
                    if (pairedDevices != null) {
                        for (BluetoothDevice device : pairedDevices) {
                            try {
                                // 检查设备是否已连接
                                if (isConnected(device)) {
                                    if (!connectedDevices.contains(device)) {
                                        connectedDevices.add(device);
                                    }
                                }
                            } catch (Exception e) {
                                // 某些设备可能不支持isConnected方法
                                Log.e(TAG, "Error checking if device is connected: " + e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error getting connected devices: " + e.getMessage());
                }
            }
        }
        return connectedDevices;
    }

    // 扩展BluetoothDevice类，添加isConnected方法
    private static boolean isConnected(BluetoothDevice device) {
        try {
            Method m = device.getClass().getMethod("isConnected");
            return (boolean) m.invoke(device);
        } catch (Exception e) {
            // 如果方法不可用，假设设备未连接
            return false;
        }
    }

    private void clearDeviceLists() {
        pairedDevicesList.clear();
        availableDevicesList.clear();
        connectedDevicesList.clear();
        pairedDevicesAdapter.updateDeviceList(pairedDevicesList, connectedDevicesList);
        availableDevicesAdapter.updateDeviceList(availableDevicesList, connectedDevicesList);
        hideLoading();
        showEmptyState();
    }

    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        swipeRefresh.setRefreshing(false);
    }

    private void hideLoading() {
        progressBar.setVisibility(View.GONE);
        swipeRefresh.setRefreshing(false);
    }

    private void showEmptyState() {
        tvEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
    }

    @Override
    public void onDeviceClick(BluetoothDevice device) {
        if (!checkBluetoothPermissions()) {
            return;
        }

        // 检查设备配对状态
        int bondState = device.getBondState();
        if (bondState == BluetoothDevice.BOND_BONDED) {
            // 已配对设备，显示连接/断开选项
            showPairedDeviceDialog(device);
        } else {
            // 未配对设备，开始配对
            pairDevice(device);
        }
    }

    private void showPairedDeviceDialog(BluetoothDevice device) {
        boolean isConnected = connectedDevicesList.contains(device);
        String[] options;
        if (isConnected) {
            options = new String[]{getString(R.string.bluetooth_disconnect), getString(R.string.bluetooth_unpair)};
        } else {
            options = new String[]{getString(R.string.bluetooth_connect), getString(R.string.bluetooth_unpair)};
        }

        new AlertDialog.Builder(this)
                .setTitle(device.getName())
                .setItems(options, (dialog, which) -> {
                    if (which == 0) {
                        if (isConnected) {
                            disconnectDevice(device);
                        } else {
                            connectDevice(device);
                        }
                    } else if (which == 1) {
                        unpairDevice(device);
                    }
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    private void pairDevice(BluetoothDevice device) {
        try {
            Toast.makeText(this, getString(R.string.bluetooth_pairing, device.getName()), Toast.LENGTH_SHORT).show();
            device.createBond();
        } catch (Exception e) {
            Log.e(TAG, "Error pairing device: " + e.getMessage());
            Toast.makeText(this, R.string.bluetooth_pairing_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void unpairDevice(BluetoothDevice device) {
        try {
            // 使用反射调用removeBond方法
            device.getClass().getMethod("removeBond").invoke(device);
            Toast.makeText(this, getString(R.string.bluetooth_unpair) + ": " + device.getName(), Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error unpairing device: " + e.getMessage());
            Toast.makeText(this, R.string.bluetooth_pairing_failed, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 初始化蓝牙配置文件监听器
     */
    private void initProfileListener() {
        profileListener = new BluetoothProfile.ServiceListener() {
            @Override
            public void onServiceConnected(int profile, BluetoothProfile proxy) {
                if (profile == BluetoothProfile.A2DP) {
                    bluetoothA2dp = (BluetoothA2dp) proxy;
                    Log.d(TAG, "A2DP profile connected");
                } else if (profile == BluetoothProfile.HEADSET) {
                    bluetoothHeadset = (BluetoothHeadset) proxy;
                    Log.d(TAG, "HEADSET profile connected");
                }
            }

            @Override
            public void onServiceDisconnected(int profile) {
                if (profile == BluetoothProfile.A2DP) {
                    bluetoothA2dp = null;
                    Log.d(TAG, "A2DP profile disconnected");
                } else if (profile == BluetoothProfile.HEADSET) {
                    bluetoothHeadset = null;
                    Log.d(TAG, "HEADSET profile disconnected");
                }
            }
        };

        // 获取A2DP和HEADSET配置文件代理
        if (bluetoothAdapter != null) {
            try {
                bluetoothAdapter.getProfileProxy(this, profileListener, BluetoothProfile.A2DP);
                bluetoothAdapter.getProfileProxy(this, profileListener, BluetoothProfile.HEADSET);
            } catch (Exception e) {
                Log.e(TAG, "Error getting profile proxy: " + e.getMessage());
            }
        }
    }

    /**
     * 连接蓝牙设备
     * @param device 要连接的蓝牙设备
     */
    private void connectDevice(BluetoothDevice device) {
        if (isConnecting) {
            Toast.makeText(this, "已有连接正在进行中，请稍后再试", Toast.LENGTH_SHORT).show();
            return;
        }

        if (!checkBluetoothPermissions()) {
            return;
        }

        // 停止扫描以提高连接性能
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }

        Toast.makeText(this, getString(R.string.bluetooth_connecting, device.getName()), Toast.LENGTH_SHORT).show();
        isConnecting = true;

        // 根据设备类型选择连接方式
        int deviceClass = device.getBluetoothClass().getMajorDeviceClass();

        // 尝试使用GATT连接（适用于BLE设备）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            try {
                connectWithGatt(device);
                return;
            } catch (Exception e) {
                Log.e(TAG, "GATT连接失败，尝试其他方式: " + e.getMessage());
            }
        }

        // 尝试使用A2DP连接（适用于音频设备）
        if (deviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO) {
            try {
                connectWithA2dp(device);
                return;
            } catch (Exception e) {
                Log.e(TAG, "A2DP连接失败，尝试其他方式: " + e.getMessage());
            }
        }

        // 尝试使用HEADSET连接（适用于耳机设备）
        if (deviceClass == android.bluetooth.BluetoothClass.Device.Major.AUDIO_VIDEO ||
            deviceClass == android.bluetooth.BluetoothClass.Device.Major.PHONE) {
            try {
                connectWithHeadset(device);
                return;
            } catch (Exception e) {
                Log.e(TAG, "HEADSET连接失败，尝试其他方式: " + e.getMessage());
            }
        }

        // 尝试使用Socket连接（通用方法）
        try {
            connectWithSocket(device);
        } catch (Exception e) {
            Log.e(TAG, "Socket连接失败: " + e.getMessage());
            isConnecting = false;
            Toast.makeText(this, R.string.bluetooth_connection_failed, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 使用GATT连接BLE设备
     * @param device 要连接的蓝牙设备
     */
    private void connectWithGatt(BluetoothDevice device) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            bluetoothGatt = device.connectGatt(this, false, new BluetoothGattCallback() {
                @Override
                public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
                    super.onConnectionStateChange(gatt, status, newState);
                    final String deviceName = gatt.getDevice().getName();

                    if (newState == BluetoothProfile.STATE_CONNECTED) {
                        Log.d(TAG, "GATT连接成功: " + deviceName);
                        runOnUiThread(() -> {
                            isConnecting = false;
                            Toast.makeText(BluetoothActivity.this,
                                    getString(R.string.bluetooth_connected) + ": " + deviceName,
                                    Toast.LENGTH_SHORT).show();
                            updateDeviceLists();
                        });
                    } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                        Log.d(TAG, "GATT连接断开: " + deviceName);
                        runOnUiThread(() -> {
                            isConnecting = false;
                            if (status != 0) {
                                Toast.makeText(BluetoothActivity.this,
                                        R.string.bluetooth_connection_failed,
                                        Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                }
            });
        } else {
            throw new UnsupportedOperationException("GATT不支持在当前Android版本");
        }
    }

    /**
     * 使用A2DP连接音频设备
     * @param device 要连接的蓝牙设备
     */
    private void connectWithA2dp(BluetoothDevice device) throws Exception {
        if (bluetoothA2dp == null) {
            throw new IllegalStateException("A2DP配置文件不可用");
        }

        // 使用反射调用connect方法
        Method connectMethod = bluetoothA2dp.getClass().getMethod("connect", BluetoothDevice.class);
        boolean success = (boolean) connectMethod.invoke(bluetoothA2dp, device);

        if (success) {
            Log.d(TAG, "A2DP连接请求已发送");

            // 添加延迟检查连接状态
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    Method getConnectionStateMethod = bluetoothA2dp.getClass().getMethod("getConnectionState", BluetoothDevice.class);
                    int state = (int) getConnectionStateMethod.invoke(bluetoothA2dp, device);

                    if (state == BluetoothProfile.STATE_CONNECTED) {
                        Toast.makeText(this, getString(R.string.bluetooth_connected) + ": " + device.getName(),
                                Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(this, R.string.bluetooth_connection_failed, Toast.LENGTH_SHORT).show();
                    }

                    isConnecting = false;
                    updateDeviceLists();
                } catch (Exception e) {
                    Log.e(TAG, "检查A2DP连接状态失败: " + e.getMessage());
                    isConnecting = false;
                }
            }, 5000); // 5秒后检查
        } else {
            isConnecting = false;
            throw new IOException("A2DP连接失败");
        }
    }

    /**
     * 使用HEADSET连接耳机设备
     * @param device 要连接的蓝牙设备
     */
    private void connectWithHeadset(BluetoothDevice device) throws Exception {
        if (bluetoothHeadset == null) {
            throw new IllegalStateException("HEADSET配置文件不可用");
        }

        // 使用反射调用connect方法
        Method connectMethod = bluetoothHeadset.getClass().getMethod("connect", BluetoothDevice.class);
        boolean success = (boolean) connectMethod.invoke(bluetoothHeadset, device);

        if (success) {
            Log.d(TAG, "HEADSET连接请求已发送");

            // 添加延迟检查连接状态
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    Method getConnectionStateMethod = bluetoothHeadset.getClass().getMethod("getConnectionState", BluetoothDevice.class);
                    int state = (int) getConnectionStateMethod.invoke(bluetoothHeadset, device);

                    if (state == BluetoothProfile.STATE_CONNECTED) {
                        Toast.makeText(this, getString(R.string.bluetooth_connected) + ": " + device.getName(),
                                Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(this, R.string.bluetooth_connection_failed, Toast.LENGTH_SHORT).show();
                    }

                    isConnecting = false;
                    updateDeviceLists();
                } catch (Exception e) {
                    Log.e(TAG, "检查HEADSET连接状态失败: " + e.getMessage());
                    isConnecting = false;
                }
            }, 5000); // 5秒后检查
        } else {
            isConnecting = false;
            throw new IOException("HEADSET连接失败");
        }
    }

    /**
     * 使用Socket连接通用蓝牙设备
     * @param device 要连接的蓝牙设备
     */
    private void connectWithSocket(BluetoothDevice device) throws IOException {
        // 获取设备的UUID
        UUID uuid = null;
        try {
            ParcelUuid[] uuids = device.getUuids();
            if (uuids != null && uuids.length > 0) {
                uuid = uuids[0].getUuid();
            }
        } catch (Exception e) {
            Log.e(TAG, "获取设备UUID失败: " + e.getMessage());
        }

        // 如果无法获取设备UUID，使用通用串行端口UUID
        if (uuid == null) {
            uuid = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB"); // 串行端口服务UUID
        }

        // 创建并连接Socket
        try {
            bluetoothSocket = device.createRfcommSocketToServiceRecord(uuid);

            // 在后台线程中连接，避免阻塞UI
            new Thread(() -> {
                try {
                    bluetoothSocket.connect();

                    runOnUiThread(() -> {
                        Toast.makeText(BluetoothActivity.this,
                                getString(R.string.bluetooth_connected) + ": " + device.getName(),
                                Toast.LENGTH_SHORT).show();
                        isConnecting = false;
                        updateDeviceLists();
                    });
                } catch (IOException e) {
                    try {
                        bluetoothSocket.close();
                    } catch (IOException closeException) {
                        Log.e(TAG, "关闭Socket失败: " + closeException.getMessage());
                    }

                    runOnUiThread(() -> {
                        Toast.makeText(BluetoothActivity.this,
                                R.string.bluetooth_connection_failed,
                                Toast.LENGTH_SHORT).show();
                        isConnecting = false;
                    });

                    Log.e(TAG, "Socket连接失败: " + e.getMessage());
                }
            }).start();
        } catch (IOException e) {
            isConnecting = false;
            throw e;
        }
    }

    /**
     * 断开蓝牙设备连接
     * @param device 要断开连接的蓝牙设备
     */
    private void disconnectDevice(BluetoothDevice device) {
        if (!checkBluetoothPermissions()) {
            return;
        }

        Toast.makeText(this, getString(R.string.bluetooth_disconnect) + ": " + device.getName(), Toast.LENGTH_SHORT).show();

        // 尝试断开GATT连接
        if (bluetoothGatt != null && bluetoothGatt.getDevice().equals(device)) {
            bluetoothGatt.disconnect();
            bluetoothGatt.close();
            bluetoothGatt = null;
            updateDeviceLists();
            return;
        }

        // 尝试断开A2DP连接
        if (bluetoothA2dp != null) {
            try {
                Method disconnectMethod = bluetoothA2dp.getClass().getMethod("disconnect", BluetoothDevice.class);
                disconnectMethod.invoke(bluetoothA2dp, device);
                updateDeviceLists();
                return;
            } catch (Exception e) {
                Log.e(TAG, "断开A2DP连接失败: " + e.getMessage());
            }
        }

        // 尝试断开HEADSET连接
        if (bluetoothHeadset != null) {
            try {
                Method disconnectMethod = bluetoothHeadset.getClass().getMethod("disconnect", BluetoothDevice.class);
                disconnectMethod.invoke(bluetoothHeadset, device);
                updateDeviceLists();
                return;
            } catch (Exception e) {
                Log.e(TAG, "断开HEADSET连接失败: " + e.getMessage());
            }
        }

        // 尝试断开Socket连接
        if (bluetoothSocket != null && bluetoothSocket.getRemoteDevice().equals(device)) {
            try {
                bluetoothSocket.close();
                bluetoothSocket = null;
                updateDeviceLists();
            } catch (IOException e) {
                Log.e(TAG, "断开Socket连接失败: " + e.getMessage());
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSIONS_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                updateBluetoothState();
            } else {
                Toast.makeText(this, R.string.bluetooth_permission_required, Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode == RESULT_OK) {
                // 蓝牙已启用
                updateBluetoothState();
            } else {
                // 用户拒绝启用蓝牙
                switchBluetooth.setChecked(false);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 注册广播接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        registerReceiver(bluetoothReceiver, filter);

        // 更新蓝牙状态
        updateBluetoothState();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 注销广播接收器
        unregisterReceiver(bluetoothReceiver);

        // 停止扫描
        if (bluetoothAdapter != null && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 关闭GATT连接
        if (bluetoothGatt != null) {
            bluetoothGatt.close();
            bluetoothGatt = null;
        }

        // 关闭Socket连接
        if (bluetoothSocket != null) {
            try {
                bluetoothSocket.close();
            } catch (IOException e) {
                Log.e(TAG, "关闭Socket失败: " + e.getMessage());
            }
            bluetoothSocket = null;
        }

        // 关闭配置文件代理
        if (bluetoothAdapter != null) {
            if (bluetoothA2dp != null) {
                try {
                    bluetoothAdapter.closeProfileProxy(BluetoothProfile.A2DP, bluetoothA2dp);
                } catch (Exception e) {
                    Log.e(TAG, "关闭A2DP代理失败: " + e.getMessage());
                }
                bluetoothA2dp = null;
            }

            if (bluetoothHeadset != null) {
                try {
                    bluetoothAdapter.closeProfileProxy(BluetoothProfile.HEADSET, bluetoothHeadset);
                } catch (Exception e) {
                    Log.e(TAG, "关闭HEADSET代理失败: " + e.getMessage());
                }
                bluetoothHeadset = null;
            }
        }
    }
}
