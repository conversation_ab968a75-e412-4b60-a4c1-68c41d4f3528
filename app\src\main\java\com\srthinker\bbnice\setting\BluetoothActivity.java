package com.srthinker.bbnice.setting;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.InputType;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.textfield.TextInputEditText;
import com.srthinker.bbnice.R;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class BluetoothActivity extends AppCompatActivity implements BluetoothDeviceAdapter.OnDeviceClickListener {

    private static final String TAG = "BluetoothActivity";
    private static final int PERMISSIONS_REQUEST_CODE = 200;
    private static final int REQUEST_ENABLE_BT = 1;

    private Toolbar toolbar;
    private SwitchCompat switchBluetooth;
    private RecyclerView rvPairedDevices;
    private RecyclerView rvAvailableDevices;
    private SwipeRefreshLayout swipeRefresh;
    private ProgressBar progressBar;
    private TextView tvEmptyState;

    private BluetoothManager bluetoothManager;
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothDeviceAdapter pairedDevicesAdapter;
    private BluetoothDeviceAdapter availableDevicesAdapter;
    private List<BluetoothDevice> pairedDevicesList = new ArrayList<>();
    private List<BluetoothDevice> availableDevicesList = new ArrayList<>();
    private List<BluetoothDevice> connectedDevicesList = new ArrayList<>();
    private BroadcastReceiver bluetoothReceiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bluetooth);

        // 初始化视图
        initViews();

        // 初始化蓝牙管理器
        bluetoothManager = (BluetoothManager) getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager.getAdapter();

        // 检查设备是否支持蓝牙
        if (bluetoothAdapter == null) {
            Toast.makeText(this, R.string.bluetooth_not_supported, Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 初始化适配器
        pairedDevicesAdapter = new BluetoothDeviceAdapter(this, true);
        rvPairedDevices.setLayoutManager(new LinearLayoutManager(this));
        rvPairedDevices.setAdapter(pairedDevicesAdapter);

        availableDevicesAdapter = new BluetoothDeviceAdapter(this, false);
        rvAvailableDevices.setLayoutManager(new LinearLayoutManager(this));
        rvAvailableDevices.setAdapter(availableDevicesAdapter);

        // 设置下拉刷新监听器
        swipeRefresh.setOnRefreshListener(this::scanBluetoothDevices);

        // 设置蓝牙开关监听器
        switchBluetooth.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                enableBluetooth();
            } else {
                disableBluetooth();
            }
        });

        // 初始化蓝牙广播接收器
        initBluetoothReceiver();

        // 检查蓝牙状态
        updateBluetoothState();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.bluetooth_settings));

        switchBluetooth = findViewById(R.id.switch_bluetooth);
        rvPairedDevices = findViewById(R.id.rv_paired_devices);
        rvAvailableDevices = findViewById(R.id.rv_available_devices);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        progressBar = findViewById(R.id.progress_bar);
        tvEmptyState = findViewById(R.id.tv_empty_state);
    }

    private void initBluetoothReceiver() {
        bluetoothReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action != null) {
                    switch (action) {
                        case BluetoothAdapter.ACTION_STATE_CHANGED:
                            int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
                            updateBluetoothState(state);
                            break;
                        case BluetoothDevice.ACTION_FOUND:
                            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                            if (device != null && device.getName() != null && !pairedDevicesList.contains(device)) {
                                availableDevicesAdapter.addDevice(device);
                                hideEmptyState();
                            }
                            break;
                        case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                            showLoading();
                            availableDevicesAdapter.clearDevices();
                            break;
                        case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                            hideLoading();
                            if (availableDevicesList.isEmpty()) {
                                showEmptyState();
                            }
                            break;
                        case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                            int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR);
                            BluetoothDevice bondDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                            if (bondDevice != null) {
                                if (bondState == BluetoothDevice.BOND_BONDED) {
                                    // 设备已配对
                                    Toast.makeText(context, getString(R.string.bluetooth_paired), Toast.LENGTH_SHORT).show();
                                    updateDeviceLists();
                                } else if (bondState == BluetoothDevice.BOND_NONE) {
                                    // 设备未配对
                                    updateDeviceLists();
                                }
                            }
                            break;
                    }
                }
            }
        };
    }

    private void updateBluetoothState() {
        if (bluetoothAdapter != null) {
            int state = bluetoothAdapter.getState();
            updateBluetoothState(state);
        }
    }

    private void updateBluetoothState(int state) {
        switch (state) {
            case BluetoothAdapter.STATE_ON:
                switchBluetooth.setChecked(true);
                updateDeviceLists();
                break;
            case BluetoothAdapter.STATE_OFF:
                switchBluetooth.setChecked(false);
                clearDeviceLists();
                break;
            case BluetoothAdapter.STATE_TURNING_ON:
                switchBluetooth.setChecked(true);
                showLoading();
                break;
            case BluetoothAdapter.STATE_TURNING_OFF:
                switchBluetooth.setChecked(false);
                showLoading();
                break;
        }
    }

    private void enableBluetooth() {
        if (bluetoothAdapter != null && !bluetoothAdapter.isEnabled()) {
            if (checkBluetoothPermissions()) {
                Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
            }
        }
    }

    private void disableBluetooth() {
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            if (checkBluetoothPermissions()) {
                bluetoothAdapter.disable();
                clearDeviceLists();
            }
        }
    }

    private boolean checkBluetoothPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED ||
                checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.BLUETOOTH_SCAN
                }, PERMISSIONS_REQUEST_CODE);
                return false;
            }
        }
        return true;
    }

    private void scanBluetoothDevices() {
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            hideLoading();
            return;
        }

        // 检查蓝牙权限
        if (!checkBluetoothPermissions()) {
            hideLoading();
            return;
        }

        // 如果正在扫描，先停止
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }

        // 开始扫描
        showLoading();
        availableDevicesAdapter.clearDevices();
        boolean success = bluetoothAdapter.startDiscovery();
        if (!success) {
            hideLoading();
            Toast.makeText(this, R.string.bluetooth_scan_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void updateDeviceLists() {
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            return;
        }

        if (!checkBluetoothPermissions()) {
            return;
        }

        // 获取已配对设备
        pairedDevicesList.clear();
        Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
        if (pairedDevices != null && !pairedDevices.isEmpty()) {
            pairedDevicesList.addAll(pairedDevices);
        }

        // 获取已连接设备
        connectedDevicesList.clear();
        List<BluetoothDevice> connectedDevices = getConnectedDevices();
        if (connectedDevices != null && !connectedDevices.isEmpty()) {
            connectedDevicesList.addAll(connectedDevices);
        }

        // 更新适配器
        pairedDevicesAdapter.updateDeviceList(pairedDevicesList, connectedDevicesList);
        availableDevicesAdapter.updateDeviceList(availableDevicesList, connectedDevicesList);

        // 扫描可用设备
        scanBluetoothDevices();
    }

    private List<BluetoothDevice> getConnectedDevices() {
        List<BluetoothDevice> connectedDevices = new ArrayList<>();
        if (bluetoothManager != null && bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            if (checkBluetoothPermissions()) {
                // 使用安全的方式获取连接的设备
                try {
                    // 检查设备是否支持HEADSET配置文件
                    if (bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEADSET) != BluetoothProfile.STATE_DISCONNECTED) {
                        try {
                            List<BluetoothDevice> devices = bluetoothManager.getConnectedDevices(BluetoothProfile.HEADSET);
                            if (devices != null) {
                                connectedDevices.addAll(devices);
                            }
                        } catch (IllegalArgumentException e) {
                            Log.e(TAG, "HEADSET profile not supported: " + e.getMessage());
                        }
                    }

                    // 检查设备是否支持A2DP配置文件
                    if (bluetoothAdapter.getProfileConnectionState(BluetoothProfile.A2DP) != BluetoothProfile.STATE_DISCONNECTED) {
                        try {
                            List<BluetoothDevice> devices = bluetoothManager.getConnectedDevices(BluetoothProfile.A2DP);
                            if (devices != null) {
                                connectedDevices.addAll(devices);
                            }
                        } catch (IllegalArgumentException e) {
                            Log.e(TAG, "A2DP profile not supported: " + e.getMessage());
                        }
                    }

                    // 尝试获取已配对设备中的已连接设备
                    Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
                    if (pairedDevices != null) {
                        for (BluetoothDevice device : pairedDevices) {
                            try {
                                // 检查设备是否已连接
                                if (isConnected(device)) {
                                    if (!connectedDevices.contains(device)) {
                                        connectedDevices.add(device);
                                    }
                                }
                            } catch (Exception e) {
                                // 某些设备可能不支持isConnected方法
                                Log.e(TAG, "Error checking if device is connected: " + e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error getting connected devices: " + e.getMessage());
                }
            }
        }
        return connectedDevices;
    }

    // 扩展BluetoothDevice类，添加isConnected方法
    private static boolean isConnected(BluetoothDevice device) {
        try {
            Method m = device.getClass().getMethod("isConnected");
            return (boolean) m.invoke(device);
        } catch (Exception e) {
            // 如果方法不可用，假设设备未连接
            return false;
        }
    }

    private void clearDeviceLists() {
        pairedDevicesList.clear();
        availableDevicesList.clear();
        connectedDevicesList.clear();
        pairedDevicesAdapter.updateDeviceList(pairedDevicesList, connectedDevicesList);
        availableDevicesAdapter.updateDeviceList(availableDevicesList, connectedDevicesList);
        hideLoading();
        showEmptyState();
    }

    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        swipeRefresh.setRefreshing(false);
    }

    private void hideLoading() {
        progressBar.setVisibility(View.GONE);
        swipeRefresh.setRefreshing(false);
    }

    private void showEmptyState() {
        tvEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
    }

    @Override
    public void onDeviceClick(BluetoothDevice device) {
        if (!checkBluetoothPermissions()) {
            return;
        }

        // 检查设备配对状态
        int bondState = device.getBondState();
        if (bondState == BluetoothDevice.BOND_BONDED) {
            // 已配对设备，显示连接/断开选项
            showPairedDeviceDialog(device);
        } else {
            // 未配对设备，开始配对
            pairDevice(device);
        }
    }

    private void showPairedDeviceDialog(BluetoothDevice device) {
        boolean isConnected = connectedDevicesList.contains(device);
        String[] options;
        if (isConnected) {
            options = new String[]{getString(R.string.bluetooth_disconnect), getString(R.string.bluetooth_unpair)};
        } else {
            options = new String[]{getString(R.string.bluetooth_connect), getString(R.string.bluetooth_unpair)};
        }

        new AlertDialog.Builder(this)
                .setTitle(device.getName())
                .setItems(options, (dialog, which) -> {
                    if (which == 0) {
                        if (isConnected) {
                            disconnectDevice(device);
                        } else {
                            connectDevice(device);
                        }
                    } else if (which == 1) {
                        unpairDevice(device);
                    }
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    private void pairDevice(BluetoothDevice device) {
        try {
            Toast.makeText(this, getString(R.string.bluetooth_pairing, device.getName()), Toast.LENGTH_SHORT).show();
            device.createBond();
        } catch (Exception e) {
            Log.e(TAG, "Error pairing device: " + e.getMessage());
            Toast.makeText(this, R.string.bluetooth_pairing_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void unpairDevice(BluetoothDevice device) {
        try {
            // 使用反射调用removeBond方法
            device.getClass().getMethod("removeBond").invoke(device);
            Toast.makeText(this, getString(R.string.bluetooth_unpair) + ": " + device.getName(), Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error unpairing device: " + e.getMessage());
            Toast.makeText(this, R.string.bluetooth_pairing_failed, Toast.LENGTH_SHORT).show();
        }
    }

    private void connectDevice(BluetoothDevice device) {
        // 连接设备的实现依赖于具体的蓝牙配置文件
        // 这里只是一个简单的示例
        Toast.makeText(this, getString(R.string.bluetooth_connecting, device.getName()), Toast.LENGTH_SHORT).show();
        // 实际连接逻辑需要根据设备类型和蓝牙配置文件来实现
    }

    private void disconnectDevice(BluetoothDevice device) {
        // 断开设备连接的实现依赖于具体的蓝牙配置文件
        // 这里只是一个简单的示例
        Toast.makeText(this, getString(R.string.bluetooth_disconnect) + ": " + device.getName(), Toast.LENGTH_SHORT).show();
        // 实际断开连接逻辑需要根据设备类型和蓝牙配置文件来实现
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSIONS_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                updateBluetoothState();
            } else {
                Toast.makeText(this, R.string.bluetooth_permission_required, Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode == RESULT_OK) {
                // 蓝牙已启用
                updateBluetoothState();
            } else {
                // 用户拒绝启用蓝牙
                switchBluetooth.setChecked(false);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 注册广播接收器
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        registerReceiver(bluetoothReceiver, filter);

        // 更新蓝牙状态
        updateBluetoothState();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 注销广播接收器
        unregisterReceiver(bluetoothReceiver);

        // 停止扫描
        if (bluetoothAdapter != null && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
