<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_mobile_network" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_mobile_network.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_mobile_network_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="150" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="53"/></Target><Target id="@+id/mobile_data_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="50" endOffset="55"/></Target><Target id="@+id/tv_mobile_data_label" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="40" endOffset="55"/></Target><Target id="@+id/switch_mobile_data" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="55"/></Target><Target id="@+id/divider2" view="View"><Expressions/><location startLine="53" startOffset="4" endLine="58" endOffset="74"/></Target><Target id="@+id/tv_network_info_label" view="TextView"><Expressions/><location startLine="60" startOffset="4" endLine="68" endOffset="61"/></Target><Target id="@+id/card_network_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="70" startOffset="4" endLine="126" endOffset="39"/></Target><Target id="@+id/tv_network_status" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="91" endOffset="41"/></Target><Target id="@+id/tv_network_type" view="TextView"><Expressions/><location startLine="93" startOffset="12" endLine="99" endOffset="41"/></Target><Target id="@+id/tv_network_operator" view="TextView"><Expressions/><location startLine="101" startOffset="12" endLine="107" endOffset="41"/></Target><Target id="@+id/tv_signal_strength" view="TextView"><Expressions/><location startLine="109" startOffset="12" endLine="115" endOffset="41"/></Target><Target id="@+id/tv_roaming" view="TextView"><Expressions/><location startLine="117" startOffset="12" endLine="122" endOffset="41"/></Target><Target id="@+id/btn_refresh" view="Button"><Expressions/><location startLine="128" startOffset="4" endLine="136" endOffset="70"/></Target><Target id="@+id/tv_permission_warning" view="TextView"><Expressions/><location startLine="138" startOffset="4" endLine="148" endOffset="64"/></Target></Targets></Layout>