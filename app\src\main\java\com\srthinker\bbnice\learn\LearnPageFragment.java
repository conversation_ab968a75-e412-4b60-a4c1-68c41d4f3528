package com.srthinker.bbnice.learn;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;

import com.srthinker.bbnice.App;
import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.repository.AIRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.chat.ChatMessage;
import com.srthinker.bbnice.chat.MySubtitleMessage;
import com.srthinker.bbnice.common.GlobalRtcVideo;
import com.srthinker.bbnice.common.RoomMessage;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.db.ChatRepository;
import com.srthinker.bbnice.db.RoomType;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.SubtitleUtil;
import com.ss.bytertc.engine.RTCRoom;
import com.ss.bytertc.engine.RTCRoomConfig;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.type.ChannelProfile;

import java.nio.ByteBuffer;
import java.util.UUID;

public class LearnPageFragment extends Fragment {

    private static final String TAG = "PageFragment";
    private LearnPageType pageType;
    private RTCRoom rtcRoom = null;
    private final RoomMessage roomMessage = new RoomMessage();
    private ChatRepository chatRepository;
    private AIRepository aiRepository;
    private Context context;
    private RTCStartRequestData requestData;
    private UserInfo userInfo;
    private RTCRoomConfig roomConfig;

    public static LearnPageFragment newInstance(LearnPageType pageType) {
        LearnPageFragment fragment = new LearnPageFragment();
        fragment.setPageType(pageType);
        return fragment;
    }

    public void setPageType(LearnPageType pageType) {
        this.pageType = pageType;
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.learn_fragment_page, container, false);
        initUI(view);
        initData();
        context = getContext();
        chatRepository = ChatRepository.getInstance(App.getInstance());
        aiRepository = ApiRepositoryProvider.getInstance(context).getAiRepository();
        return view;
    }

    private void initUI(View view) {
        TextView name = view.findViewById(R.id.tv_name);
        name.setText(pageType.getName());
        view.setBackgroundResource(pageType.getBgResId());
        ImageView icon = view.findViewById(R.id.img_icon);
        icon.setImageResource(pageType.getIconResId());

        view.findViewById(R.id.btn_finish).setOnClickListener(v -> {
            requireActivity().finish();
        });
    }

    private void initData() {

        roomMessage.roomID = DeviceIdUtils.getDeviceId(getContext()) + "_" + pageType.toString();
        roomMessage.userID = DeviceIdUtils.getDeviceId(getContext());
        roomMessage.systemMessage = pageType.getSystemMessage();
        roomMessage.welcomeMessage = pageType.getWelcomeMessage();

        rtcRoom = GlobalRtcVideo.getInstance().rtcVideo().createRTCRoom(roomMessage.roomID); // 创建房间
        rtcRoom.setRTCRoomEventHandler(new IRTCRoomEventHandler() {
            @Override
            public void onRoomBinaryMessageReceived(String uid, ByteBuffer message) {
                StringBuilder subtitles = new StringBuilder();
                MySubtitleMessage subtitleMessage = SubtitleUtil.unpack(message, subtitles);
                if (subtitleMessage != null) {
                    MySubtitleMessage.SubtitleData subtitleData = subtitleMessage.data.get(0);
                    boolean sendByMe = subtitleData.userId.equals(roomMessage.userID);
                    if (subtitleData.definite) {
                        Log.d(TAG, "onRoomBinaryMessageReceived: " + subtitleData.text);
                        ChatMessage chatMessage = new ChatMessage(subtitleData.text, sendByMe);
                        chatMessage.setRoomType(RoomType.Poetry.getValue());
                        chatRepository.saveMessage(chatMessage);
                    }
                }
            }
        });


        // 用户信息
        userInfo = new UserInfo(roomMessage.userID, "");

        // 开启房间参数
        requestData = new RTCStartRequestData();
        requestData.room_id = roomMessage.roomID;
        requestData.enable_vision = false;
        requestData.disable_subtitle = false;
        requestData.welcome_message = roomMessage.welcomeMessage;
        requestData.system_message = roomMessage.systemMessage;

        // 设置房间配置
        roomConfig = new RTCRoomConfig(
                ChannelProfile.CHANNEL_PROFILE_CHAT_ROOM,
                true,
                true,
                false
        );
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: " + pageType);

        if (TextUtils.isEmpty(roomMessage.token) || TextUtils.isEmpty(roomMessage.taskID)) {
            aiRepository.aiTokens(roomMessage.roomID).observe(LearnPageFragment.this, new Observer<Result<RTCTokenResponse>>() {
                @Override
                public void onChanged(Result<RTCTokenResponse> rtcTokenResponseResult) {
                    if (rtcTokenResponseResult.isSuccess()) {
                        RTCTokenResponse tokenResponse = rtcTokenResponseResult.getData();
                        if (tokenResponse.isSuccess()) {
                            roomMessage.token = tokenResponse.getData().getToken();
                            aiRepository.rtcStart(requestData).observe(LearnPageFragment.this, rtcStartResponseResult -> {
                                if (rtcStartResponseResult.isSuccess()) {
                                    RTCStartResponse rtcStartResponse = rtcStartResponseResult.getData();
                                    roomMessage.taskID = rtcStartResponse.getData().getTask_id();
                                    Log.d(TAG, "join room: " + rtcRoom.joinRoom(roomMessage.token, userInfo, roomConfig));
                                }
                            });
                        }
                    }
                }
            });
        } else {
            Log.d(TAG, "join room: " + rtcRoom.joinRoom(roomMessage.token, userInfo, roomConfig));
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: " + pageType);
        if (rtcRoom != null) {
            rtcRoom.leaveRoom();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (rtcRoom != null) {
            rtcRoom.destroy();
        }
        aiRepository.rtcStop(roomMessage.taskID, roomMessage.roomID);
    }
}