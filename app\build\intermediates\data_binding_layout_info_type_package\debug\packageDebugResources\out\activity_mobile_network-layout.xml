<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_mobile_network" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_mobile_network.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_mobile_network_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="201" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="53"/></Target><Target id="@+id/mobile_data_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="50" endOffset="55"/></Target><Target id="@+id/tv_mobile_data_label" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="40" endOffset="55"/></Target><Target id="@+id/switch_mobile_data" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="55"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="52" startOffset="4" endLine="57" endOffset="74"/></Target><Target id="@+id/data_roaming_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="59" startOffset="4" endLine="102" endOffset="55"/></Target><Target id="@+id/tv_data_roaming_label" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="79" endOffset="55"/></Target><Target id="@+id/tv_data_roaming_description" view="TextView"><Expressions/><location startLine="81" startOffset="8" endLine="92" endOffset="78"/></Target><Target id="@+id/switch_data_roaming" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="94" startOffset="8" endLine="100" endOffset="55"/></Target><Target id="@+id/divider2" view="View"><Expressions/><location startLine="104" startOffset="4" endLine="109" endOffset="75"/></Target><Target id="@+id/tv_network_info_label" view="TextView"><Expressions/><location startLine="111" startOffset="4" endLine="119" endOffset="61"/></Target><Target id="@+id/card_network_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="121" startOffset="4" endLine="177" endOffset="39"/></Target><Target id="@+id/tv_network_status" view="TextView"><Expressions/><location startLine="136" startOffset="12" endLine="142" endOffset="41"/></Target><Target id="@+id/tv_network_type" view="TextView"><Expressions/><location startLine="144" startOffset="12" endLine="150" endOffset="41"/></Target><Target id="@+id/tv_network_operator" view="TextView"><Expressions/><location startLine="152" startOffset="12" endLine="158" endOffset="41"/></Target><Target id="@+id/tv_signal_strength" view="TextView"><Expressions/><location startLine="160" startOffset="12" endLine="166" endOffset="41"/></Target><Target id="@+id/tv_roaming" view="TextView"><Expressions/><location startLine="168" startOffset="12" endLine="173" endOffset="41"/></Target><Target id="@+id/btn_refresh" view="Button"><Expressions/><location startLine="179" startOffset="4" endLine="187" endOffset="70"/></Target><Target id="@+id/tv_permission_warning" view="TextView"><Expressions/><location startLine="189" startOffset="4" endLine="199" endOffset="64"/></Target></Targets></Layout>