<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/settings"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"/>

    <!-- 连接设置 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="连接设置"
        android:textStyle="bold"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"/>

    <!-- 用户ID输入框 -->
    <EditText
        android:id="@+id/user_id_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入用户ID" />

    <!-- 房间ID输入框 -->
    <EditText
        android:id="@+id/room_id_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入房间ID" />

    <EditText
        android:id="@+id/server_host_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="服务器地址" />

    <!-- 语言设置 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/language_settings"
        android:textStyle="bold"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="8dp"/>

    <Button
        android:id="@+id/btn_language_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/language_settings" />

    <!-- 保存配置按钮 -->
    <Button
        android:id="@+id/save_config_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/save" />

</LinearLayout>