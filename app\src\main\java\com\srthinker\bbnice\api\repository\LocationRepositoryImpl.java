package com.srthinker.bbnice.api.repository;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.LocationData;
import com.srthinker.bbnice.core.ErrorHandler;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.location.LocationManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LocationRepository接口的实现类
 */
public class LocationRepositoryImpl implements LocationRepository {

    private final Context context;
    private final BBNiceApi api;

    // 认证Token
    private String authToken;

    /**
     * 构造函数
     * @param context 上下文
     */
    public LocationRepositoryImpl(Context context) {
        this.context = context;
        this.api = new BBNiceApi(context);
    }

    /**
     * 设置认证Token
     * @param token 认证Token
     */
    public void setAuthToken(String token) {
        this.authToken = token;
        api.setAuthToken(token);
    }

    /**
     * 创建带认证的请求头
     * @return 请求头Map
     */
    private Map<String, String> createAuthHeaders() {
        Map<String, String> headers = new HashMap<>();
        if (authToken != null && !authToken.isEmpty()) {
            headers.put("Authorization", "Bearer " + authToken);
        }
        return headers;
    }

    @Override
    public LiveData<Result<LocationData>> getCurrentLocation() {
        MutableLiveData<Result<LocationData>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        LocationManager locationManager = LocationManager.getInstance(context);
        // 获取当前位置
        locationManager.getCurrentLocation(new ApiCallback<LocationData>() {
            @Override
            public void onSuccess(LocationData locationData) {
                result.postValue(Result.success(locationData));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });
        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> reportLocation(LocationData locationData) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.reportLocation(locationData, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> reportLocations(List<LocationData> locationDataList) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.reportLocations(locationDataList, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }
}
