package com.srthinker.bbnice.setting;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.telephony.PhoneStateListener;
import android.telephony.ServiceState;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.srthinker.bbnice.R;

import java.lang.reflect.Method;

public class MobileNetworkActivity extends AppCompatActivity {

    private static final String TAG = "MobileNetworkActivity";
    private static final int PERMISSIONS_REQUEST_CODE = 300;

    private Toolbar toolbar;
    private SwitchCompat switchMobileData;
    private SwitchCompat switchDataRoaming;
    private TextView tvNetworkStatus;
    private TextView tvNetworkType;
    private TextView tvNetworkOperator;
    private TextView tvSignalStrength;
    private TextView tvRoaming;
    private Button btnRefresh;
    private TextView tvPermissionWarning;

    private TelephonyManager telephonyManager;
    private ConnectivityManager connectivityManager;
    private PhoneStateListener phoneStateListener;
    private int signalStrength = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_mobile_network);

        // 初始化视图
        initViews();
        
        // 初始化服务
        telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
        connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        
        // 检查权限
        if (!checkPhoneStatePermission()) {
            tvPermissionWarning.setVisibility(View.VISIBLE);
        }
        
        // 设置移动数据开关监听器
        switchMobileData.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) {
                if (isChecked) {
                    enableMobileData();
                } else {
                    disableMobileData();
                }
            }
        });
        
        // 设置数据漫游开关监听器
        switchDataRoaming.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) {
                setDataRoaming(isChecked);
            }
        });
        
        // 设置刷新按钮监听器
        btnRefresh.setOnClickListener(v -> refreshNetworkInfo());
        
        // 初始化电话状态监听器
        initPhoneStateListener();
        
        // 更新网络信息
        refreshNetworkInfo();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.mobile_network_settings));

        switchMobileData = findViewById(R.id.switch_mobile_data);
        switchDataRoaming = findViewById(R.id.switch_data_roaming);
        tvNetworkStatus = findViewById(R.id.tv_network_status);
        tvNetworkType = findViewById(R.id.tv_network_type);
        tvNetworkOperator = findViewById(R.id.tv_network_operator);
        tvSignalStrength = findViewById(R.id.tv_signal_strength);
        tvRoaming = findViewById(R.id.tv_roaming);
        btnRefresh = findViewById(R.id.btn_refresh);
        tvPermissionWarning = findViewById(R.id.tv_permission_warning);
    }

    private void initPhoneStateListener() {
        phoneStateListener = new PhoneStateListener() {
            @Override
            public void onSignalStrengthsChanged(SignalStrength signalStrength) {
                super.onSignalStrengthsChanged(signalStrength);
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        MobileNetworkActivity.this.signalStrength = signalStrength.getCellSignalStrengths().get(0).getDbm();
                    } else {
                        // 使用反射获取信号强度
                        Method method = SignalStrength.class.getMethod("getDbm");
                        MobileNetworkActivity.this.signalStrength = (int) method.invoke(signalStrength);
                    }
                    updateSignalStrength();
                } catch (Exception e) {
                    Log.e(TAG, "Error getting signal strength: " + e.getMessage());
                }
            }

            @Override
            public void onServiceStateChanged(ServiceState serviceState) {
                super.onServiceStateChanged(serviceState);
                refreshNetworkInfo();
            }
        };
    }

    private boolean checkPhoneStatePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{Manifest.permission.READ_PHONE_STATE}, PERMISSIONS_REQUEST_CODE);
                return false;
            }
        }
        return true;
    }

    private void refreshNetworkInfo() {
        if (!checkPhoneStatePermission()) {
            return;
        }

        try {
            // 更新移动数据开关状态
            boolean mobileDataEnabled = isMobileDataEnabled();
            switchMobileData.setChecked(mobileDataEnabled);

            // 更新数据漫游开关状态
            boolean dataRoamingEnabled = isDataRoamingEnabled();
            switchDataRoaming.setChecked(dataRoamingEnabled);

            // 更新网络状态
            boolean isConnected = isMobileNetworkConnected();
            String statusText = getString(R.string.mobile_network_status, 
                    isConnected ? getString(R.string.mobile_network_connected) : getString(R.string.mobile_network_disconnected));
            tvNetworkStatus.setText(statusText);

            // 更新网络类型
            String networkType = getNetworkType();
            tvNetworkType.setText(getString(R.string.network_type, networkType));

            // 更新网络运营商
            String networkOperator = telephonyManager.getNetworkOperatorName();
            if (networkOperator == null || networkOperator.isEmpty()) {
                networkOperator = getString(R.string.unknown);
            }
            tvNetworkOperator.setText(getString(R.string.network_operator, networkOperator));

            // 更新信号强度
            updateSignalStrength();

            // 更新漫游状态
            boolean isRoaming = telephonyManager.isNetworkRoaming();
            String roamingStatus = isRoaming ? getString(R.string.roaming_enabled) : getString(R.string.roaming_disabled);
            tvRoaming.setText(getString(R.string.roaming, roamingStatus));

        } catch (Exception e) {
            Log.e(TAG, "Error refreshing network info: " + e.getMessage());
            Toast.makeText(this, R.string.mobile_network_error, Toast.LENGTH_SHORT).show();
        }
    }

    private void updateSignalStrength() {
        if (signalStrength != -1) {
            tvSignalStrength.setText(getString(R.string.signal_strength, signalStrength));
        } else {
            tvSignalStrength.setText(getString(R.string.signal_strength, 0));
        }
    }

    private boolean isMobileDataEnabled() {
        boolean mobileDataEnabled = false;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mobileDataEnabled = connectivityManager.getRestrictBackgroundStatus() 
                        != ConnectivityManager.RESTRICT_BACKGROUND_STATUS_ENABLED;
            } else {
                Class cmClass = connectivityManager.getClass();
                Method method = cmClass.getMethod("getMobileDataEnabled");
                mobileDataEnabled = (boolean) method.invoke(connectivityManager);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking mobile data state: " + e.getMessage());
        }
        return mobileDataEnabled;
    }

    private boolean isDataRoamingEnabled() {
        boolean dataRoamingEnabled = false;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                dataRoamingEnabled = Settings.Global.getInt(getContentResolver(), 
                        Settings.Global.DATA_ROAMING, 0) == 1;
            } else {
                dataRoamingEnabled = Settings.System.getInt(getContentResolver(), 
                        Settings.System.DATA_ROAMING, 0) == 1;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking data roaming state: " + e.getMessage());
        }
        return dataRoamingEnabled;
    }

    private boolean isMobileNetworkConnected() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                return connectivityManager.getActiveNetwork() != null;
            } else {
                return connectivityManager.getActiveNetworkInfo() != null 
                        && connectivityManager.getActiveNetworkInfo().isConnected();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking network connection: " + e.getMessage());
            return false;
        }
    }

    private String getNetworkType() {
        try {
            int networkType = telephonyManager.getNetworkType();
            switch (networkType) {
                case TelephonyManager.NETWORK_TYPE_GPRS:
                case TelephonyManager.NETWORK_TYPE_EDGE:
                case TelephonyManager.NETWORK_TYPE_CDMA:
                case TelephonyManager.NETWORK_TYPE_1xRTT:
                case TelephonyManager.NETWORK_TYPE_IDEN:
                    return "2G";
                case TelephonyManager.NETWORK_TYPE_UMTS:
                case TelephonyManager.NETWORK_TYPE_EVDO_0:
                case TelephonyManager.NETWORK_TYPE_EVDO_A:
                case TelephonyManager.NETWORK_TYPE_HSDPA:
                case TelephonyManager.NETWORK_TYPE_HSUPA:
                case TelephonyManager.NETWORK_TYPE_HSPA:
                case TelephonyManager.NETWORK_TYPE_EVDO_B:
                case TelephonyManager.NETWORK_TYPE_EHRPD:
                case TelephonyManager.NETWORK_TYPE_HSPAP:
                    return "3G";
                case TelephonyManager.NETWORK_TYPE_LTE:
                    return "4G";
                case TelephonyManager.NETWORK_TYPE_NR:
                    return "5G";
                default:
                    return "Unknown";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting network type: " + e.getMessage());
            return "Unknown";
        }
    }

    private void enableMobileData() {
        try {
            // 尝试使用反射启用移动数据
            Method setMobileDataEnabledMethod = ConnectivityManager.class.getDeclaredMethod(
                    "setMobileDataEnabled", boolean.class);
            setMobileDataEnabledMethod.setAccessible(true);
            setMobileDataEnabledMethod.invoke(connectivityManager, true);
            Toast.makeText(this, R.string.mobile_data_enabled, Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error enabling mobile data: " + e.getMessage());
            // 如果反射失败，引导用户到系统设置
            showMobileDataSettingsDialog();
        }
    }

    private void disableMobileData() {
        try {
            // 尝试使用反射禁用移动数据
            Method setMobileDataEnabledMethod = ConnectivityManager.class.getDeclaredMethod(
                    "setMobileDataEnabled", boolean.class);
            setMobileDataEnabledMethod.setAccessible(true);
            setMobileDataEnabledMethod.invoke(connectivityManager, false);
            Toast.makeText(this, R.string.mobile_data_disabled, Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error disabling mobile data: " + e.getMessage());
            // 如果反射失败，引导用户到系统设置
            showMobileDataSettingsDialog();
        }
    }

    private void setDataRoaming(boolean enabled) {
        try {
            // 尝试使用反射设置数据漫游
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Settings.Global.putInt(getContentResolver(), Settings.Global.DATA_ROAMING, enabled ? 1 : 0);
            } else {
                Settings.System.putInt(getContentResolver(), Settings.System.DATA_ROAMING, enabled ? 1 : 0);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting data roaming: " + e.getMessage());
            // 如果反射失败，引导用户到系统设置
            showMobileDataSettingsDialog();
        }
    }

    private void showMobileDataSettingsDialog() {
        new AlertDialog.Builder(this)
                .setTitle(R.string.mobile_network_settings)
                .setMessage(R.string.mobile_network_permission_denied)
                .setPositiveButton(R.string.go_to_settings, (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_DATA_ROAMING_SETTINGS);
                    startActivity(intent);
                })
                .setNegativeButton(R.string.cancel, null)
                .show();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSIONS_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                tvPermissionWarning.setVisibility(View.GONE);
                refreshNetworkInfo();
            } else {
                tvPermissionWarning.setVisibility(View.VISIBLE);
                Toast.makeText(this, R.string.mobile_network_permission_required, Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 注册电话状态监听器
        if (checkPhoneStatePermission()) {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_SIGNAL_STRENGTHS | PhoneStateListener.LISTEN_SERVICE_STATE);
        }
        
        // 刷新网络信息
        refreshNetworkInfo();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 注销电话状态监听器
        telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
