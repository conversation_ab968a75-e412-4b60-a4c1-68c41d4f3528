package com.srthinker.bbnice.home;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.common.Constants;

import java.util.ArrayList;
import java.util.List;

/**
 * 首页项目适配器
 * 负责显示首页的功能入口项
 */
public class HomeItemAdapter extends RecyclerView.Adapter<HomeItemAdapter.ViewHolder> {
    private List<HomeItemType> itemList;
    private final Context context;

    /**
     * 构造函数
     * @param context 上下文
     * @param itemList 项目列表
     */
    public HomeItemAdapter(Context context, List<HomeItemType> itemList) {
        this.context = context;
        this.itemList = new ArrayList<>(itemList);
    }

    /**
     * 更新项目列表
     * @param newItems 新的项目列表
     */
    public void updateItems(List<HomeItemType> newItems) {
        // 使用DiffUtil计算差异
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return itemList.size();
            }

            @Override
            public int getNewListSize() {
                return newItems.size();
            }

            @Override
            public boolean areItemsTheSame(int oldPosition, int newPosition) {
                return itemList.get(oldPosition) == newItems.get(newPosition);
            }

            @Override
            public boolean areContentsTheSame(int oldPosition, int newPosition) {
                return itemList.get(oldPosition) == newItems.get(newPosition);
            }
        });

        // 更新数据
        this.itemList = new ArrayList<>(newItems);

        // 分发更新
        diffResult.dispatchUpdatesTo(this);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // 创建视图
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.home_list_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        HomeItemType itemType = itemList.get(position);

        // 设置图标
        holder.icon.setImageResource(itemType.getIconResId());

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            Intent intent = new Intent(context, itemType.getTargetActivity());
            intent.putExtra(Constants.INTENT_KEY_SYSTEM_MESSAGE, itemType.getSystemMessage());
            intent.putExtra(Constants.INTENT_KEY_WELCOME_MESSAGE, itemType.getWelcomeMessage());
            intent.putExtra(Constants.INTENT_KEY_CHAT_TYPE, itemType.getChatType());
            context.startActivity(intent);
        });
    }

    @Override
    public int getItemCount() {
        return itemList.size();
    }

    /**
     * ViewHolder类
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView icon;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.item_icon);
        }
    }
}
