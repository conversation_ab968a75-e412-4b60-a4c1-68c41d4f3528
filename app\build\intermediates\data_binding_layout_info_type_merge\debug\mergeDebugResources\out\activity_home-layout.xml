<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_home" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="33" endOffset="51"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="10" startOffset="4" endLine="21" endOffset="49"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="23" startOffset="4" endLine="31" endOffset="51"/></Target></Targets></Layout>