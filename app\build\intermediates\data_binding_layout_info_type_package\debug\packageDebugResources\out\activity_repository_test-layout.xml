<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_repository_test" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_repository_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_repository_test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="120" endOffset="14"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="19" startOffset="4" endLine="34" endOffset="16"/></Target><Target id="@+id/tv_result" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="32" endOffset="44"/></Target><Target id="@+id/btn_login" view="Button"><Expressions/><location startLine="46" startOffset="12" endLine="51" endOffset="48"/></Target><Target id="@+id/btn_qr_code" view="Button"><Expressions/><location startLine="53" startOffset="12" endLine="58" endOffset="48"/></Target><Target id="@+id/btn_device_list" view="Button"><Expressions/><location startLine="60" startOffset="12" endLine="65" endOffset="48"/></Target><Target id="@+id/btn_report_status" view="Button"><Expressions/><location startLine="67" startOffset="12" endLine="72" endOffset="48"/></Target><Target id="@+id/btn_report_location" view="Button"><Expressions/><location startLine="88" startOffset="12" endLine="93" endOffset="48"/></Target><Target id="@+id/btn_report_command" view="Button"><Expressions/><location startLine="95" startOffset="12" endLine="100" endOffset="48"/></Target><Target id="@+id/btn_get_location" view="Button"><Expressions/><location startLine="102" startOffset="12" endLine="107" endOffset="48"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="109" startOffset="12" endLine="114" endOffset="48"/></Target></Targets></Layout>