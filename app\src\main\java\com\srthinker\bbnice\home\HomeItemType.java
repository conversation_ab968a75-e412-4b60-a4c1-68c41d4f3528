package com.srthinker.bbnice.home;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.call.CallActivity;
import com.srthinker.bbnice.capture.CaptureActivity;
import com.srthinker.bbnice.chat.ChatActivity;
import com.srthinker.bbnice.db.RoomType;
import com.srthinker.bbnice.gallery.GalleryActivity;
import com.srthinker.bbnice.learn.LearnActivity;
import com.srthinker.bbnice.recognition.RecognitionActivity;
import com.srthinker.bbnice.setting.SettingActivity;
import com.srthinker.bbnice.test.RepositoryTestActivity;

public enum HomeItemType {

    Liao_CN(R.mipmap.item_bg_liao_zh,
            ChatActivity.class,
            RoomType.Chinese.getValue(),
            "你是莉莉，是个善解人意的中文AI助手",
            "很高兴和你聊天!"
    ),
    Liao_EN(R.mipmap.item_bg_liao_en,
            ChatActivity.class,
            RoomType.English.getValue(),
            "你是Lily，是善解人意的英文助教老师。你只用英文进行对话",
            "Nice to chat with you!"

    ),
    Shi(R.mipmap.item_bg_shi,
            RecognitionActivity.class,
            RoomType.Video.getValue(),
            "",
            "AI识别已开启。请和我对话吧！"
    ),
    Pai(R.mipmap.item_bg_pai, CaptureActivity.class,   RoomType.Photo.getValue(),"", ""),
    Call(R.mipmap.item_bg_da, CallActivity.class, RoomType.Call.getValue(),"", ""),
    Xue(R.mipmap.item_bg_xue, LearnActivity.class, RoomType.Poetry.getValue(),"", ""),
    Gallery(R.mipmap.item_bg_gallery, GalleryActivity.class, "","", ""),
    //    Jishu(R.mipmap.item_bg_ji, ChatActivity.class),
    Shezhi(R.mipmap.item_bg_setting, SettingActivity.class, "","", ""),

    Test(R.mipmap.item_bg_liao_zh, RepositoryTestActivity.class, "","", ""),
    ;

    private final int iconResId;
    private final Class<?> targetActivity;
    private final String systemMessage;
    private final String welcomeMessage;

    private final String chatType;

    HomeItemType(int iconResId, Class<?> targetActivity, String chatType, String systemMessage, String welcomeMessage) {
        this.iconResId = iconResId;
        this.chatType = chatType;
        this.targetActivity = targetActivity;
        this.systemMessage = systemMessage;
        this.welcomeMessage = welcomeMessage;
    }

    public int getIconResId() {
        return iconResId;
    }

    public Class<?> getTargetActivity() {
        return targetActivity;
    }

    public String getChatType() {
        return chatType;
    }

    public String getSystemMessage() {
        return systemMessage;
    }

    public String getWelcomeMessage() {
        return welcomeMessage;
    }

}
