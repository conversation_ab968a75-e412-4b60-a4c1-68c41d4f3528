package com.srthinker.bbnice.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.srthinker.bbnice.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限管理工具类
 */
public class PermissionManager {
    private static final String TAG = "PermissionManager";

    // 权限请求码
    public static final int PERMISSION_REQUEST_CODE = 100;

    // 应用所需的权限
    public static final List<String> REQUIRED_PERMISSIONS  = Arrays.asList(
            // 相机和麦克风权限
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            // 位置权限
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            // 电话状态权限
            Manifest.permission.READ_PHONE_STATE,
            // 网络权限
            Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.CHANGE_NETWORK_STATE
    );

    // 用于存储权限请求结果
    private static final Map<String, Boolean> permissionResults = new HashMap<>();

    // 权限回调接口
    public interface PermissionCallback {
        void onPermissionsGranted();

        void onPermissionsDenied(List<String> deniedPermissions);
    }

    public static void addPermission(String permission) {
        REQUIRED_PERMISSIONS.add(permission);
    }
    /**
     * 检查是否已授予所有必需的权限
     * @param context 上下文
     * @return 是否已授予所有权限
     */
    public static boolean hasAllRequiredPermissions(Context context) {
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取未授予的权限列表
     * @param context 上下文
     * @return 未授予的权限列表
     */
    public static List<String> getPermissionsToRequest(Context context) {


        List<String> permissionsToRequest = new ArrayList<>();

        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission);
            }
        }

        return permissionsToRequest;
    }

    /**
     * 请求必需的权限
     * @param activity 活动
     */
    public static void requestPermissions(Activity activity) {
        List<String> permissionsToRequest = getPermissionsToRequest(activity);

        if (!permissionsToRequest.isEmpty()) {
            ActivityCompat.requestPermissions(
                    activity,
                    permissionsToRequest.toArray(new String[0]),
                    PERMISSION_REQUEST_CODE
            );
        }
    }

    /**
     * 处理权限请求结果
     * @param requestCode 请求码
     * @param permissions 权限数组
     * @param grantResults 授权结果数组
     * @param callback 权限回调
     */
    public static void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                                  @NonNull int[] grantResults, PermissionCallback callback) {
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            List<String> deniedPermissions = new ArrayList<>();

            // 检查每个权限的授予状态
            for (int i = 0; i < permissions.length; i++) {
                String permission = permissions[i];
                boolean granted = grantResults[i] == PackageManager.PERMISSION_GRANTED;

                // 保存权限结果
                permissionResults.put(permission, granted);

                if (!granted) {
                    allGranted = false;
                    deniedPermissions.add(permission);
                }

                Log.d(TAG, "权限: " + permission + ", 授予: " + granted);
            }

            if (allGranted) {
                Log.d(TAG, "所有权限已授予");
                if (callback != null) {
                    callback.onPermissionsGranted();
                }
            } else {
                Log.d(TAG, "部分权限被拒绝");
                if (callback != null) {
                    callback.onPermissionsDenied(deniedPermissions);
                }
            }
        }
    }

    /**
     * 检查是否有被永久拒绝的权限
     * @param activity 活动
     * @return 被永久拒绝的权限列表
     */
    public static List<String> getPermanentlyDeniedPermissions(Activity activity) {
        List<String> permanentlyDeniedPermissions = new ArrayList<>();

        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                // 检查用户是否选择了"不再询问"
                if (!ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)) {
                    permanentlyDeniedPermissions.add(permission);
                }
            }
        }

        return permanentlyDeniedPermissions;
    }

    /**
     * 显示设置对话框
     * @param activity 活动
     * @param permanentlyDeniedPermissions 被永久拒绝的权限列表
     */
    public static void showSettingsDialog(Activity activity, List<String> permanentlyDeniedPermissions) {
        StringBuilder message = new StringBuilder(activity.getString(R.string.permission_denied_message) + "\n\n");

        for (String permission : permanentlyDeniedPermissions) {
            message.append("• ").append(getPermissionFriendlyName(activity, permission)).append("\n");
        }

        new AlertDialog.Builder(activity)
                .setTitle(R.string.permission_denied)
                .setMessage(message.toString())
                .setPositiveButton(R.string.go_to_settings, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        // 打开应用设置页面
                        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
                        intent.setData(uri);
                        activity.startActivity(intent);
                    }
                })
                .setNegativeButton(R.string.cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        Toast.makeText(activity, R.string.permission_denied_message, Toast.LENGTH_LONG).show();
                    }
                })
                .setCancelable(false)
                .show();
    }

    /**
     * 显示权限解释对话框
     * @param activity 活动
     * @param callback 对话框回调
     */
    public static void showPermissionExplanationDialog(Activity activity, final Runnable callback) {
        new AlertDialog.Builder(activity)
                .setTitle(R.string.permission_required)
                .setMessage("应用需要以下权限才能正常工作：\n\n" +
                        "• 相机权限：用于拍照和视频通话\n" +
                        "• 麦克风权限：用于语音通话和录音\n" +
                        "• 位置权限：用于获取您的位置信息\n" +
                        "• 电话状态权限：用于获取设备唯一标识\n" +
                        "• 存储权限：用于访问和保存媒体文件\n" +
                        "• 蓝牙权限：用于连接蓝牙设备\n" +
                        "• 网络权限：用于管理网络连接")
                .setPositiveButton(R.string.retry, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (callback != null) {
                            callback.run();
                        }
                    }
                })
                .setNegativeButton(R.string.cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        Toast.makeText(activity, R.string.permission_denied_message, Toast.LENGTH_LONG).show();
                    }
                })
                .setCancelable(false)
                .show();
    }

    /**
     * 获取权限的友好名称
     * @param context 上下文
     * @param permission 权限
     * @return 友好名称
     */
    public static String getPermissionFriendlyName(Context context, String permission) {
        switch (permission) {
            // 相机和麦克风权限
            case Manifest.permission.CAMERA:
                return context.getString(R.string.camera);
            case Manifest.permission.RECORD_AUDIO:
                return context.getString(R.string.microphone);

            // 位置权限
            case Manifest.permission.ACCESS_FINE_LOCATION:
            case Manifest.permission.ACCESS_COARSE_LOCATION:
                return context.getString(R.string.location);

            // 电话状态权限
            case Manifest.permission.READ_PHONE_STATE:
                return context.getString(R.string.phone_state);

            // 存储权限
            case Manifest.permission.READ_EXTERNAL_STORAGE:
            case Manifest.permission.WRITE_EXTERNAL_STORAGE:
                return context.getString(R.string.storage);

            // 蓝牙权限
            case Manifest.permission.BLUETOOTH_SCAN:
            case Manifest.permission.BLUETOOTH_CONNECT:
                return context.getString(R.string.bluetooth);

            // 网络权限
            case Manifest.permission.ACCESS_NETWORK_STATE:
            case Manifest.permission.CHANGE_NETWORK_STATE:
                return context.getString(R.string.network);

            default:
                return permission;
        }
    }

    /**
     * 获取权限的友好名称（兼容旧代码）
     * @param permission 权限
     * @return 友好名称
     * @deprecated 使用 {@link #getPermissionFriendlyName(Context, String)} 代替
     */
    @Deprecated
    public static String getPermissionFriendlyName(String permission) {
        switch (permission) {
            // 相机和麦克风权限
            case Manifest.permission.CAMERA:
                return "相机";
            case Manifest.permission.RECORD_AUDIO:
                return "麦克风";

            // 位置权限
            case Manifest.permission.ACCESS_FINE_LOCATION:
            case Manifest.permission.ACCESS_COARSE_LOCATION:
                return "位置";

            // 电话状态权限
            case Manifest.permission.READ_PHONE_STATE:
                return "电话状态";

            // 存储权限
            case Manifest.permission.READ_EXTERNAL_STORAGE:
            case Manifest.permission.WRITE_EXTERNAL_STORAGE:
                return "存储";

            // 蓝牙权限
            case Manifest.permission.BLUETOOTH_SCAN:
            case Manifest.permission.BLUETOOTH_CONNECT:
                return "蓝牙";

            // 网络权限
            case Manifest.permission.ACCESS_NETWORK_STATE:
            case Manifest.permission.CHANGE_NETWORK_STATE:
                return "网络";

            default:
                return permission;
        }
    }
}
