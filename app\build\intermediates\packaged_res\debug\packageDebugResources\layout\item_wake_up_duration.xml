<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/title"
        android:layout_marginStart="20dp"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"/>

    <ImageView
        android:id="@+id/iv_checked"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="40dp"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:src="@mipmap/ic_ensure"/>

</androidx.constraintlayout.widget.ConstraintLayout>