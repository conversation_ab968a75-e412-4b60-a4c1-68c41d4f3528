package com.srthinker.bbnice.setting;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;

import java.util.Arrays;
import java.util.List;

public class DisplayActivity extends AppCompatActivity {

    private static final int REQUEST_WRITE_SETTINGS = 200;
    private static final int MAX_BRIGHTNESS = 255;

    private Toolbar toolbar;
    private RecyclerView durationList;
    private SeekBar seekBarBrightness;
    private TextView tvBrightnessLevel;
    private SwitchCompat switchAutoBrightness;
    private SwitchCompat switchWakeUp;
    private ImageButton btnBrightnessUp;
    private ImageButton btnBrightnessDown;
    private TextView tvPermissionWarning;

    private ContentResolver contentResolver;
    private Window window;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_display);

        // 获取ContentResolver和Window
        contentResolver = getContentResolver();
        window = getWindow();

        initViews();
        setupListeners();

        // 检查权限并更新UI
        checkWriteSettingsPermission();
        updateBrightnessUI();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.display));

        // 亮度控制相关视图
        seekBarBrightness = findViewById(R.id.seekbar_screen_luminance);
        tvBrightnessLevel = findViewById(R.id.tv_brightness_level);
        switchAutoBrightness = findViewById(R.id.switch_auto_brightness);
        btnBrightnessUp = findViewById(R.id.btn_luminance_up);
        btnBrightnessDown = findViewById(R.id.btn_luminance_down);
        tvPermissionWarning = findViewById(R.id.tv_permission_warning);

        // 屏幕唤醒相关视图
        switchWakeUp = findViewById(R.id.switch_mute);
        durationList = findViewById(R.id.rv_duration_list);
        durationList.setAdapter(new DurationAdapter());
        durationList.setLayoutManager(new LinearLayoutManager(this));

        // 设置亮度SeekBar的最大值
        seekBarBrightness.setMax(MAX_BRIGHTNESS);
    }

    private void setupListeners() {
        // 亮度SeekBar监听器
        seekBarBrightness.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && canWriteSettings()) {
                    setBrightness(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
            }
        });

        // 亮度按钮监听器
        btnBrightnessUp.setOnClickListener(v -> increaseBrightness());
        btnBrightnessDown.setOnClickListener(v -> decreaseBrightness());

        // 自动亮度开关监听器
        switchAutoBrightness.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed() && canWriteSettings()) {
                setAutoBrightness(isChecked);
            }
        });

        // 屏幕唤醒开关监听器
        switchWakeUp.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (buttonView.isPressed()) {
                // 实现屏幕唤醒功能
                // 这里可以保存用户的选择到SharedPreferences
                Toast.makeText(DisplayActivity.this,
                        isChecked ? "Screen wake up enabled" : "Screen wake up disabled",
                        Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 检查是否有WRITE_SETTINGS权限
     */
    private boolean canWriteSettings() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.System.canWrite(this)) {
                tvPermissionWarning.setVisibility(View.VISIBLE);
                return false;
            } else {
                tvPermissionWarning.setVisibility(View.GONE);
                return true;
            }
        }
        return true;
    }

    /**
     * 请求WRITE_SETTINGS权限
     */
    private void checkWriteSettingsPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.System.canWrite(this)) {
                tvPermissionWarning.setVisibility(View.VISIBLE);
                tvPermissionWarning.setOnClickListener(v -> {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivityForResult(intent, REQUEST_WRITE_SETTINGS);
                });
            } else {
                tvPermissionWarning.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 更新亮度UI
     */
    private void updateBrightnessUI() {
        if (canWriteSettings()) {
            // 获取当前亮度
            int currentBrightness = getCurrentBrightness();

            // 更新SeekBar
            seekBarBrightness.setProgress(currentBrightness);

            // 更新亮度百分比显示
            int brightnessPercent = (int) (((float) currentBrightness / MAX_BRIGHTNESS) * 100);
            tvBrightnessLevel.setText(getString(R.string.brightness_level, brightnessPercent));

            // 更新自动亮度开关
            boolean isAutoBrightness = isAutoBrightnessEnabled();
            switchAutoBrightness.setChecked(isAutoBrightness);

            // 如果自动亮度开启，禁用手动亮度控制
            seekBarBrightness.setEnabled(!isAutoBrightness);
            btnBrightnessUp.setEnabled(!isAutoBrightness);
            btnBrightnessDown.setEnabled(!isAutoBrightness);
        }
    }

    /**
     * 获取当前亮度
     */
    private int getCurrentBrightness() {
        try {
            return Settings.System.getInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS);
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
            return MAX_BRIGHTNESS / 2; // 默认值
        }
    }

    /**
     * 检查自动亮度是否开启
     */
    private boolean isAutoBrightnessEnabled() {
        try {
            int mode = Settings.System.getInt(contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE);
            return mode == Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC;
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置亮度
     */
    private void setBrightness(int brightness) {
        if (canWriteSettings()) {
            // 设置系统亮度
            Settings.System.putInt(contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS, brightness);

            // 设置当前窗口亮度
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.screenBrightness = brightness / (float) MAX_BRIGHTNESS;
            window.setAttributes(layoutParams);

            // 更新UI
            updateBrightnessUI();
        }
    }

    /**
     * 增加亮度
     */
    private void increaseBrightness() {
        if (canWriteSettings()) {
            int currentBrightness = getCurrentBrightness();
            int newBrightness = Math.min(currentBrightness + (MAX_BRIGHTNESS / 10), MAX_BRIGHTNESS);
            setBrightness(newBrightness);
        }
    }

    /**
     * 减少亮度
     */
    private void decreaseBrightness() {
        if (canWriteSettings()) {
            int currentBrightness = getCurrentBrightness();
            int newBrightness = Math.max(currentBrightness - (MAX_BRIGHTNESS / 10), 0);
            setBrightness(newBrightness);
        }
    }

    /**
     * 设置自动亮度
     */
    private void setAutoBrightness(boolean enable) {
        if (canWriteSettings()) {
            Settings.System.putInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS_MODE,
                    enable ? Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC :
                            Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL);

            // 更新UI
            updateBrightnessUI();

            // 显示提示
            Toast.makeText(this,
                    enable ? R.string.auto_brightness_enabled : R.string.auto_brightness_disabled,
                    Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_WRITE_SETTINGS) {
            // 检查权限是否已授予
            checkWriteSettingsPermission();
            updateBrightnessUI();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 更新UI
        checkWriteSettingsPermission();
        updateBrightnessUI();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private class DurationAdapter extends RecyclerView.Adapter<WakeUpDurationViewHolder> {

        private final List<DurationSelectData> data;
        public DurationAdapter() {
            data = Arrays.asList(
                    new DurationSelectData(15, false),
                    new DurationSelectData(30, false),
                    new DurationSelectData(60, false)
            );
        }

        @NonNull
        @Override
        public WakeUpDurationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_wake_up_duration, parent, false);
            return new WakeUpDurationViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull WakeUpDurationViewHolder holder, int position) {
            holder.setDuration(data.get(position));
            holder.itemView.setOnClickListener(v -> {
                Log.d("DisplayAc", "onBindViewHolder:setOnClickListener " + position);
                for (int i = 0; i < data.size(); i++) {
                    data.get(i).selected = (i == position);
                }
                notifyDataSetChanged();
                int screenOnTime = data.get(position).duration;
                // TODO: 2025/5/20 设置对应的亮屏时间
            });
        }

        @Override
        public int getItemCount() {
            return data.size();
        }
    }

    private class WakeUpDurationViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvTitle;
        private final ImageView ivChecked;

        public WakeUpDurationViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.title);
            ivChecked = itemView.findViewById(R.id.iv_checked);
        }
        @SuppressLint("SetTextI18n")
        public void setDuration(DurationSelectData data) {
            tvTitle.setText(data.duration + getString(R.string.second));
            ivChecked.setVisibility(data.selected ? View.VISIBLE : View.GONE);
        }
    }

    private static class DurationSelectData {

        public int duration;
        boolean selected;

        public DurationSelectData(int duration, boolean selected) {
            this.duration = duration;
            this.selected = selected;
        }
    }

}