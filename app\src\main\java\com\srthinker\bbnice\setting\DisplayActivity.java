package com.srthinker.bbnice.setting;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;

import java.util.Arrays;
import java.util.List;

public class DisplayActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private RecyclerView durationList;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_display);
        initViews();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.location_switch));

        durationList = findViewById(R.id.rv_duration_list);
        durationList.setAdapter(new DurationAdapter());
        durationList.setLayoutManager(new LinearLayoutManager(this));
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private class DurationAdapter extends RecyclerView.Adapter<WakeUpDurationViewHolder> {

        private final List<DurationSelectData> data;
        public DurationAdapter() {
            data = Arrays.asList(
                    new DurationSelectData(15, false),
                    new DurationSelectData(30, false),
                    new DurationSelectData(60, false)
            );
        }

        @NonNull
        @Override
        public WakeUpDurationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_wake_up_duration, parent, false);
            return new WakeUpDurationViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull WakeUpDurationViewHolder holder, int position) {
            holder.setDuration(data.get(position));
            holder.itemView.setOnClickListener(v -> {
                Log.d("DisplayAc", "onBindViewHolder:setOnClickListener " + position);
                for (int i = 0; i < data.size(); i++) {
                    data.get(i).selected = (i == position);
                }
                notifyDataSetChanged();
            });
        }

        @Override
        public int getItemCount() {
            return data.size();
        }
    }

    private class WakeUpDurationViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvTitle;
        private final ImageView ivChecked;

        public WakeUpDurationViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.title);
            ivChecked = itemView.findViewById(R.id.iv_checked);
        }
        @SuppressLint("SetTextI18n")
        public void setDuration(DurationSelectData data) {
            tvTitle.setText(data.duration + getString(R.string.second));
            ivChecked.setVisibility(data.selected ? View.VISIBLE : View.GONE);
        }
    }

    private static class DurationSelectData {

        public int duration;
        boolean selected;

        public DurationSelectData(int duration, boolean selected) {
            this.duration = duration;
            this.selected = selected;
        }
    }

}