package com.srthinker.bbnice.api.repository;

import androidx.lifecycle.LiveData;

import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.LocationData;
import com.srthinker.bbnice.core.Repository;
import com.srthinker.bbnice.core.Result;

import java.util.List;

/**
 * 位置相关的Repository接口
 */
public interface LocationRepository extends Repository {


    /**
     * 获取当前位置
     * @return 当前位置LiveData
     */
    LiveData<Result<LocationData>> getCurrentLocation();

    /**
     * 上报位置
     * @param locationData 位置数据
     * @return 上报结果LiveData
     */
    LiveData<Result<BaseResponse>> reportLocation(LocationData locationData);
    
    /**
     * 上报多个位置
     * @param locationDataList 位置数据列表
     * @return 上报结果LiveData
     */
    LiveData<Result<BaseResponse>> reportLocations(List<LocationData> locationDataList);
}
