-- The C compiler identification is Clang 18.0.1
-- The CXX compiler identification is Clang 18.0.1
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: D:/Workspace/env/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: D:/Workspace/env/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found OpenCV: D:/Workspace/Projects/BBNice/AndroidClient/app/src/main/cpp/opencv (found version "4.11.0") 
-- Configuring done
-- Generating done
-- Build files have been written to: D:/Workspace/Projects/BBNice/AndroidClient/app/.cxx/Debug/3a5w3v5s/arm64-v8a
