package com.srthinker.bbnice.learn;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.srthinker.bbnice.App;
import com.srthinker.bbnice.common.RoomMessage;
import com.srthinker.bbnice.utils.DeviceIdUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LearnPagerAdapter extends FragmentStateAdapter {
    private static final String TAG = "ViewPagerAdapter";

    private static final int PAGE_COUNT = 3; // 页面数量

    private static final List<LearnPageType> pageSource = new ArrayList<>();

    private String  token;


    public void setToken(String token) {
        this.token = token;
    }

    public LearnPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
        pageSource.addAll(Arrays.asList(LearnPageType.values()));
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        Log.d(TAG, "-------createFragment: " + position);

        return LearnPageFragment.newInstance(pageSource.get(position)); // 页面编号从1开始
    }

    @Override
    public int getItemCount() {
        return pageSource.size();
    }
}