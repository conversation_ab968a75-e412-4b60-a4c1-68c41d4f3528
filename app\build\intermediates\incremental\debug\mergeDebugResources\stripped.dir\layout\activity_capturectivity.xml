<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".capture.CaptureActivity">

    <TextureView
        android:id="@+id/surface"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <View
        android:visibility="gone"
        android:id="@+id/result_bg_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <ImageView
        android:visibility="gone"
        android:id="@+id/result_bg"
        android:src="@mipmap/recognize_result"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <TextView
        android:textSize="22sp"
        android:id="@+id/tv_en_result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.18"
        app:layout_constraintStart_toStartOf="parent"
        tools:layout_editor_absoluteY="41dp" />

    <ImageView
        android:id="@+id/btn_cap"
        android:clickable="true"
        android:focusable="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:src="@mipmap/btn_capture"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:textSize="25sp"
        android:id="@+id/tv_cn_result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/tv_en_result"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:visibility="gone"
        android:id="@+id/btn_back"
        android:focusable="true"
        android:clickable="true"
        android:layout_marginTop="30dp"
        android:layout_marginStart="50dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:background="@mipmap/arrow_back"

        />

</androidx.constraintlayout.widget.ConstraintLayout>