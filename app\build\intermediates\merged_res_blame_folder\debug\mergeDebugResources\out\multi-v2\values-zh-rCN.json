{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "21236", "endColumns": "100", "endOffsets": "21332"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,132,133,134,135,136,143,144,152,175,182,194,195,232,233,234,235,236,237,238,239,240,241,242,243,244,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8742,8806,8868,8938,9008,9382,9473,9893,11074,11427,12084,12143,14185,14263,14324,14381,14437,14496,14554,14608,14693,14749,14807,14861,14926,15844,15918,15994,16116,16178,16240,16339,16418,16492,16542,16593,16659,16723,16792,16870,16941,17002,17073,17140,17200,17286,17365,17432,17515,17600,17674,17739,17815,17863,17936,18000,18076,18154,18216,18280,18343,18408,18488,18564,18642,18718,18772,19568", "endLines": "5,132,133,134,135,136,143,144,152,175,182,194,195,232,233,234,235,236,237,238,239,240,241,242,243,244,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,314", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,8801,8863,8933,9003,9080,9468,9574,9961,11131,11499,12138,12197,14258,14319,14376,14432,14491,14549,14603,14688,14744,14802,14856,14921,15013,15913,15989,16111,16173,16235,16334,16413,16487,16537,16588,16654,16718,16787,16865,16936,16997,17068,17135,17195,17281,17360,17427,17510,17595,17669,17734,17810,17858,17931,17995,18071,18149,18211,18275,18338,18403,18483,18559,18637,18713,18767,18822,19632"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\cff9521c234b1cb1e27a4b1f3461c77d\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "383,384,385,386", "startColumns": "4,4,4,4", "startOffsets": "23058,23107,23154,23241", "endColumns": "48,46,86,68", "endOffsets": "23102,23149,23236,23305"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "301,302", "startColumns": "4,4", "startOffsets": "18827,18928", "endColumns": "100,102", "endOffsets": "18923,19026"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,341", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,20877", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,20951"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,134,136,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5581,5679,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5629,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,36,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,49,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5624,5711,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5674,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "33,34,35,36,37,38,40,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,137,142,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161,162,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,186,187,188,189,196,197,198,201,202,203,204,205,206,207,208,209,210,211,245,246,247,248,249,250,251,252,253,254,255,256,257,258,303,304,305,306,307,308,309,310,311,313,315,316,317,318,319,320,321,322,323,324,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,342,343,344,345,349,350,351,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2897,2939,2983,3020,3071,3167,5456,5503,5551,5592,5692,5797,5872,5936,5985,6037,6097,6160,6224,6285,6346,6399,6453,6508,6563,6639,6717,6795,6879,6947,7020,7102,7179,7261,7326,7401,7475,7544,7620,7726,7837,7918,7990,8051,8097,8146,8204,8264,8329,8399,8455,8514,8579,8631,8681,9085,9344,9579,9615,9656,9697,9743,9782,9829,9966,10005,10055,10101,10164,10202,10257,10306,10352,10395,10571,10626,10665,10715,10766,10820,10859,10913,10967,11017,11136,11189,11236,11286,11333,11380,11675,11714,11762,11814,12202,12253,12304,12462,12503,12552,12592,12649,12713,12776,12865,12916,12963,13014,15018,15061,15106,15163,15219,15267,15324,15384,15445,15500,15565,15644,15726,15785,19031,19070,19127,19175,19227,19286,19337,19390,19447,19519,19637,19674,19726,19804,19857,19902,19940,19976,20023,20074,20171,20210,20250,20299,20359,20396,20441,20488,20534,20570,20621,20668,20725,20779,20829,20956,20993,21035,21075,21337,21376,21447,21887,21941,21988,22037,22087,22126,22175,22223,22273,22311,22361,22399,22458,22502,22549,22604,22664,22714,22771,22816,22870,22919", "endColumns": "42,41,43,36,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,38,49,50,53,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,42,44,56,55,47,56,59,60,54,64,78,81,58,58,38,56,47,51,58,50,52,56,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,46,45,35,50,46,56,53,49,47,36,41,39,48,38,70,48,53,46,48,49,38,48,47,49,37,49,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "2892,2934,2978,3015,3066,3118,3207,5498,5546,5587,5687,5792,5867,5931,5980,6032,6092,6155,6219,6280,6341,6394,6448,6503,6558,6634,6712,6790,6874,6942,7015,7097,7174,7256,7321,7396,7470,7539,7615,7721,7832,7913,7985,8046,8092,8141,8199,8259,8324,8394,8450,8509,8574,8626,8676,8737,9118,9377,9610,9651,9692,9738,9777,9824,9888,10000,10050,10096,10159,10197,10252,10301,10347,10390,10435,10621,10660,10710,10761,10815,10854,10908,10962,11012,11069,11184,11231,11281,11328,11375,11422,11709,11757,11809,11845,12248,12299,12351,12498,12547,12587,12644,12708,12771,12860,12911,12958,13009,13046,15056,15101,15158,15214,15262,15319,15379,15440,15495,15560,15639,15721,15780,15839,19065,19122,19170,19222,19281,19332,19385,19442,19476,19563,19669,19721,19799,19852,19897,19935,19971,20018,20069,20127,20205,20245,20294,20354,20391,20436,20483,20529,20565,20616,20663,20720,20774,20824,20872,20988,21030,21070,21119,21371,21442,21491,21936,21983,22032,22082,22121,22170,22218,22268,22306,22356,22394,22453,22497,22544,22599,22659,22709,22766,22811,22865,22914,22966"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\faceunity\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,138,139,140,141,163,164,183,184,185,190,191,192,193,199,200,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,312,325,346,347,352,353,354,355,356,357,358,381,382", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3123,3212,3262,3311,3365,3414,3469,3523,3577,3634,3684,3738,3792,3845,3898,3952,4012,4069,4130,4188,4245,4299,4351,4401,4456,4508,4558,4609,4660,4708,4759,4809,4862,4919,4973,5027,5084,5138,5190,5242,5297,5348,5404,9123,9173,9243,9293,10440,10502,11504,11573,11615,11850,11907,11969,12026,12356,12403,13051,13092,13149,13208,13264,13321,13380,13439,13498,13557,13616,13675,13736,13792,13849,13904,13961,14017,14078,14134,19481,20132,21124,21164,21496,21538,21591,21645,21702,21759,21827,22971,23017", "endColumns": "43,49,48,53,48,54,53,53,56,49,53,53,52,52,53,59,56,60,57,56,53,51,49,54,51,49,50,50,47,50,49,52,56,53,53,56,53,51,51,54,50,55,51,49,69,49,50,61,68,68,41,59,56,61,56,57,46,58,40,56,58,55,56,58,58,58,58,58,58,60,55,56,54,56,55,60,55,50,37,38,39,71,41,52,53,56,56,67,59,45,40", "endOffsets": "3162,3257,3306,3360,3409,3464,3518,3572,3629,3679,3733,3787,3840,3893,3947,4007,4064,4125,4183,4240,4294,4346,4396,4451,4503,4553,4604,4655,4703,4754,4804,4857,4914,4968,5022,5079,5133,5185,5237,5292,5343,5399,5451,9168,9238,9288,9339,10497,10566,11568,11610,11670,11902,11964,12021,12079,12398,12457,13087,13144,13203,13259,13316,13375,13434,13493,13552,13611,13670,13731,13787,13844,13899,13956,14012,14073,14129,14180,19514,20166,21159,21231,21533,21586,21640,21697,21754,21822,21882,23012,23053"}}]}, {"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-zh-rCN\\values-zh-rCN.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "344", "startColumns": "4", "startOffsets": "21062", "endColumns": "100", "endOffsets": "21158"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2445,2524,2598,2648,2699,2765,2829,2898,2976,3047,3108,3179,3246,3306,3392,3471,3538,3621,3706,3780,3845,3921,3969,4042,4106,4182,4260,4322,4386,4449,4514,4594,4670,4748,4824,4878,4933", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2440,2519,2593,2643,2694,2760,2824,2893,2971,3042,3103,3174,3241,3301,3387,3466,3533,3616,3701,3775,3840,3916,3964,4037,4101,4177,4255,4317,4381,4444,4509,4589,4665,4743,4819,4873,4928,4997"}, "to": {"startLines": "2,132,133,134,135,136,143,144,152,174,181,193,194,231,232,233,234,235,236,237,238,239,240,241,242,243,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,8742,8806,8868,8938,9008,9382,9473,9893,11035,11388,12045,12104,14146,14224,14285,14342,14398,14457,14515,14569,14654,14710,14768,14822,14887,15805,15879,15955,16077,16139,16201,16300,16379,16453,16503,16554,16620,16684,16753,16831,16902,16963,17034,17101,17161,17247,17326,17393,17476,17561,17635,17700,17776,17824,17897,17961,18037,18115,18177,18241,18304,18369,18449,18525,18603,18679,18733,19529", "endLines": "5,132,133,134,135,136,143,144,152,174,181,193,194,231,232,233,234,235,236,237,238,239,240,241,242,243,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,313", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,8801,8863,8933,9003,9080,9468,9574,9961,11092,11460,12099,12158,14219,14280,14337,14393,14452,14510,14564,14649,14705,14763,14817,14882,14974,15874,15950,16072,16134,16196,16295,16374,16448,16498,16549,16615,16679,16748,16826,16897,16958,17029,17096,17156,17242,17321,17388,17471,17556,17630,17695,17771,17819,17892,17956,18032,18110,18172,18236,18299,18364,18444,18520,18598,18674,18728,18783,19593"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\cff9521c234b1cb1e27a4b1f3461c77d\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,238", "endColumns": "48,46,86,68", "endOffsets": "99,146,233,302"}, "to": {"startLines": "378,379,380,381", "startColumns": "4,4,4,4", "startOffsets": "22834,22883,22930,23017", "endColumns": "48,46,86,68", "endOffsets": "22878,22925,23012,23081"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "300,301", "startColumns": "4,4", "startOffsets": "18788,18889", "endColumns": "100,102", "endOffsets": "18884,18987"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,338", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,20740", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,20814"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "98,46,2,16,136,112,48,35,34,135,175,177,173,154,150,141,145,146,159,158,160,163,151,142,152,181,186,184,185,168,169,187,179,182,166,183,180,167,172,176,178,174,170,155,148,143,144,147,156,164,153,140,165,134,149,171,78,8,43,44,45,57,23,58,59,9,67,203,204,11,102,101,100,33,47,196,157,128,195,22,91,88,85,90,89,3,92,87,86,4,95,77,60,42,24,20,51,12,107,66,70,68,126,127,69,131,71,28,79,192,194,193,191,210,211,208,197,205,207,206,190,209,82,199,50,198,138,103,56,113,7,137,106,75,76,74,80,96,105,54,31,32,14,29,55,25,13,200,202,201,10,115,30,139,114,99,15,49,81,104,21,37,36,39,38,17,63,61,62,97,111,125,122,116,118,119,117,123,120,124,110,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3897,1725,55,556,5609,4482,1812,1305,1257,5568,7891,8097,7735,6591,6377,5884,6098,6158,6892,6831,6956,7063,6426,5936,6480,8464,8859,8697,8775,7385,7453,8937,8313,8540,7251,8622,8390,7316,7659,7991,8202,7810,7526,6655,6281,5991,6040,6221,6716,7116,6535,5825,7186,5516,6327,7598,3123,248,1607,1643,1684,2174,794,2220,2267,286,2593,9724,9770,361,4077,4028,3982,1214,1767,9367,6781,5371,9313,755,3656,3492,3348,3599,3546,99,3710,3445,3398,146,3782,3075,2331,1571,833,655,1954,399,4327,2553,2758,2643,5219,5282,2707,5445,2815,981,3161,9155,9256,9200,9107,10179,10236,10059,9422,9833,9980,9898,9048,10120,3288,9529,1906,9477,5709,4132,2121,4534,214,5660,4290,2945,2997,2892,3204,3821,4254,2025,1105,1156,477,1018,2072,884,440,9586,9677,9631,325,4645,1058,5768,4591,3940,516,1857,3249,4183,706,1399,1352,1503,1453,593,2481,2383,2431,3859,4444,5160,5005,4696,4793,4848,4743,5049,4908,5106,4395,4953", "endColumns": "42,41,43,36,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,49,50,53,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,42,44,56,55,47,56,59,60,54,64,78,81,58,58,38,56,47,51,58,50,52,56,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,46,45,35,50,46,56,53,41,39,48,38,70,48,53,46,48,49,38,48,47,49,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "3935,1762,94,588,5655,4529,1852,1347,1300,5604,7986,8197,7805,6650,6421,5931,6153,6216,6951,6887,7012,7111,6475,5986,6530,8535,8932,8770,8854,7448,7521,9014,8385,8617,7311,8692,8459,7380,7730,8092,8308,7886,7593,6711,6322,6035,6093,6276,6776,7181,6586,5879,7246,5563,6372,7654,3156,281,1638,1679,1720,2215,828,2262,2326,320,2638,9765,9828,394,4127,4072,4023,1252,1807,9417,6826,5417,9362,789,3705,3541,3393,3651,3594,141,3755,3487,3440,188,3816,3118,2378,1602,879,701,2001,435,4371,2588,2810,2702,5277,5366,2753,5487,2861,1013,3199,9195,9308,9251,9150,10231,10291,10115,9472,9893,10054,9975,9102,10174,3322,9581,1949,9524,5763,4178,2169,4586,243,5704,4322,2992,3070,2940,3244,3854,4285,2067,1151,1209,511,1053,2116,939,472,9626,9719,9672,356,4691,1100,5820,4640,3977,551,1901,3283,4249,750,1448,1394,1547,1498,627,2525,2426,2476,3892,4477,5214,5044,4738,4843,4903,4788,5101,4948,5155,4439,5000"}, "to": {"startLines": "33,34,35,36,37,38,40,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,137,142,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161,162,165,166,167,168,169,170,171,172,173,175,176,177,178,179,180,185,186,187,188,195,196,197,200,201,202,203,204,205,206,207,208,209,210,244,245,246,247,248,249,250,251,252,253,254,255,256,257,302,303,304,305,306,307,308,309,310,312,314,315,316,317,318,319,320,321,322,323,325,326,327,328,329,330,331,332,333,334,335,336,337,339,340,341,345,346,347,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2897,2939,2983,3020,3071,3167,5456,5503,5551,5592,5692,5797,5872,5936,5985,6037,6097,6160,6224,6285,6346,6399,6453,6508,6563,6639,6717,6795,6879,6947,7020,7102,7179,7261,7326,7401,7475,7544,7620,7726,7837,7918,7990,8051,8097,8146,8204,8264,8329,8399,8455,8514,8579,8631,8681,9085,9344,9579,9615,9656,9697,9743,9782,9829,9966,10005,10055,10101,10164,10202,10257,10306,10352,10395,10571,10626,10676,10727,10781,10820,10874,10928,10978,11097,11150,11197,11247,11294,11341,11636,11675,11723,11775,12163,12214,12265,12423,12464,12513,12553,12610,12674,12737,12826,12877,12924,12975,14979,15022,15067,15124,15180,15228,15285,15345,15406,15461,15526,15605,15687,15746,18992,19031,19088,19136,19188,19247,19298,19351,19408,19480,19598,19635,19687,19765,19818,19863,19901,19937,19984,20035,20132,20171,20211,20260,20320,20357,20402,20449,20495,20531,20582,20629,20686,20819,20861,20901,21163,21202,21273,21713,21767,21814,21863,21913,21952,22001,22049,22099,22137,22175,22234,22278,22325,22380,22440,22490,22547,22592,22646,22695", "endColumns": "42,41,43,36,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,49,50,53,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,42,44,56,55,47,56,59,60,54,64,78,81,58,58,38,56,47,51,58,50,52,56,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,46,45,35,50,46,56,53,41,39,48,38,70,48,53,46,48,49,38,48,47,49,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "2892,2934,2978,3015,3066,3118,3207,5498,5546,5587,5687,5792,5867,5931,5980,6032,6092,6155,6219,6280,6341,6394,6448,6503,6558,6634,6712,6790,6874,6942,7015,7097,7174,7256,7321,7396,7470,7539,7615,7721,7832,7913,7985,8046,8092,8141,8199,8259,8324,8394,8450,8509,8574,8626,8676,8737,9118,9377,9610,9651,9692,9738,9777,9824,9888,10000,10050,10096,10159,10197,10252,10301,10347,10390,10435,10621,10671,10722,10776,10815,10869,10923,10973,11030,11145,11192,11242,11289,11336,11383,11670,11718,11770,11806,12209,12260,12312,12459,12508,12548,12605,12669,12732,12821,12872,12919,12970,13007,15017,15062,15119,15175,15223,15280,15340,15401,15456,15521,15600,15682,15741,15800,19026,19083,19131,19183,19242,19293,19346,19403,19437,19524,19630,19682,19760,19813,19858,19896,19932,19979,20030,20088,20166,20206,20255,20315,20352,20397,20444,20490,20526,20577,20624,20681,20735,20856,20896,20945,21197,21268,21317,21762,21809,21858,21908,21947,21996,22044,22094,22132,22170,22229,22273,22320,22375,22435,22485,22542,22587,22641,22690,22742"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\faceunity\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,99,149,198,252,301,356,410,464,521,571,625,679,732,785,839,899,956,1017,1075,1132,1186,1238,1288,1343,1395,1445,1496,1547,1595,1646,1696,1749,1806,1860,1914,1971,2025,2077,2129,2184,2235,2291,2343,2393,2463,2513,2641,2703,2772,2841,2883,2943,3000,3062,3119,3177,3224,3283,3324,3381,3440,3496,3553,3612,3671,3730,3789,3848,3907,3968,4024,4081,4136,4193,4249,4310,4366,4417,4455,4494,4534,4606,4648,4701,4755,4812,4869,4937,4997,5043", "endColumns": "43,49,48,53,48,54,53,53,56,49,53,53,52,52,53,59,56,60,57,56,53,51,49,54,51,49,50,50,47,50,49,52,56,53,53,56,53,51,51,54,50,55,51,49,69,49,50,61,68,68,41,59,56,61,56,57,46,58,40,56,58,55,56,58,58,58,58,58,58,60,55,56,54,56,55,60,55,50,37,38,39,71,41,52,53,56,56,67,59,45,40", "endOffsets": "94,144,193,247,296,351,405,459,516,566,620,674,727,780,834,894,951,1012,1070,1127,1181,1233,1283,1338,1390,1440,1491,1542,1590,1641,1691,1744,1801,1855,1909,1966,2020,2072,2124,2179,2230,2286,2338,2388,2458,2508,2559,2698,2767,2836,2878,2938,2995,3057,3114,3172,3219,3278,3319,3376,3435,3491,3548,3607,3666,3725,3784,3843,3902,3963,4019,4076,4131,4188,4244,4305,4361,4412,4450,4489,4529,4601,4643,4696,4750,4807,4864,4932,4992,5038,5079"}, "to": {"startLines": "39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,138,139,140,141,163,164,182,183,184,189,190,191,192,198,199,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,311,324,342,343,348,349,350,351,352,353,354,376,377", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3123,3212,3262,3311,3365,3414,3469,3523,3577,3634,3684,3738,3792,3845,3898,3952,4012,4069,4130,4188,4245,4299,4351,4401,4456,4508,4558,4609,4660,4708,4759,4809,4862,4919,4973,5027,5084,5138,5190,5242,5297,5348,5404,9123,9173,9243,9293,10440,10502,11465,11534,11576,11811,11868,11930,11987,12317,12364,13012,13053,13110,13169,13225,13282,13341,13400,13459,13518,13577,13636,13697,13753,13810,13865,13922,13978,14039,14095,19442,20093,20950,20990,21322,21364,21417,21471,21528,21585,21653,22747,22793", "endColumns": "43,49,48,53,48,54,53,53,56,49,53,53,52,52,53,59,56,60,57,56,53,51,49,54,51,49,50,50,47,50,49,52,56,53,53,56,53,51,51,54,50,55,51,49,69,49,50,61,68,68,41,59,56,61,56,57,46,58,40,56,58,55,56,58,58,58,58,58,58,60,55,56,54,56,55,60,55,50,37,38,39,71,41,52,53,56,56,67,59,45,40", "endOffsets": "3162,3257,3306,3360,3409,3464,3518,3572,3629,3679,3733,3787,3840,3893,3947,4007,4064,4125,4183,4240,4294,4346,4396,4451,4503,4553,4604,4655,4703,4754,4804,4857,4914,4968,5022,5079,5133,5185,5237,5292,5343,5399,5451,9168,9238,9288,9339,10497,10566,11529,11571,11631,11863,11925,11982,12040,12359,12418,13048,13105,13164,13220,13277,13336,13395,13454,13513,13572,13631,13692,13748,13805,13860,13917,13973,14034,14090,14141,19475,20127,20985,21057,21359,21412,21466,21523,21580,21648,21708,22788,22829"}}]}]}