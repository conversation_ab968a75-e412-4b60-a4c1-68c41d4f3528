{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "188", "startColumns": "4", "startOffsets": "12443", "endColumns": "100", "endOffsets": "12539"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,76,77,78,79,80,86,87,88,92,93,101,102,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5142,5206,5268,5338,5408,5744,5835,5941,6184,6246,6728,6787,8086,8164,8225,8282,8338,8397,8455,8509,8594,8650,8708,8762,8827,8919,8993,9069,9191,9253,9315,9414,9493,9567,9617,9668,9734,9798,9867,9945,10016,10077,10148,10215,10275,10361,10440,10507,10590,10675,10749,10814,10890,10938,11011,11075,11151,11229,11291,11355,11418,11483,11563,11639,11717,11793,11847,12144", "endLines": "5,76,77,78,79,80,86,87,88,92,93,101,102,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,183", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,5201,5263,5333,5403,5480,5830,5936,6009,6241,6318,6782,6841,8159,8220,8277,8333,8392,8450,8504,8589,8645,8703,8757,8822,8914,8988,9064,9186,9248,9310,9409,9488,9562,9612,9663,9729,9793,9862,9940,10011,10072,10143,10210,10270,10356,10435,10502,10585,10670,10744,10809,10885,10933,11006,11070,11146,11224,11286,11350,11413,11478,11558,11634,11712,11788,11842,11897,12208"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\cff9521c234b1cb1e27a4b1f3461c77d\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "198,199,200,201", "startColumns": "4,4,4,4", "startOffsets": "13022,13071,13118,13205", "endColumns": "48,46,86,68", "endOffsets": "13066,13113,13200,13269"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "180,181", "startColumns": "4,4", "startOffsets": "11902,12003", "endColumns": "100,102", "endOffsets": "11998,12101"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,12252", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,12326"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\faceunity\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,81,82,83,84,85,89,90,91,94,95,96,97,98,99,100,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,182,184,186,187,189,190,191,192,193,194,195,196,197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2898,2948,2997,3051,3100,3155,3209,3263,3320,3370,3424,3478,3531,3584,3638,3698,3755,3816,3874,3931,3985,4037,4087,4142,4194,4244,4295,4346,4394,4445,4495,4548,4605,4659,4713,4770,4824,4876,4928,4983,5034,5090,5485,5535,5605,5655,5706,6014,6053,6115,6323,6392,6434,6494,6551,6613,6670,6846,6893,6952,6993,7050,7109,7165,7222,7281,7340,7399,7458,7517,7576,7637,7693,7750,7805,7862,7918,7979,8035,12106,12213,12331,12371,12544,12586,12639,12693,12750,12807,12875,12935,12981", "endColumns": "43,49,48,53,48,54,53,53,56,49,53,53,52,52,53,59,56,60,57,56,53,51,49,54,51,49,50,50,47,50,49,52,56,53,53,56,53,51,51,54,50,55,51,49,69,49,50,37,38,61,68,68,41,59,56,61,56,57,46,58,40,56,58,55,56,58,58,58,58,58,58,60,55,56,54,56,55,60,55,50,37,38,39,71,41,52,53,56,56,67,59,45,40", "endOffsets": "2893,2943,2992,3046,3095,3150,3204,3258,3315,3365,3419,3473,3526,3579,3633,3693,3750,3811,3869,3926,3980,4032,4082,4137,4189,4239,4290,4341,4389,4440,4490,4543,4600,4654,4708,4765,4819,4871,4923,4978,5029,5085,5137,5530,5600,5650,5701,5739,6048,6110,6179,6387,6429,6489,6546,6608,6665,6723,6888,6947,6988,7045,7104,7160,7217,7276,7335,7394,7453,7512,7571,7632,7688,7745,7800,7857,7913,7974,8030,8081,12139,12247,12366,12438,12581,12634,12688,12745,12802,12870,12930,12976,13017"}}]}, {"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-zh-rCN\\values-zh-rCN.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\e33f3c5e78c7b440b2bd748a3baa2e42\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "248", "startColumns": "4", "startOffsets": "15289", "endColumns": "100", "endOffsets": "15385"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\131f10619d340c0fef3711908f5a0bff\\transformed\\material-1.10.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2445,2524,2598,2648,2699,2765,2829,2898,2976,3047,3108,3179,3246,3306,3392,3471,3538,3621,3706,3780,3845,3921,3969,4042,4106,4182,4260,4322,4386,4449,4514,4594,4670,4748,4824,4878,4933", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2440,2519,2593,2643,2694,2760,2824,2893,2971,3042,3103,3174,3241,3301,3387,3466,3533,3616,3701,3775,3840,3916,3964,4037,4101,4177,4255,4317,4381,4444,4509,4589,4665,4743,4819,4873,4928,4997"}, "to": {"startLines": "2,81,82,83,84,85,92,93,101,114,121,132,133,166,167,168,169,170,171,172,173,174,175,176,177,178,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5365,5429,5491,5561,5631,6005,6096,6516,7189,7542,8160,8219,10012,10090,10151,10208,10264,10323,10381,10435,10520,10576,10634,10688,10753,10888,10962,11038,11160,11222,11284,11383,11462,11536,11586,11637,11703,11767,11836,11914,11985,12046,12117,12184,12244,12330,12409,12476,12559,12644,12718,12783,12859,12907,12980,13044,13120,13198,13260,13324,13387,13452,13532,13608,13686,13762,13816,14248", "endLines": "5,81,82,83,84,85,92,93,101,114,121,132,133,166,167,168,169,170,171,172,173,174,175,176,177,178,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,228", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,5424,5486,5556,5626,5703,6091,6197,6584,7246,7614,8214,8273,10085,10146,10203,10259,10318,10376,10430,10515,10571,10629,10683,10748,10840,10957,11033,11155,11217,11279,11378,11457,11531,11581,11632,11698,11762,11831,11909,11980,12041,12112,12179,12239,12325,12404,12471,12554,12639,12713,12778,12854,12902,12975,13039,13115,13193,13255,13319,13382,13447,13527,13603,13681,13757,13811,13866,14312"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\cff9521c234b1cb1e27a4b1f3461c77d\\transformed\\jetified-zxing-android-embedded-4.3.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,238", "endColumns": "48,46,86,68", "endOffsets": "99,146,233,302"}, "to": {"startLines": "266,267,268,269", "startColumns": "4,4,4,4", "startOffsets": "16264,16313,16360,16447", "endColumns": "48,46,86,68", "endOffsets": "16308,16355,16442,16511"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\159d8e172c6b8737be1ec453195ecd5f\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "222,223", "startColumns": "4,4", "startOffsets": "13871,13972", "endColumns": "100,102", "endOffsets": "13967,14070"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\bfc74f1504facdc13ec82146aa98274a\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,15009", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,15083"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "2,15,45,33,32,75,8,41,42,43,54,21,55,56,9,64,11,31,44,20,86,83,80,85,84,3,87,82,81,4,74,57,40,22,18,48,12,63,67,65,66,68,26,76,47,53,7,72,73,71,77,51,29,30,27,52,23,13,10,28,14,46,19,35,34,37,36,60,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,517,1692,1227,1179,3004,248,1529,1565,1606,2055,716,2101,2148,286,2474,361,1136,1647,677,3459,3295,3151,3402,3349,99,3513,3248,3201,146,2956,2212,1493,755,577,1836,399,2434,2639,2524,2588,2696,903,3042,1788,2002,214,2826,2878,2773,3085,1906,1027,1078,940,1953,806,440,325,980,477,1739,628,1321,1274,1425,1375,2362,2264,2312", "endColumns": "43,36,46,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,50,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "94,549,1734,1269,1222,3037,281,1560,1601,1642,2096,750,2143,2207,320,2519,394,1174,1687,711,3508,3344,3196,3454,3397,141,3558,3290,3243,188,2999,2259,1524,801,623,1882,435,2469,2691,2583,2634,2742,935,3080,1831,2050,243,2873,2951,2821,3125,1948,1073,1131,975,1997,861,472,356,1022,512,1783,672,1370,1316,1469,1420,2406,2307,2357"}, "to": {"startLines": "33,34,36,79,80,86,91,94,95,96,97,98,99,100,102,103,104,105,106,109,110,111,112,113,115,116,117,118,119,120,125,126,127,134,135,136,139,140,141,142,143,144,145,179,224,225,226,229,230,231,232,233,234,235,237,238,239,240,241,242,244,245,249,257,258,259,260,261,262,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,2898,2979,5270,5317,5708,5967,6202,6238,6279,6320,6366,6405,6452,6589,6628,6678,6716,6759,6935,6974,7028,7082,7132,7251,7304,7351,7401,7448,7495,7790,7838,7890,8278,8329,8380,8537,8578,8618,8675,8739,8790,8841,10845,14075,14123,14176,14317,14369,14447,14500,14545,14592,14643,14740,14780,14829,14889,14926,14962,15088,15128,15390,15830,15884,15931,15980,16030,16079,16127", "endColumns": "43,36,46,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,50,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "2893,2930,3021,5312,5360,5741,6000,6233,6274,6315,6361,6400,6447,6511,6623,6673,6711,6754,6799,6969,7023,7077,7127,7184,7299,7346,7396,7443,7490,7537,7833,7885,7921,8324,8375,8426,8573,8613,8670,8734,8785,8836,8873,10883,14118,14171,14205,14364,14442,14495,14540,14587,14638,14696,14775,14824,14884,14921,14957,15004,15123,15172,15434,15879,15926,15975,16025,16074,16122,16172"}}, {"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\faceunity\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,99,149,198,252,301,356,410,464,521,571,625,679,732,785,839,899,956,1017,1075,1132,1186,1238,1288,1343,1395,1445,1496,1547,1595,1646,1696,1749,1806,1860,1914,1971,2025,2077,2129,2184,2235,2291,2343,2393,2463,2513,2641,2703,2772,2841,2883,2943,3000,3062,3119,3177,3224,3283,3324,3381,3440,3496,3553,3612,3671,3730,3789,3848,3907,3968,4024,4081,4136,4193,4249,4310,4366,4417,4455,4494,4534,4606,4648,4701,4755,4812,4869,4937,4997,5043", "endColumns": "43,49,48,53,48,54,53,53,56,49,53,53,52,52,53,59,56,60,57,56,53,51,49,54,51,49,50,50,47,50,49,52,56,53,53,56,53,51,51,54,50,55,51,49,69,49,50,61,68,68,41,59,56,61,56,57,46,58,40,56,58,55,56,58,58,58,58,58,58,60,55,56,54,56,55,60,55,50,37,38,39,71,41,52,53,56,56,67,59,45,40", "endOffsets": "94,144,193,247,296,351,405,459,516,566,620,674,727,780,834,894,951,1012,1070,1127,1181,1233,1283,1338,1390,1440,1491,1542,1590,1641,1691,1744,1801,1855,1909,1966,2020,2072,2124,2179,2230,2286,2338,2388,2458,2508,2559,2698,2767,2836,2878,2938,2995,3057,3114,3172,3219,3278,3319,3376,3435,3491,3548,3607,3666,3725,3784,3843,3902,3963,4019,4076,4131,4188,4244,4305,4361,4412,4450,4489,4529,4601,4643,4696,4750,4807,4864,4932,4992,5038,5079"}, "to": {"startLines": "35,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,87,88,89,90,107,108,122,123,124,128,129,130,131,137,138,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,227,236,246,247,250,251,252,253,254,255,256,264,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2935,3026,3076,3125,3179,3228,3283,3337,3391,3448,3498,3552,3606,3659,3712,3766,3826,3883,3944,4002,4059,4113,4165,4215,4270,4322,4372,4423,4474,4522,4573,4623,4676,4733,4787,4841,4898,4952,5004,5056,5111,5162,5218,5746,5796,5866,5916,6804,6866,7619,7688,7730,7926,7983,8045,8102,8431,8478,8878,8919,8976,9035,9091,9148,9207,9266,9325,9384,9443,9502,9563,9619,9676,9731,9788,9844,9905,9961,14210,14701,15177,15217,15439,15481,15534,15588,15645,15702,15770,16177,16223", "endColumns": "43,49,48,53,48,54,53,53,56,49,53,53,52,52,53,59,56,60,57,56,53,51,49,54,51,49,50,50,47,50,49,52,56,53,53,56,53,51,51,54,50,55,51,49,69,49,50,61,68,68,41,59,56,61,56,57,46,58,40,56,58,55,56,58,58,58,58,58,58,60,55,56,54,56,55,60,55,50,37,38,39,71,41,52,53,56,56,67,59,45,40", "endOffsets": "2974,3071,3120,3174,3223,3278,3332,3386,3443,3493,3547,3601,3654,3707,3761,3821,3878,3939,3997,4054,4108,4160,4210,4265,4317,4367,4418,4469,4517,4568,4618,4671,4728,4782,4836,4893,4947,4999,5051,5106,5157,5213,5265,5791,5861,5911,5962,6861,6930,7683,7725,7785,7978,8040,8097,8155,8473,8532,8914,8971,9030,9086,9143,9202,9261,9320,9379,9438,9497,9558,9614,9671,9726,9783,9839,9900,9956,10007,14243,14735,15212,15284,15476,15529,15583,15640,15697,15765,15825,16218,16259"}}]}]}