<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_display" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_display.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_display_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="188" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="37"/></Target><Target id="@+id/tv_media_luminance_label" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="30" endOffset="60"/></Target><Target id="@+id/media_luminance_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="32" startOffset="4" endLine="70" endOffset="55"/></Target><Target id="@+id/btn_luminance_down" view="ImageButton"><Expressions/><location startLine="39" startOffset="8" endLine="47" endOffset="55"/></Target><Target id="@+id/seekbar_screen_luminance" view="SeekBar"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="55"/></Target><Target id="@+id/btn_luminance_up" view="ImageButton"><Expressions/><location startLine="60" startOffset="8" endLine="68" endOffset="55"/></Target><Target id="@+id/tv_brightness_level" view="TextView"><Expressions/><location startLine="72" startOffset="4" endLine="80" endOffset="78"/></Target><Target id="@+id/auto_brightness_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="83" startOffset="4" endLine="111" endOffset="55"/></Target><Target id="@+id/tv_auto_brightness_label" view="TextView"><Expressions/><location startLine="92" startOffset="8" endLine="101" endOffset="55"/></Target><Target id="@+id/switch_auto_brightness" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="103" startOffset="8" endLine="109" endOffset="55"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="113" startOffset="4" endLine="118" endOffset="78"/></Target><Target id="@+id/mute_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="121" startOffset="4" endLine="149" endOffset="55"/></Target><Target id="@+id/tv_mute_label" view="TextView"><Expressions/><location startLine="130" startOffset="8" endLine="139" endOffset="55"/></Target><Target id="@+id/switch_mute" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="141" startOffset="8" endLine="147" endOffset="55"/></Target><Target id="@+id/divider2" view="View"><Expressions/><location startLine="151" startOffset="4" endLine="156" endOffset="67"/></Target><Target id="@+id/tv_wake_up_duration_label" view="TextView"><Expressions/><location startLine="158" startOffset="4" endLine="166" endOffset="60"/></Target><Target id="@+id/rv_duration_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="168" startOffset="4" endLine="174" endOffset="45"/></Target><Target id="@+id/tv_permission_warning" view="TextView"><Expressions/><location startLine="176" startOffset="4" endLine="186" endOffset="69"/></Target></Targets></Layout>