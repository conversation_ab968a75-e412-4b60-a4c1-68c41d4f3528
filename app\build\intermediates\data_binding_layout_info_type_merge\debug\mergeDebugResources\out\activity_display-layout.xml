<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_display" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_display.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_display_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="136" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="37"/></Target><Target id="@+id/tv_media_luminance_label" view="TextView"><Expressions/><location startLine="22" startOffset="4" endLine="30" endOffset="60"/></Target><Target id="@+id/media_luminance_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="32" startOffset="4" endLine="70" endOffset="55"/></Target><Target id="@+id/btn_media_luminance_down" view="ImageButton"><Expressions/><location startLine="39" startOffset="8" endLine="47" endOffset="55"/></Target><Target id="@+id/seekbar_media_luminance" view="SeekBar"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="55"/></Target><Target id="@+id/btn_media_luminance_up" view="ImageButton"><Expressions/><location startLine="60" startOffset="8" endLine="68" endOffset="55"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="72" startOffset="4" endLine="77" endOffset="78"/></Target><Target id="@+id/mute_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="80" startOffset="4" endLine="108" endOffset="55"/></Target><Target id="@+id/tv_mute_label" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="55"/></Target><Target id="@+id/switch_mute" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="100" startOffset="8" endLine="106" endOffset="55"/></Target><Target id="@+id/divider2" view="View"><Expressions/><location startLine="110" startOffset="4" endLine="115" endOffset="67"/></Target><Target id="@+id/tv_wake_up_duration_label" view="TextView"><Expressions/><location startLine="117" startOffset="4" endLine="125" endOffset="60"/></Target><Target id="@+id/rv_duration_list" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="127" startOffset="4" endLine="133" endOffset="45"/></Target></Targets></Layout>