<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical">

        <com.faceunity.nama.control.FaceBeautySkinControlView
            android:id="@+id/control_beauty_skin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_gravity="bottom" />

        <com.faceunity.nama.control.FaceBeautyShapeControlView
            android:id="@+id/control_beauty_shape"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_gravity="bottom" />

        <com.faceunity.nama.control.FilterControlView
            android:id="@+id/control_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_gravity="bottom" />

        <com.faceunity.nama.control.BodyBeautyControlView
            android:id="@+id/control_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:visibility="gone" />

        <com.faceunity.nama.control.MakeupControlView
            android:id="@+id/control_makeup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:visibility="gone" />

        <com.faceunity.nama.control.PropControlView
            android:id="@+id/control_prop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/x1"
            android:background="@color/primary_list"
            android:src="@color/divider_line_color" />
    </LinearLayout>

    <com.faceunity.nama.checkbox.CheckGroup
        android:id="@+id/group_function"
        android:layout_width="match_parent"
        android:layout_height="@dimen/x98"
        android:layout_gravity="bottom"
        android:background="@color/primary_list"
        android:orientation="horizontal">

        <com.faceunity.nama.checkbox.CheckBoxCompat
            android:id="@+id/radio_skin_beauty"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/beauty_radio_skin_beauty"
            android:textColor="@color/bottom_radio_color"
            android:textSize="@dimen/text_size_26" />

        <com.faceunity.nama.checkbox.CheckBoxCompat
            android:id="@+id/radio_shape_beauty"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/beauty_radio_face_shape"
            android:textColor="@color/bottom_radio_color"
            android:textSize="@dimen/text_size_26" />

        <com.faceunity.nama.checkbox.CheckBoxCompat
            android:id="@+id/radio_filter"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/beauty_radio_filter"
            android:textColor="@color/bottom_radio_color"
            android:textSize="@dimen/text_size_26" />

        <com.faceunity.nama.checkbox.CheckBoxCompat
            android:id="@+id/radio_sticker"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/home_function_name_sticker"
            android:textColor="@color/bottom_radio_color"
            android:textSize="@dimen/text_size_26" />

        <com.faceunity.nama.checkbox.CheckBoxCompat
            android:id="@+id/radio_makeup"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/home_function_name_makeup"
            android:textColor="@color/bottom_radio_color"
            android:textSize="@dimen/text_size_26" />

        <com.faceunity.nama.checkbox.CheckBoxCompat
            android:id="@+id/radio_body"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/transparent"
            android:button="@null"
            android:gravity="center"
            android:text="@string/home_function_name_beauty_body"
            android:textColor="@color/bottom_radio_color"
            android:textSize="@dimen/text_size_26" />
    </com.faceunity.nama.checkbox.CheckGroup>
</LinearLayout>