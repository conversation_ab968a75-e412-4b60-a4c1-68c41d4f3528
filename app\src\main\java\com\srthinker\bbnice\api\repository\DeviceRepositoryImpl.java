package com.srthinker.bbnice.api.repository;

import android.content.Context;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiConstants;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.ApiResponse;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.DeviceListResponse;
import com.srthinker.bbnice.api.LoginResponse;
import com.srthinker.bbnice.api.MediaResponse;
import com.srthinker.bbnice.api.PetNameResponse;
import com.srthinker.bbnice.api.QrCodeResponse;
import com.srthinker.bbnice.api.WhitelistResponse;
import com.srthinker.bbnice.core.ErrorHandler;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.utils.DeviceIdUtils;
import com.srthinker.bbnice.utils.HttpUtils;
import com.srthinker.bbnice.utils.JsonUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * DeviceRepository接口的实现类
 */
public class DeviceRepositoryImpl implements DeviceRepository {

    private final BBNiceApi api;

    // 认证Token

    /**
     * 构造函数
     * @param context 上下文
     */
    public DeviceRepositoryImpl(Context context) {
        this.api = new BBNiceApi(context);
    }

    /**
     * 设置认证Token
     * @param token 认证Token
     */
    public void setAuthToken(String token) {
        api.setAuthToken(token);
    }

    @Override
    public LiveData<Result<LoginResponse>> login() {
        MutableLiveData<Result<LoginResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.deviceLogin(new ApiCallback<LoginResponse>() {
            @Override
            public void onSuccess(LoginResponse response) {
                // 保存Token
                if (response.getData() != null) {
                    setAuthToken(response.getData().getAccessToken());
                }
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<QrCodeResponse>> getDeviceQrCode() {
        MutableLiveData<Result<QrCodeResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.getDeviceQrCode(new ApiCallback<QrCodeResponse>() {
            @Override
            public void onSuccess(QrCodeResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> unbindDevice() {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.unbindDevice(new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<DeviceListResponse>> getDeviceList() {
        MutableLiveData<Result<DeviceListResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.getDeviceList(new ApiCallback<DeviceListResponse>() {
            @Override
            public void onSuccess(DeviceListResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<PetNameResponse>> updatePetName(String deviceId, String petName) {
        MutableLiveData<Result<PetNameResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.updatePetName(deviceId, petName, new ApiCallback<PetNameResponse>() {
            @Override
            public void onSuccess(PetNameResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> addPhoneWhitelist(String deviceId, String phone, String name) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.addPhoneWhitelist(deviceId, phone, name, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> deletePhoneWhitelist(String deviceId, String phone) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.deletePhoneWhitelist(deviceId, phone, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<WhitelistResponse>> getPhoneWhitelist(String deviceId) {
        MutableLiveData<Result<WhitelistResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.getPhoneWhitelist(deviceId, new ApiCallback<WhitelistResponse>() {
            @Override
            public void onSuccess(WhitelistResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<BaseResponse>> reportDeviceStatus(int batteryLevel, String networkStatus,
                                                           int signalStrength, long timestamp) {
        MutableLiveData<Result<BaseResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());

        api.reportDeviceStatus(batteryLevel, networkStatus, signalStrength, timestamp, new ApiCallback<BaseResponse>() {
            @Override
            public void onSuccess(BaseResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<MediaResponse>> uploadMedia(String type, byte[] file) {
        MutableLiveData<Result<MediaResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.uploadMedia(type, file, new ApiCallback<MediaResponse>() {
            @Override
            public void onSuccess(MediaResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }

    @Override
    public LiveData<Result<MediaResponse>> uploadMedia(String type, File[] files) {
        MutableLiveData<Result<MediaResponse>> result = new MutableLiveData<>();
        result.setValue(Result.loading());
        api.uploadMedia(type, files, new ApiCallback<MediaResponse>() {
            @Override
            public void onSuccess(MediaResponse response) {
                result.postValue(Result.success(response));
            }

            @Override
            public void onError(ApiError error) {
                result.postValue(ErrorHandler.handleApiError(error));
            }
        });

        return result;
    }
}
