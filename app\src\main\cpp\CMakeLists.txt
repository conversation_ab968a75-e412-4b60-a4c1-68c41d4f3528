# 设置CMake最低版本要求
cmake_minimum_required(VERSION 3.22.1)

# 设置项目名称
project("bbnice")

# OpenCV-Android
set(OpenCV_DIR ${CMAKE_SOURCE_DIR}/opencv/sdk/native/jni)
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})


message("OpenCV_LIBS=${OpenCV_LIBS}")
message("OpenCV_INCLUDE_DIRS=${OpenCV_INCLUDE_DIRS}")

aux_source_directory(. MAIN_SRC)

# 添加库
add_library(${CMAKE_PROJECT_NAME} SHARED
        # 源文件
        ${MAIN_SRC}
)

# 链接库
target_link_libraries(${CMAKE_PROJECT_NAME}
        ${OpenCV_LIBS}
        android
        jnigraphics
        log
)
