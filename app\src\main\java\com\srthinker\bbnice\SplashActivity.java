package com.srthinker.bbnice;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.ProgressBar;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;

import com.srthinker.bbnice.api.bean.LoginResponse;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.home.HomeActivity;
import com.srthinker.bbnice.registration.DeviceRegistrationActivity;
import com.srthinker.bbnice.setting.WifiActivity;
import com.srthinker.bbnice.utils.SPUtils;

/**
 * 启动页Activity
 * 用于检查网络连接状态和设备注册状态，并跳转到相应的界面
 */
public class SplashActivity extends AppCompatActivity {
    private static final String TAG = "SplashActivity";
    private static final String KEY_DEVICE_REGISTERED = "device_registered";
    private static final long SPLASH_DELAY = 1000; // 1秒的启动页显示时间

    private ProgressBar progressBar;
    private DeviceRepository deviceRepository;
    private Handler handler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // 初始化视图
        progressBar = findViewById(R.id.progressBar);
        
        // 初始化Repository
        deviceRepository = ApiRepositoryProvider.getInstance(this).getDeviceRepository();
        
        // 初始化Handler
        handler = new Handler(Looper.getMainLooper());
        
        // 延迟一段时间后检查状态并跳转
        handler.postDelayed(this::checkStatusAndNavigate, SPLASH_DELAY);
    }
    
    /**
     * 检查状态并跳转到相应的界面
     */
    private void checkStatusAndNavigate() {
        // 检查网络连接状态
        if (!isNetworkConnected()) {
            // 没有网络连接，跳转到WiFi设置界面
            Log.d(TAG, "No network connection, navigating to WiFi settings");
            navigateToWifiSettings();
            return;
        }
        
        // 检查设备是否已注册
        boolean isRegistered = SPUtils.getInstance().getBoolean(KEY_DEVICE_REGISTERED);
        if (isRegistered) {
            // 设备已注册，尝试登录验证
            Log.d(TAG, "Device registered, trying to login");
            tryLogin();
        } else {
            // 设备未注册，跳转到设备注册界面
            Log.d(TAG, "Device not registered, navigating to registration");
            navigateToRegistration();
        }
    }
    
    /**
     * 尝试登录
     */
    private void tryLogin() {
        progressBar.setVisibility(View.VISIBLE);
        
        deviceRepository.login().observe(this, new Observer<Result<LoginResponse>>() {
            @Override
            public void onChanged(Result<LoginResponse> result) {
                progressBar.setVisibility(View.GONE);
                
                if (result.isSuccess()) {
                    // 登录成功，跳转到主界面
                    Log.d(TAG, "Login successful, navigating to home");
                    navigateToHome();
                } else {
                    // 登录失败，可能是设备未注册或网络问题
                    Log.d(TAG, "Login failed, navigating to registration");
                    // 清除注册状态
                    SPUtils.getInstance().saveBoolean(KEY_DEVICE_REGISTERED, false);
                    navigateToRegistration();
                }
            }
        });
    }
    
    /**
     * 检查网络连接状态
     * @return 是否已连接网络
     */
    private boolean isNetworkConnected() {
        ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            return false;
        }
        
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        return activeNetwork != null && activeNetwork.isConnected();
    }
    
    /**
     * 跳转到WiFi设置界面
     */
    private void navigateToWifiSettings() {
        Intent intent = new Intent(this, WifiActivity.class);
        intent.putExtra("from_splash", true); // 标记来自启动页
        startActivity(intent);
        finish();
    }
    
    /**
     * 跳转到设备注册界面
     */
    private void navigateToRegistration() {
        Intent intent = new Intent(this, DeviceRegistrationActivity.class);
        startActivity(intent);
        finish();
    }
    
    /**
     * 跳转到主界面
     */
    private void navigateToHome() {
        Intent intent = new Intent(this, HomeActivity.class);
        startActivity(intent);
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
    }
}
