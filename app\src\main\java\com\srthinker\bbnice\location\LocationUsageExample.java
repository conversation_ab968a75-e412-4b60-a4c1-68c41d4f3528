package com.srthinker.bbnice.location;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.repository.LocationRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;

/**
 * 位置使用示例类
 * 演示如何在应用中使用位置功能
 */
public class LocationUsageExample {
    private static final String TAG = "LocationUsageExample";

    /**
     * 获取并上报当前位置
     * @param context 上下文
     */
    public static void getAndReportCurrentLocation(Context context) {
        // 获取位置管理器
        LocationManager locationManager = LocationManager.getInstance(context);

        // 获取当前位置
        locationManager.getCurrentLocation(new ApiCallback<LocationData>() {
            @Override
            public void onSuccess(LocationData locationData) {
                // 显示位置信息
                String locationInfo = String.format(
                        "当前位置: 纬度=%.6f, 经度=%.6f, 地址=%s",
                        locationData.getLatitude(),
                        locationData.getLongitude(),
                        locationData.getAddress() != null ? locationData.getAddress() : "未知"
                );

                Log.d(TAG, locationInfo);
                Toast.makeText(context, locationInfo, Toast.LENGTH_LONG).show();

                // 上报位置
                reportLocation(context, locationData);
            }

            @Override
            public void onError(ApiError error) {
                String errorMsg = "获取位置失败: " + error.getMessage();
                Log.e(TAG, errorMsg);
                Toast.makeText(context, errorMsg, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 上报位置
     * @param context 上下文
     * @param locationData 位置数据
     */
    private static void reportLocation(Context context, LocationData locationData) {
        // 获取LocationRepository
        LocationRepository locationRepository = ApiRepositoryProvider.getInstance(context).getLocationRepository();

        // 上报位置
        locationRepository.reportLocation(locationData).observeForever(result -> {
            if (result.isLoading()) {
                return;
            }

            if (result.isSuccess()) {
                BaseResponse response = result.getData();
                if (response.isSuccess()) {
                    String successMsg = "位置上报成功";
                    Log.d(TAG, successMsg);
                    Toast.makeText(context, successMsg, Toast.LENGTH_SHORT).show();
                } else {
                    String errorMsg = "位置上报失败: " + response.getMessage();
                    Log.e(TAG, errorMsg);
                    Toast.makeText(context, errorMsg, Toast.LENGTH_SHORT).show();
                }
            } else {
                Throwable error = result.getError();
                String errorMsg = "位置上报错误: " + (error != null ? error.getMessage() : "Unknown error");
                Log.e(TAG, errorMsg);
                Toast.makeText(context, errorMsg, Toast.LENGTH_SHORT).show();
            }

            // 移除观察者，避免内存泄漏
            locationRepository.reportLocation(locationData).removeObserver(result1 -> {});
        });
    }

    /**
     * 启动位置服务
     * @param context 上下文
     */
    public static void startLocationService(Context context) {
        LocationService.startService(context);
        String msg = "位置服务已启动";
        Log.d(TAG, msg);
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }

    /**
     * 停止位置服务
     * @param context 上下文
     */
    public static void stopLocationService(Context context) {
        LocationService.stopService(context);
        String msg = "位置服务已停止";
        Log.d(TAG, msg);
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }

    /**
     * 开始位置更新
     * @param context 上下文
     * @param listener 位置监听器
     */
    public static void startLocationUpdates(Context context, LocationManager.LocationChangeListener listener) {
        // 获取位置管理器
        LocationManager locationManager = LocationManager.getInstance(context);

        // 添加位置监听器
        locationManager.addLocationListener(listener);

        // 开始位置更新
        locationManager.startLocationUpdates();

        String msg = "位置更新已开始";
        Log.d(TAG, msg);
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }

    /**
     * 停止位置更新
     * @param context 上下文
     * @param listener 位置监听器
     */
    public static void stopLocationUpdates(Context context, LocationManager.LocationChangeListener listener) {
        // 获取位置管理器
        LocationManager locationManager = LocationManager.getInstance(context);

        // 移除位置监听器
        locationManager.removeLocationListener(listener);

        // 停止位置更新
        locationManager.stopLocationUpdates();

        String msg = "位置更新已停止";
        Log.d(TAG, msg);
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }
}
