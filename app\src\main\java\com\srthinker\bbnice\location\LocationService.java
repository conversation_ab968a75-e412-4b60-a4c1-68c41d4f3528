package com.srthinker.bbnice.location;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.repository.LocationRepository;
import com.srthinker.bbnice.api.repository.RepositoryProvider;

import java.util.concurrent.TimeUnit;

/**
 * 位置服务类
 * 用于在后台定期获取和上报位置
 */
public class LocationService extends Service implements LocationManager.LocationChangeListener {
    private static final String TAG = "LocationService";

    private static volatile boolean started = false;

    // 立即上报位置的广播Action
    public static final String ACTION_REPORT_NOW = "com.srthinker.bbnice.location.ACTION_REPORT_NOW";

    // 通知ID
    private static final int NOTIFICATION_ID = 1001;

    // 通知渠道ID
    private static final String CHANNEL_ID = "location_service_channel";

    // 广播接收器，用于接收立即上报的请求
    private BroadcastReceiver reportNowReceiver;

    // 位置管理器
    private LocationManager locationManager;

    // 位置Repository
    private LocationRepository locationRepository;

    // 最后一次位置
    private LocationData lastLocation;

    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;

    // 当前重试次数
    private int retryCount = 0;

    // 上报处理器
    private Handler reportHandler;

    // 是否正在上报
    private boolean isReporting;

    // 是否已启动
    private boolean isStarted;

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化位置管理器
        locationManager = LocationManager.getInstance(this);

        // 初始化位置Repository
        locationRepository = RepositoryProvider.getInstance(this).getLocationRepository();

        // 初始化上报处理器
        reportHandler = new Handler(Looper.getMainLooper());

        // 初始化并注册广播接收器
        reportNowReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ACTION_REPORT_NOW.equals(intent.getAction())) {
                    Log.d(TAG, "Received immediate report request");
                    // 立即上报位置
                    reportLocations();
                }
            }
        };

        // 注册广播接收器
        IntentFilter filter = new IntentFilter(ACTION_REPORT_NOW);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // 在Android 13及以上版本使用新的API，指定接收器不接收外部广播
            registerReceiver(reportNowReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            // 在较低版本使用旧API
            registerReceiver(reportNowReceiver, filter);
        }

        Log.d(TAG, "Location service created");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (!isStarted) {
            // 创建通知
            createNotificationChannel();
            Notification notification = createNotification();

            // 启动前台服务
            startForeground(NOTIFICATION_ID, notification);

            // 添加位置监听器
            locationManager.addLocationListener(this);

            // 开始位置更新
            locationManager.startLocationUpdates();

            isStarted = true;

            Log.d(TAG, "Location service started");
        }

        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        // 移除位置监听器
        locationManager.removeLocationListener(this);

        // 停止位置更新
        locationManager.stopLocationUpdates();

        // 注销广播接收器
        try {
            unregisterReceiver(reportNowReceiver);
        } catch (Exception e) {
            Log.e(TAG, "Error unregistering receiver", e);
        }

        isStarted = false;

        Log.d(TAG, "Location service destroyed");

        super.onDestroy();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onLocationChanged(LocationData locationData) {
        // 保存最后一次位置
        lastLocation = locationData;

        Log.d(TAG, "Location changed: " + locationData);
    }

    /**
     * 上报位置
     */
    private synchronized void reportLocations() {
        // 如果没有位置或正在上报，直接返回
        if (lastLocation == null || isReporting) {
            Log.d(TAG, "No location to report or already reporting");
            return;
        }

        // 标记为正在上报
        isReporting = true;

        Log.d(TAG, "Reporting last location: " + lastLocation);

        // 上报位置
        locationRepository.reportLocation(lastLocation).observeForever(result -> {
            if (result.isLoading()) {
                return;
            }

            if (result.isSuccess()) {
                Log.d(TAG, "Location reported successfully");

                // 重置重试次数
                retryCount = 0;

                // 标记为不在上报
                isReporting = false;
            } else {
                // 处理错误
                Throwable error = result.getError();
                Log.e(TAG, "Error reporting location: " + (error != null ? error.getMessage() : "Unknown error"));

                // 增加重试次数
                retryCount++;

                // 如果重试次数小于最大重试次数，安排重试
                if (retryCount <= MAX_RETRY_COUNT) {
                    // 计算重试延迟，使用指数退避策略
                    long retryDelay = TimeUnit.SECONDS.toMillis(10) * (1L << (retryCount - 1));

                    Log.d(TAG, "Scheduling retry in " + retryDelay + " ms, retry count: " + retryCount);

                    // 安排重试
                    reportHandler.postDelayed(() -> {
                        // 重试上报
                        reportLocations();
                    }, retryDelay);
                } else {
                    // 重置重试次数
                    retryCount = 0;

                    Log.e(TAG, "Max retry count reached, giving up");
                }

                // 标记为不在上报
                isReporting = false;
            }

            // 移除观察者，避免内存泄漏
            locationRepository.reportLocation(lastLocation).removeObserver(result1 -> {});
        });
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Location Service Channel",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Used for the location service");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * 创建通知
     * @return 通知
     */
    private Notification createNotification() {
        // 创建通知意图
        Intent notificationIntent = new Intent(); // 替换为主Activity的Intent
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this,
                0,
                notificationIntent,
                PendingIntent.FLAG_IMMUTABLE
        );

        // 创建通知
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("位置服务")
                .setContentText("正在获取和上报位置")
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_LOW);

        return builder.build();
    }

    /**
     * 启动位置服务
     * @param context 上下文
     */
    public static void startService(Context context) {
        if (started) return;
        started = true;
        Intent intent = new Intent(context, LocationService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

    /**
     * 停止位置服务
     * @param context 上下文
     */
    public static void stopService(Context context) {
        if (!started) return;
        started = false;
        Intent intent = new Intent(context, LocationService.class);
        context.stopService(intent);
    }
}
