[{"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_mobile_network.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_mobile_network.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_chat.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_chat.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_device_registration_new.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_device_registration_new.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_device_registration.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_device_registration.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\learn_fragment_page.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\learn_fragment_page.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_display.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_display.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\item_wake_up_duration.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\item_wake_up_duration.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_private.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_private.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\fragment_media_list.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\fragment_media_list.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\dialog_wifi_password.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\dialog_wifi_password.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_splash.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_splash.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\item_message_sent.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\item_message_sent.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\fragment_video_detail.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\fragment_video_detail.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_gallery.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_gallery.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_voice.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_voice.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_location.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_location.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_bluetooth.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_bluetooth.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\item_message_received.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\item_message_received.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_setting.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_setting.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_language_settings.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_language_settings.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\fragment_photo_detail.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\fragment_photo_detail.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\setting_list_item.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\setting_list_item.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\call_fragment_page.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\call_fragment_page.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\item_wifi.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\item_wifi.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_wifi.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_wifi.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_call.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_call.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\item_media.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\item_media.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_learn.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_learn.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\dialog_bluetooth_pin.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\dialog_bluetooth_pin.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_about.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_about.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_repository_test.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_repository_test.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_home.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_home.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\dialog_loading.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\dialog_loading.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_config.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_config.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_chat_cnactivity.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_chat_cnactivity.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_capturectivity.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_capturectivity.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_recognition.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_recognition.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\home_list_item.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\home_list_item.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\item_bluetooth.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\item_bluetooth.xml"}, {"merged": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-3:\\layout\\activity_main.xml", "source": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-main-6:\\layout\\activity_main.xml"}]