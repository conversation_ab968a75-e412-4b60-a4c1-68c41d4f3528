[{"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_chat.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_chat.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_recognition.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_recognition.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_chat_cnactivity.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_chat_cnactivity.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_device_registration.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_device_registration.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_language_settings.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_language_settings.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/dialog_wifi_password.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/dialog_wifi_password.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_splash.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_splash.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_voice.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_voice.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_location.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_location.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/dialog_loading.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/dialog_loading.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/home_list_item.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/home_list_item.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/item_wake_up_duration.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/item_wake_up_duration.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/dialog_bluetooth_pin.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/dialog_bluetooth_pin.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/fragment_photo_detail.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/fragment_photo_detail.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/item_message_received.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/item_message_received.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_setting.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_setting.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/item_bluetooth.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/item_bluetooth.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/item_media.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/item_media.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_main.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_main.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/call_fragment_page.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/call_fragment_page.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_private.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_private.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_device_registration_new.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_device_registration_new.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_display.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_display.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_learn.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_learn.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_bluetooth.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_bluetooth.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_capturectivity.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_capturectivity.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_home.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_home.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/fragment_video_detail.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/fragment_video_detail.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_call.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_call.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_gallery.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_gallery.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_about.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_about.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/learn_fragment_page.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/learn_fragment_page.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/item_wifi.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/item_wifi.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_wifi.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_wifi.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_video_chat.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_video_chat.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_mobile_network.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_mobile_network.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_repository_test.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_repository_test.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/fragment_media_list.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/fragment_media_list.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/setting_list_item.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/setting_list_item.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/activity_config.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/activity_config.xml"}, {"merged": "com.srthinker.bbnice.app-mergeDebugResources-3:/layout/item_message_sent.xml", "source": "com.srthinker.bbnice.app-main-6:/layout/item_message_sent.xml"}]