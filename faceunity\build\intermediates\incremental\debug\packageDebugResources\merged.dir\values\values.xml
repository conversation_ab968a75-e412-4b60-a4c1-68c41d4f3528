<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="bg_switch_button">#BBBBBB</color>
    <color name="black_dialog_text">#31373E</color>
    <color name="blue">#FF0000FF</color>
    <color name="btn_live_photo_text">#2C2E30</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="colorWhite">#FFFFFFFF</color>
    <color name="divider_line_color">#33FFFFFF</color>
    <color name="divider_line_color2">#FFE5E5E5</color>
    <color name="dsb_disabled_color">#66939393</color>
    <color name="dsb_progress_color">#ff009688</color>
    <color name="dsb_ripple_color_focused">#99999999</color>
    <color name="dsb_ripple_color_pressed">#77939393</color>
    <color name="dsb_track_color">#ff939393</color>
    <color name="green">#FF00FF00</color>
    <color name="line_302D33">#FF302D33</color>
    <color name="main_classification_gradient_end">#FF7755FC</color>
    <color name="main_classification_gradient_start">#FFF661FF</color>
    <color name="main_color">#1FB2FF</color>
    <color name="main_color_090017">#FF090017</color>
    <color name="main_color_79CDF9">#FF79CDF9</color>
    <color name="main_color_c5c5c5">#FFC5C5C5</color>
    <color name="main_color_gray">#FFA8A8A8</color>
    <color name="percent50translucentBlack">#80000000</color>
    <color name="percent60translucentBlack">#99000000</color>
    <color name="primary_background">#050F14</color>
    <color name="primary_border_background">#8A797A7B</color>
    <color name="primary_border_background_old">#5C040B0E</color>
    <color name="primary_disable_state">#C6C6C6</color>
    <color name="primary_list">#BD050F14</color>
    <color name="primary_selected">#1FB2FF</color>
    <color name="primary_selected_old">#5EC7FE</color>
    <color name="primary_text">#2C2E30</color>
    <color name="transparent">#00000000</color>
    <dimen name="text_size_20">10sp</dimen>
    <dimen name="text_size_22">11sp</dimen>
    <dimen name="text_size_24">12sp</dimen>
    <dimen name="text_size_26">13sp</dimen>
    <dimen name="text_size_28">14sp</dimen>
    <dimen name="text_size_30">15sp</dimen>
    <dimen name="text_size_32">16sp</dimen>
    <dimen name="text_size_34">17sp</dimen>
    <dimen name="text_size_36">18sp</dimen>
    <string name="bailiang_1">Bright 1</string>
    <string name="beautify_head_slim">Head shrink</string>
    <string name="beautify_hip_slim">Hip</string>
    <string name="beautify_leg_thin_slim">Thin leg</string>
    <string name="beautify_shoulder">Shoulder</string>
    <string name="beauty_box_cheek_narrow">CheekNarrow</string>
    <string name="beauty_box_cheek_short">Cheek short</string>
    <string name="beauty_box_cheek_small">Cheek small</string>
    <string name="beauty_box_cheek_thinning">Cheek thin</string>
    <string name="beauty_box_cheek_v">V face</string>
    <string name="beauty_box_cheekbones">Cheekbone</string>
    <string name="beauty_box_color_level">Whiten</string>
    <string name="beauty_box_eye_bright">Eye brighten</string>
    <string name="beauty_box_eye_circle">Eye round</string>
    <string name="beauty_box_eye_enlarge">Eye enlarge</string>
    <string name="beauty_box_heavy_blur_fine">Fine smooth</string>
    <string name="beauty_box_intensity_chin">Chin</string>
    <string name="beauty_box_intensity_forehead">Forehead</string>
    <string name="beauty_box_intensity_mouth">Mouth</string>
    <string name="beauty_box_intensity_nose">Nose</string>
    <string name="beauty_box_lower_jaw">Jawbone</string>
    <string name="beauty_box_red_level">Ruddy</string>
    <string name="beauty_box_sharpen">Sharpen</string>
    <string name="beauty_box_tooth_whiten">Tooth whiten</string>
    <string name="beauty_brow_height">Brow height</string>
    <string name="beauty_brow_space">Brow space</string>
    <string name="beauty_brow_thick">Brow thick</string>
    <string name="beauty_eye_height">Eye height</string>
    <string name="beauty_eye_lid">Eye lid</string>
    <string name="beauty_face_three">Face three dimensional</string>
    <string name="beauty_lip_thick">Lip thick</string>
    <string name="beauty_micro_canthus">Canthus</string>
    <string name="beauty_micro_eye_rotate">Slant</string>
    <string name="beauty_micro_eye_space">Eye distance</string>
    <string name="beauty_micro_long_nose">Length</string>
    <string name="beauty_micro_nasolabial">Wrinkles</string>
    <string name="beauty_micro_philtrum">Philtrum</string>
    <string name="beauty_micro_pouch">Circle</string>
    <string name="beauty_micro_smile">Smile</string>
    <string name="beauty_radio_face_shape">Reshape</string>
    <string name="beauty_radio_filter">Filter</string>
    <string name="beauty_radio_skin_beauty">Skin</string>
    <string name="beauty_radio_style">Presets</string>
    <string name="camera_dialog_back">quit</string>
    <string name="camera_dialog_message">Camera permissions are disabled or the camera is occupied by another app!</string>
    <string name="camera_dialog_open">Retry</string>
    <string name="camera_dialog_title">Warning</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Yes</string>
    <string name="dialog_confirm_delete">Are you sure to delete?</string>
    <string name="dialog_reset_avatar_model">Reset all parameters to default?</string>
    <string name="face_beauty_function_tips">%s is only supported for high-end computers</string>
    <string name="fennen_1">Pink 1</string>
    <string name="fu_base_is_tracking_text">No face tracking</string>
    <string name="home_function_name_beauty">Beautification</string>
    <string name="home_function_name_beauty_body">Body</string>
    <string name="home_function_name_makeup">Makeup</string>
    <string name="home_function_name_sticker">Sticker</string>
    <string name="lengsediao_1">Cold tone 1</string>
    <string name="live_photo_save_succeed">Save succeed</string>
    <string name="long_legs">Leg</string>
    <string name="makeup_combination_chaomo">Model</string>
    <string name="makeup_combination_charming">Charming</string>
    <string name="makeup_combination_chuju">Daisy</string>
    <string name="makeup_combination_chuqiu">Autumn</string>
    <string name="makeup_combination_gangfeng">HK style</string>
    <string name="makeup_combination_hongfeng">Maple</string>
    <string name="makeup_combination_jianling">Tender</string>
    <string name="makeup_combination_neighbor">Next-door</string>
    <string name="makeup_combination_nuandong">Warm</string>
    <string name="makeup_combination_occident">European</string>
    <string name="makeup_combination_qianzhihe">Crane</string>
    <string name="makeup_combination_renyu">Mermaid</string>
    <string name="makeup_combination_rose">Rose</string>
    <string name="makeup_combination_sexy">Sexy</string>
    <string name="makeup_combination_shaonv">Maiden</string>
    <string name="makeup_combination_sweet">Sweet</string>
    <string name="makeup_combination_yanshimao">Cat</string>
    <string name="makeup_combination_ziyun">Purple</string>
    <string name="makeup_radio_remove">Remover</string>
    <string name="origin">Origin</string>
    <string name="recover">Recover</string>
    <string name="slimming">Body</string>
    <string name="sorry_no_permission">Sorry ,your license is not supported for these functions.</string>
    <string name="thin_waist">Waist</string>
    <string name="toast_delete_failed">Delete failed</string>
    <string name="toast_delete_succeed">Delete succeed</string>
    <string name="toast_not_detect_body">No body tracking</string>
    <string name="toast_not_detect_face">No face tracking</string>
    <string name="toast_not_detect_face_or_body">No face or body tracking</string>
    <string name="toast_not_detect_gesture">No gesture tracking</string>
    <string name="zhiganhui_1">Grey 1</string>
    <string name="ziran_1">Nature 1</string>
    <style name="SwitchCompat">
        <item name="colorControlActivated">@color/main_color</item>
        <item name="colorSwitchThumbNormal">#F1F1F1</item>
        <item name="android:colorForeground">#FF2F2F2F</item>
    </style>
    <style name="Widget.DiscreteIndicatorTextAppearance" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:padding">6dp</item>
    </style>
    <style name="Widget.DiscreteSeekBar" parent="android:Widget">
        <item name="dsb_indicatorTextAppearance">@style/Widget.DiscreteIndicatorTextAppearance
        </item>
        <item name="dsb_progressColor">@color/dsb_progress_color_list</item>
        <item name="dsb_trackColor">@color/dsb_track_color_list</item>
        <item name="dsb_rippleColor">@color/dsb_ripple_color_list</item>
        <item name="dsb_indicatorColor">@color/dsb_progress_color</item>
        <item name="dsb_indicatorElevation">4dp</item>
        <item name="dsb_mirrorForRtl">true</item>
    </style>
    <declare-styleable name="CircleImageView">
        <attr format="dimension" name="civ_border_width"/>
        <attr format="color" name="civ_border_color"/>
        <attr format="boolean" name="civ_border_overlay"/>
        <attr format="color" name="civ_circle_background_color"/>
    </declare-styleable>
    <declare-styleable name="DiscreteSeekBar">
        <attr format="integer|dimension" name="dsb_min"/>
        <attr format="integer|dimension" name="dsb_max"/>
        <attr format="integer|dimension" name="dsb_value"/>
        <attr format="boolean" name="dsb_mirrorForRtl"/>
        <attr format="boolean" name="dsb_allowTrackClickToDrag"/>
        <attr format="color|reference" name="dsb_progressColor"/>
        <attr format="color|reference" name="dsb_trackColor"/>
        <attr format="reference" name="dsb_indicatorTextAppearance"/>
        <attr format="color|reference" name="dsb_indicatorColor"/>
        <attr format="dimension" name="dsb_indicatorElevation"/>
        <attr format="string|reference" name="dsb_indicatorFormatter"/>
        <attr format="color|reference" name="dsb_rippleColor"/>
        <attr format="boolean" name="dsb_indicatorPopupEnabled"/>
        <attr format="integer|dimension" name="dsb_trackHeight"/>
        <attr format="integer|dimension" name="dsb_trackBaseHeight"/>
        <attr format="integer|dimension" name="dsb_scrubberHeight"/>
        <attr format="integer|dimension" name="dsb_thumbSize"/>
        <attr format="integer|dimension" name="dsb_indicatorSeparation"/>
    </declare-styleable>
    <declare-styleable name="Theme">
        <attr format="reference" name="discreteSeekBarStyle"/>
    </declare-styleable>
</resources>