1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:5-75
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:22-72
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-77
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-74
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:5-79
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-81
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-85
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-82
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-80
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-81
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-78
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-75
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
28    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
29    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
30    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-76
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-73
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-79
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:22-76
33    <uses-permission
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-31:31
34        android:name="android.permission.NEARBY_WIFI_DEVICES"
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:9-62
35        android:usesPermissionFlags="neverForLocation" /> <!-- 蓝牙相关权限 -->
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:9-55
36    <uses-permission android:name="android.permission.BLUETOOTH" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-68
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-65
37    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-74
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-71
38    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-76
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-73
39    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
40    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:5-77
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:22-74
41    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-80
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-77
42    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:5-85
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 精确闹钟权限，用于MQTT服务 -->
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-73
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:22-70
44    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:5-79
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:22-76
45    <!-- 前台服务位置权限，Android 14及以上需要 -->
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
46-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:5-86
46-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:22-83
47    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
47-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
47-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
49        android:name="android.hardware.camera"
49-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
50        android:required="false" />
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
51    <uses-feature
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
52        android:name="android.hardware.camera.front"
52-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
53        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
54    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
55    <uses-feature
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
56        android:name="android.hardware.camera.autofocus"
56-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
57        android:required="false" />
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
58    <uses-feature
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
59        android:name="android.hardware.camera.flash"
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
60        android:required="false" />
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
61    <uses-feature
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
62        android:name="android.hardware.screen.landscape"
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
63        android:required="false" />
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
64    <uses-feature
64-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
65        android:name="android.hardware.wifi"
65-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
66        android:required="false" />
66-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
67
68    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
68-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
68-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
69
70    <permission
70-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
71        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
71-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
72        android:protectionLevel="signature" />
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
73
74    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
74-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
74-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
75
76    <application
76-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:5-154:19
77        android:name="com.srthinker.bbnice.App"
77-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-28
78        android:allowBackup="true"
78-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-35
79        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
79-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
80        android:debuggable="true"
81        android:extractNativeLibs="false"
82        android:icon="@mipmap/ic_launcher"
82-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-43
83        android:label="@string/app_name"
83-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-41
84        android:roundIcon="@mipmap/ic_launcher_round"
84-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:9-54
85        android:supportsRtl="true"
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:9-35
86        android:testOnly="true"
87        android:theme="@style/Theme.AppCompat.NoActionBar"
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-59
88        android:usesCleartextTraffic="true" >
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:9-44
89        <activity
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:9-55:40
90            android:name="com.srthinker.bbnice.chat.VideoChatActivity"
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:13-51
91            android:exported="false" />
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:13-37
92        <activity
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:9-58:40
93            android:name="com.srthinker.bbnice.setting.PrivateActivity"
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:13-52
94            android:exported="false" />
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:13-37
95        <activity
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:9-61:40
96            android:name="com.srthinker.bbnice.setting.AboutActivity"
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:13-50
97            android:exported="false" />
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:13-37
98        <activity
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:9-64:40
99            android:name="com.srthinker.bbnice.setting.VoiceActivity"
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:13-50
100            android:exported="false" />
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:13-37
101        <activity
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:9-67:40
102            android:name="com.srthinker.bbnice.setting.DisplayActivity"
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:13-52
103            android:exported="false" />
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:13-37
104        <activity
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:9-70:40
105            android:name="com.srthinker.bbnice.setting.LocationActivity"
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:13-53
106            android:exported="false" />
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:13-37
107        <activity
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:9-73:40
108            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:13-54
109            android:exported="false" />
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:13-37
110        <activity
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:9-76:40
111            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:13-58
112            android:exported="false" />
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:13-37
113        <activity
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:9-79:40
114            android:name="com.srthinker.bbnice.setting.WifiActivity"
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:13-49
115            android:exported="false" />
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:13-37
116        <activity
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:9-82:40
117            android:name="com.srthinker.bbnice.setting.SettingActivity"
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:13-52
118            android:exported="false" />
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:13-37
119        <activity
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:9-85:40
120            android:name="com.srthinker.bbnice.call.CallActivity"
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:13-46
121            android:exported="false" />
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:13-37
122        <activity
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:9-88:40
123            android:name="com.srthinker.bbnice.capture.CaptureActivity"
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:13-52
124            android:exported="false" />
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:13-37
125        <activity
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:9-91:40
126            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:13-60
127            android:exported="false" />
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:13-37
128        <activity
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:9-94:40
129            android:name="com.srthinker.bbnice.learn.LearnActivity"
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:13-48
130            android:exported="false" />
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:13-37
131        <activity
131-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:9-105:20
132            android:name="com.srthinker.bbnice.home.HomeActivity"
132-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:13-46
133            android:exported="false" >
133-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:13-37
134
135            <!-- <intent-filter> -->
136            <!-- <action android:name="android.intent.action.MAIN" /> -->
137
138
139            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
140            <!-- </intent-filter> -->
141        </activity>
142        <activity
142-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:9-108:40
143            android:name="com.srthinker.bbnice.chat.ChatActivity"
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:107:13-46
144            android:exported="false" />
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:13-37
145        <activity
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:9-118:20
146            android:name="com.srthinker.bbnice.SplashActivity"
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-43
147            android:exported="true"
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:13-36
148            android:theme="@style/Theme.AppCompat.NoActionBar" >
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:13-63
149            <intent-filter>
149-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:13-117:29
150                <action android:name="android.intent.action.MAIN" />
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:17-69
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:25-66
151
152                <category android:name="android.intent.category.LAUNCHER" />
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:17-77
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:27-74
153            </intent-filter>
154        </activity>
155        <activity
155-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:119:9-122:48
156            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:120:13-68
157            android:exported="false"
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:13-37
158            android:label="@string/register" />
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-45
159        <activity
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:9-126:57
160            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:13-61
161            android:exported="false"
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:13-37
162            android:label="@string/language_settings" /> <!-- MQTT服务 -->
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:13-54
163        <service android:name="org.eclipse.paho.android.service.MqttService" />
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:9-80
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:18-77
164        <service
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:9-130:38
165            android:name="com.srthinker.bbnice.mqtt.MqttService"
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:13-45
166            android:enabled="true" /> <!-- 位置服务 -->
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-35
167        <service
167-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:131:9-135:58
168            android:name="com.srthinker.bbnice.location.LocationService"
168-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:132:13-53
169            android:enabled="true"
169-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:133:13-35
170            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
170-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:134:13-53
171        <activity
171-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:136:9-148:20
172            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
172-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:137:13-56
173            android:exported="true"
173-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:138:13-36
174            android:label="Repository测试"
174-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:139:13-41
175            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
175-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:140:13-71
176
177            <!-- <intent-filter> -->
178            <!-- <action android:name="android.intent.action.MAIN" /> -->
179
180
181            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
182            <!-- </intent-filter> -->
183        </activity> <!-- 相册Activity -->
184        <activity
184-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:149:9-153:66
185            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
185-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:150:13-52
186            android:exported="false"
186-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:151:13-37
187            android:label="@string/gallery"
187-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:152:13-44
188            android:theme="@style/Theme.AppCompat.NoActionBar" />
188-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:153:13-63
189
190        <service
190-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
191            android:name="androidx.camera.core.impl.MetadataHolderService"
191-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
192            android:enabled="false"
192-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
193            android:exported="false" >
193-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
194            <meta-data
194-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
195                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
195-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
196                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
196-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
197        </service>
198
199        <activity
199-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
200            android:name="com.journeyapps.barcodescanner.CaptureActivity"
200-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
201            android:clearTaskOnLaunch="true"
201-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
202            android:screenOrientation="sensorLandscape"
202-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
203            android:stateNotNeeded="true"
203-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
204            android:theme="@style/zxing_CaptureTheme"
204-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
205            android:windowSoftInputMode="stateAlwaysHidden" />
205-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
206
207        <provider
207-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
208            android:name="androidx.startup.InitializationProvider"
208-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
209            android:authorities="com.srthinker.bbnice.androidx-startup"
209-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
210            android:exported="false" >
210-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
211            <meta-data
211-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
212                android:name="androidx.work.WorkManagerInitializer"
212-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
213                android:value="androidx.startup" />
213-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
214            <meta-data
214-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.emoji2.text.EmojiCompatInitializer"
215-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
216                android:value="androidx.startup" />
216-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
217            <meta-data
217-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
218                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
218-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
219                android:value="androidx.startup" />
219-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
220            <meta-data
220-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
221                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
221-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
222                android:value="androidx.startup" />
222-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
223        </provider>
224
225        <service
225-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
226            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
226-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
228            android:enabled="@bool/enable_system_alarm_service_default"
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
229            android:exported="false" />
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
230        <service
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
231            android:name="androidx.work.impl.background.systemjob.SystemJobService"
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
232            android:directBootAware="false"
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
233            android:enabled="@bool/enable_system_job_service_default"
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
234            android:exported="true"
234-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
235            android:permission="android.permission.BIND_JOB_SERVICE" />
235-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
236        <service
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
237            android:name="androidx.work.impl.foreground.SystemForegroundService"
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
239            android:enabled="@bool/enable_system_foreground_service_default"
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
240            android:exported="false" />
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
241
242        <receiver
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
243            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
244            android:directBootAware="false"
244-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
245            android:enabled="true"
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
246            android:exported="false" />
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
247        <receiver
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
248            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
250            android:enabled="false"
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
251            android:exported="false" >
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
252            <intent-filter>
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
253                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
253-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
253-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
254                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
254-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
254-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
255            </intent-filter>
256        </receiver>
257        <receiver
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
258            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
259            android:directBootAware="false"
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
260            android:enabled="false"
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
261            android:exported="false" >
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
262            <intent-filter>
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
263                <action android:name="android.intent.action.BATTERY_OKAY" />
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
264                <action android:name="android.intent.action.BATTERY_LOW" />
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
265            </intent-filter>
266        </receiver>
267        <receiver
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
268            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
269            android:directBootAware="false"
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
270            android:enabled="false"
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
271            android:exported="false" >
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
272            <intent-filter>
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
273                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
273-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
273-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
274                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
275            </intent-filter>
276        </receiver>
277        <receiver
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
278            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
279            android:directBootAware="false"
279-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
280            android:enabled="false"
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
281            android:exported="false" >
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
282            <intent-filter>
282-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
283                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
283-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
283-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
284            </intent-filter>
285        </receiver>
286        <receiver
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
287            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
287-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
288            android:directBootAware="false"
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
289            android:enabled="false"
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
290            android:exported="false" >
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
291            <intent-filter>
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
292                <action android:name="android.intent.action.BOOT_COMPLETED" />
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
293                <action android:name="android.intent.action.TIME_SET" />
293-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
293-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
294                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
295            </intent-filter>
296        </receiver>
297        <receiver
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
298            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
298-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
299            android:directBootAware="false"
299-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
300            android:enabled="@bool/enable_system_alarm_service_default"
300-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
301            android:exported="false" >
301-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
302            <intent-filter>
302-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
303                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
304            </intent-filter>
305        </receiver>
306        <receiver
306-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
307            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
307-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
308            android:directBootAware="false"
308-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
309            android:enabled="true"
309-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
310            android:exported="true"
310-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
311            android:permission="android.permission.DUMP" >
311-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
312            <intent-filter>
312-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
313                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
313-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
313-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
314            </intent-filter>
315        </receiver>
316
317        <service
317-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
318            android:name="androidx.room.MultiInstanceInvalidationService"
318-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
319            android:directBootAware="true"
319-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
320            android:exported="false" />
320-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
321
322        <uses-library
322-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
323            android:name="androidx.window.extensions"
323-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
324            android:required="false" />
324-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
325        <uses-library
325-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
326            android:name="androidx.window.sidecar"
326-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
327            android:required="false" />
327-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
328
329        <receiver
329-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
330            android:name="androidx.profileinstaller.ProfileInstallReceiver"
330-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
331            android:directBootAware="false"
331-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
332            android:enabled="true"
332-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
333            android:exported="true"
333-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
334            android:permission="android.permission.DUMP" >
334-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
335            <intent-filter>
335-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
336                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
337            </intent-filter>
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
339                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
340            </intent-filter>
341            <intent-filter>
341-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
342                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
343            </intent-filter>
344            <intent-filter>
344-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
345                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
345-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
345-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
346            </intent-filter>
347        </receiver>
348    </application>
349
350</manifest>
