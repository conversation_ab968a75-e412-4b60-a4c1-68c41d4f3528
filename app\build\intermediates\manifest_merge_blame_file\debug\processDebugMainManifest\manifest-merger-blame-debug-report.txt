1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:5:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:5:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-76
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-75
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-72
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:5-77
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:22-74
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-79
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:5-81
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:22-78
22    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-85
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-82
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-80
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-81
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-76
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-73
26    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-75
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-72
27
28    <!-- WiFi相关权限 -->
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:5-76
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:22-73
30    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:22-73
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-79
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-76
32
33    <!-- 蓝牙相关权限 -->
34    <uses-permission android:name="android.permission.BLUETOOTH" />
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-68
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:22-65
35    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:5-74
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:22-71
36    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:5-76
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:22-73
37    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:5-73
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:22-70
38
39    <!-- 移动网络相关权限 -->
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-79
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-76
41    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-79
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-76
42    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:5-77
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:22-74
43    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-75
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-72
44    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
44-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:12:5-80
44-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:12:22-77
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
45-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
45-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
47        android:name="android.hardware.camera"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
49    <uses-feature
49-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
50        android:name="android.hardware.camera.front"
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
51        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
52    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
54        android:name="android.hardware.camera.autofocus"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
55        android:required="false" />
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
56    <uses-feature
56-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
57        android:name="android.hardware.camera.flash"
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
58        android:required="false" />
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
60        android:name="android.hardware.screen.landscape"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
63        android:name="android.hardware.wifi"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
65
66    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
66-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
66-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
67
68    <permission
68-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-119:19
75        android:name="com.srthinker.bbnice.App"
75-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:9-28
76        android:allowBackup="true"
76-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:41:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:icon="@mipmap/ic_launcher"
80-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:9-43
81        android:label="@string/app_name"
81-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:43:9-41
82        android:roundIcon="@mipmap/ic_launcher_round"
82-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:9-54
83        android:supportsRtl="true"
83-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-35
84        android:testOnly="true"
85        android:theme="@style/Theme.AppCompat.NoActionBar"
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-59
86        android:usesCleartextTraffic="true" >
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-44
87        <activity
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-50:40
88            android:name="com.srthinker.bbnice.setting.LocationActivity"
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:13-53
89            android:exported="false" />
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:13-37
90        <activity
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-53:40
91            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:13-54
92            android:exported="false" />
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:13-37
93        <activity
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:9-56:40
94            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:13-58
95            android:exported="false" />
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-37
96        <activity
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:9-59:40
97            android:name="com.srthinker.bbnice.setting.WifiActivity"
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:13-49
98            android:exported="false" />
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-37
99        <activity
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:9-62:40
100            android:name="com.srthinker.bbnice.setting.SettingActivity"
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:13-52
101            android:exported="false" />
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-37
102        <activity
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:9-65:40
103            android:name="com.srthinker.bbnice.call.CallActivity"
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:13-46
104            android:exported="false" />
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-37
105        <activity
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:9-68:40
106            android:name="com.srthinker.bbnice.capture.CaptureActivity"
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:13-52
107            android:exported="false" />
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-37
108        <activity
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:9-71:40
109            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:13-60
110            android:exported="false" />
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-37
111        <activity
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:9-74:40
112            android:name="com.srthinker.bbnice.learn.LearnActivity"
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:13-48
113            android:exported="false" />
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-37
114        <activity
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:9-83:20
115            android:name="com.srthinker.bbnice.home.HomeActivity"
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:13-46
116            android:exported="true" >
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-36
117            <intent-filter>
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:13-82:29
118                <action android:name="android.intent.action.MAIN" />
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:17-69
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:25-66
119
120                <category android:name="android.intent.category.LAUNCHER" />
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:17-77
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:27-74
121            </intent-filter>
122        </activity>
123        <activity
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:9-86:40
124            android:name="com.srthinker.bbnice.chat.ChatActivity"
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:13-46
125            android:exported="false" />
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-37
126        <activity
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:9-90:48
127            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:13-68
128            android:exported="false"
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:13-37
129            android:label="@string/register" /> <!-- 语言设置Activity -->
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:13-45
130        <activity
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:9-94:57
131            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
131-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:13-61
132            android:exported="false"
132-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:13-37
133            android:label="@string/language_settings" /> <!-- MQTT服务 -->
133-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:13-54
134        <service android:name="org.eclipse.paho.android.service.MqttService" />
134-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:9-80
134-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:18-77
135        <service
135-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:9-98:38
136            android:name="com.srthinker.bbnice.mqtt.MqttService"
136-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:13-45
137            android:enabled="true" /> <!-- 位置服务 -->
137-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:13-35
138        <service
138-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:99:9-102:56
139            android:name="com.srthinker.bbnice.location.LocationService"
139-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:100:13-53
140            android:enabled="true"
140-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:101:13-35
141            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
141-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:102:13-53
142        <activity
142-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:103:9-113:20
143            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:13-56
144            android:exported="true"
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:105:13-36
145            android:label="Repository测试"
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:13-41
146            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:107:13-71
147            <intent-filter>
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:13-112:29
148                <action android:name="android.intent.action.VIEW" />
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:17-69
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:25-66
149
150                <category android:name="android.intent.category.DEFAULT" />
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:17-76
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:27-73
151            </intent-filter>
152        </activity> <!-- 相册Activity -->
153        <activity
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:9-118:66
154            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:115:13-52
155            android:exported="false"
155-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:13-37
156            android:label="@string/gallery"
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:13-44
157            android:theme="@style/Theme.AppCompat.NoActionBar" />
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:13-63
158
159        <service
159-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
160            android:name="androidx.camera.core.impl.MetadataHolderService"
160-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
161            android:enabled="false"
161-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
162            android:exported="false" >
162-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
163            <meta-data
163-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
164                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
164-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
165                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
165-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
166        </service>
167
168        <activity
168-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
169            android:name="com.journeyapps.barcodescanner.CaptureActivity"
169-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
170            android:clearTaskOnLaunch="true"
170-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
171            android:screenOrientation="sensorLandscape"
171-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
172            android:stateNotNeeded="true"
172-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
173            android:theme="@style/zxing_CaptureTheme"
173-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
174            android:windowSoftInputMode="stateAlwaysHidden" />
174-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
175
176        <provider
176-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
177            android:name="androidx.startup.InitializationProvider"
177-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
178            android:authorities="com.srthinker.bbnice.androidx-startup"
178-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
179            android:exported="false" >
179-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
180            <meta-data
180-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
181                android:name="androidx.work.WorkManagerInitializer"
181-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
182                android:value="androidx.startup" />
182-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
183            <meta-data
183-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
184                android:name="androidx.emoji2.text.EmojiCompatInitializer"
184-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
185                android:value="androidx.startup" />
185-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
186            <meta-data
186-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
187-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
188                android:value="androidx.startup" />
188-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
189            <meta-data
189-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
190-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
191                android:value="androidx.startup" />
191-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
192        </provider>
193
194        <service
194-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
195            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
195-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
196            android:directBootAware="false"
196-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
197            android:enabled="@bool/enable_system_alarm_service_default"
197-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
198            android:exported="false" />
198-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
199        <service
199-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
200            android:name="androidx.work.impl.background.systemjob.SystemJobService"
200-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
202            android:enabled="@bool/enable_system_job_service_default"
202-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
203            android:exported="true"
203-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
204            android:permission="android.permission.BIND_JOB_SERVICE" />
204-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
205        <service
205-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
206            android:name="androidx.work.impl.foreground.SystemForegroundService"
206-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
208            android:enabled="@bool/enable_system_foreground_service_default"
208-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
209            android:exported="false" />
209-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
210
211        <receiver
211-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
212            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
212-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
213            android:directBootAware="false"
213-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
214            android:enabled="true"
214-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
215            android:exported="false" />
215-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
216        <receiver
216-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
217            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
217-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
218            android:directBootAware="false"
218-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
219            android:enabled="false"
219-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
220            android:exported="false" >
220-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
221            <intent-filter>
221-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
222                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
222-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
222-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
223                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
224            </intent-filter>
225        </receiver>
226        <receiver
226-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
227            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
227-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
228            android:directBootAware="false"
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
229            android:enabled="false"
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
230            android:exported="false" >
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
231            <intent-filter>
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
232                <action android:name="android.intent.action.BATTERY_OKAY" />
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
233                <action android:name="android.intent.action.BATTERY_LOW" />
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
234            </intent-filter>
235        </receiver>
236        <receiver
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
237            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
239            android:enabled="false"
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
240            android:exported="false" >
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
241            <intent-filter>
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
242                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
243                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
244            </intent-filter>
245        </receiver>
246        <receiver
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
247            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
249            android:enabled="false"
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
250            android:exported="false" >
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
251            <intent-filter>
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
252                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
253            </intent-filter>
254        </receiver>
255        <receiver
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
256            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
257            android:directBootAware="false"
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
258            android:enabled="false"
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
259            android:exported="false" >
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
260            <intent-filter>
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
261                <action android:name="android.intent.action.BOOT_COMPLETED" />
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
262                <action android:name="android.intent.action.TIME_SET" />
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
263                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
264            </intent-filter>
265        </receiver>
266        <receiver
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
267            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
268            android:directBootAware="false"
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
269            android:enabled="@bool/enable_system_alarm_service_default"
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
270            android:exported="false" >
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
271            <intent-filter>
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
272                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
273            </intent-filter>
274        </receiver>
275        <receiver
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
276            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
278            android:enabled="true"
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
279            android:exported="true"
279-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
280            android:permission="android.permission.DUMP" >
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
281            <intent-filter>
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
282                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
282-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
282-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
283            </intent-filter>
284        </receiver>
285
286        <service
286-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
287            android:name="androidx.room.MultiInstanceInvalidationService"
287-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
288            android:directBootAware="true"
288-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
289            android:exported="false" />
289-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
290
291        <uses-library
291-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
292            android:name="androidx.window.extensions"
292-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
293            android:required="false" />
293-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
294        <uses-library
294-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
295            android:name="androidx.window.sidecar"
295-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
296            android:required="false" />
296-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
297
298        <receiver
298-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
299            android:name="androidx.profileinstaller.ProfileInstallReceiver"
299-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
300            android:directBootAware="false"
300-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
301            android:enabled="true"
301-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
302            android:exported="true"
302-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
303            android:permission="android.permission.DUMP" >
303-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
304            <intent-filter>
304-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
305                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
305-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
305-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
306            </intent-filter>
307            <intent-filter>
307-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
308                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
308-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
308-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
309            </intent-filter>
310            <intent-filter>
310-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
311                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
311-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
311-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
312            </intent-filter>
313            <intent-filter>
313-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
314                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
314-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
314-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
315            </intent-filter>
316        </receiver>
317    </application>
318
319</manifest>
