1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:5-75
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:13:22-72
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-77
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-74
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:5-79
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-81
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-78
22    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-85
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-82
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-80
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-77
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-81
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-78
25    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-75
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-72
27    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
28    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-76
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-73
29    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-75
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-72
30    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-76
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-73
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-79
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:22-76
33    <uses-permission
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-31:31
34        android:name="android.permission.NEARBY_WIFI_DEVICES"
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:9-62
35        android:usesPermissionFlags="neverForLocation" /> <!-- 蓝牙相关权限 -->
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:9-55
36    <uses-permission android:name="android.permission.BLUETOOTH" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-68
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-65
37    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-74
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-71
38    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-76
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-73
39    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
40    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:5-77
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:22-74
41    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-80
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-77
42    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:5-85
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 精确闹钟权限，用于MQTT服务 -->
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-73
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:22-70
44    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:5-79
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:22-76
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
45-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
45-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\1e7756b843b9167ce74c6b7cd4c3af1a\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
47        android:name="android.hardware.camera"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
49    <uses-feature
49-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
50        android:name="android.hardware.camera.front"
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
51        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
52    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
54        android:name="android.hardware.camera.autofocus"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
55        android:required="false" />
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
56    <uses-feature
56-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
57        android:name="android.hardware.camera.flash"
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
58        android:required="false" />
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
60        android:name="android.hardware.screen.landscape"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
63        android:name="android.hardware.wifi"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
65
66    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
66-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
66-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
67
68    <permission
68-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:5-152:19
75        android:name="com.srthinker.bbnice.App"
75-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:43:9-28
76        android:allowBackup="true"
76-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\60755c64c7a6aca0bf5a1e7604eb1619\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:icon="@mipmap/ic_launcher"
80-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-43
81        android:label="@string/app_name"
81-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-41
82        android:roundIcon="@mipmap/ic_launcher_round"
82-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-54
83        android:supportsRtl="true"
83-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-35
84        android:testOnly="true"
85        android:theme="@style/Theme.AppCompat.NoActionBar"
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:9-59
86        android:usesCleartextTraffic="true" >
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:9-44
87        <activity
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-53:40
88            android:name="com.srthinker.bbnice.chat.VideoChatActivity"
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:13-51
89            android:exported="false" />
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:13-37
90        <activity
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:9-56:40
91            android:name="com.srthinker.bbnice.setting.PrivateActivity"
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:13-52
92            android:exported="false" />
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-37
93        <activity
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:9-59:40
94            android:name="com.srthinker.bbnice.setting.AboutActivity"
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:13-50
95            android:exported="false" />
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-37
96        <activity
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:9-62:40
97            android:name="com.srthinker.bbnice.setting.VoiceActivity"
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:13-50
98            android:exported="false" />
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-37
99        <activity
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:9-65:40
100            android:name="com.srthinker.bbnice.setting.DisplayActivity"
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:13-52
101            android:exported="false" />
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-37
102        <activity
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:9-68:40
103            android:name="com.srthinker.bbnice.setting.LocationActivity"
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:13-53
104            android:exported="false" />
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-37
105        <activity
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:9-71:40
106            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:13-54
107            android:exported="false" />
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-37
108        <activity
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:9-74:40
109            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:13-58
110            android:exported="false" />
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-37
111        <activity
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:9-77:40
112            android:name="com.srthinker.bbnice.setting.WifiActivity"
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:13-49
113            android:exported="false" />
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-37
114        <activity
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:9-80:40
115            android:name="com.srthinker.bbnice.setting.SettingActivity"
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:13-52
116            android:exported="false" />
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:13-37
117        <activity
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:9-83:40
118            android:name="com.srthinker.bbnice.call.CallActivity"
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:13-46
119            android:exported="false" />
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:13-37
120        <activity
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:9-86:40
121            android:name="com.srthinker.bbnice.capture.CaptureActivity"
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:13-52
122            android:exported="false" />
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-37
123        <activity
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:9-89:40
124            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:13-60
125            android:exported="false" />
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:13-37
126        <activity
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:9-92:40
127            android:name="com.srthinker.bbnice.learn.LearnActivity"
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:13-48
128            android:exported="false" />
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:13-37
129        <activity
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:9-103:20
130            android:name="com.srthinker.bbnice.home.HomeActivity"
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:13-46
131            android:exported="false" >
131-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:13-37
132
133            <!-- <intent-filter> -->
134            <!-- <action android:name="android.intent.action.MAIN" /> -->
135
136
137            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
138            <!-- </intent-filter> -->
139        </activity>
140        <activity
140-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:9-106:40
141            android:name="com.srthinker.bbnice.chat.ChatActivity"
141-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:105:13-46
142            android:exported="false" />
142-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:13-37
143        <activity
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:107:9-116:20
144            android:name="com.srthinker.bbnice.SplashActivity"
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:13-43
145            android:exported="true"
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:13-36
146            android:theme="@style/Theme.AppCompat.NoActionBar" >
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-63
147            <intent-filter>
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:13-115:29
148                <action android:name="android.intent.action.MAIN" />
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:17-69
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:25-66
149
150                <category android:name="android.intent.category.LAUNCHER" />
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:17-77
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:27-74
151            </intent-filter>
152        </activity>
153        <activity
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:9-120:48
154            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:13-68
155            android:exported="false"
155-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:119:13-37
156            android:label="@string/register" />
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:120:13-45
157        <activity
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:9-124:57
158            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-61
159            android:exported="false"
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:13-37
160            android:label="@string/language_settings" /> <!-- MQTT服务 -->
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:13-54
161        <service android:name="org.eclipse.paho.android.service.MqttService" />
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:9-80
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:18-77
162        <service
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:9-128:38
163            android:name="com.srthinker.bbnice.mqtt.MqttService"
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:13-45
164            android:enabled="true" /> <!-- 位置服务 -->
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:13-35
165        <service
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:9-133:58
166            android:name="com.srthinker.bbnice.location.LocationService"
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-53
167            android:enabled="true"
167-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:131:13-35
168            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
168-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:132:13-53
169        <activity
169-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:134:9-146:20
170            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
170-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:135:13-56
171            android:exported="true"
171-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:136:13-36
172            android:label="Repository测试"
172-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:137:13-41
173            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
173-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:138:13-71
174
175            <!-- <intent-filter> -->
176            <!-- <action android:name="android.intent.action.MAIN" /> -->
177
178
179            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
180            <!-- </intent-filter> -->
181        </activity> <!-- 相册Activity -->
182        <activity
182-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:147:9-151:66
183            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
183-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:148:13-52
184            android:exported="false"
184-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:149:13-37
185            android:label="@string/gallery"
185-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:150:13-44
186            android:theme="@style/Theme.AppCompat.NoActionBar" />
186-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:151:13-63
187
188        <service
188-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
189            android:name="androidx.camera.core.impl.MetadataHolderService"
189-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
190            android:enabled="false"
190-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
191            android:exported="false" >
191-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
192            <meta-data
192-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
193                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
193-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
194                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
194-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ea800ae20d5c61773a3ade34eb7ba049\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
195        </service>
196
197        <activity
197-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
198            android:name="com.journeyapps.barcodescanner.CaptureActivity"
198-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
199            android:clearTaskOnLaunch="true"
199-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
200            android:screenOrientation="sensorLandscape"
200-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
201            android:stateNotNeeded="true"
201-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
202            android:theme="@style/zxing_CaptureTheme"
202-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
203            android:windowSoftInputMode="stateAlwaysHidden" />
203-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3897927b4e640fb040be54a2126e5ad6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
204
205        <provider
205-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
206            android:name="androidx.startup.InitializationProvider"
206-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
207            android:authorities="com.srthinker.bbnice.androidx-startup"
207-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
208            android:exported="false" >
208-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
209            <meta-data
209-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
210                android:name="androidx.work.WorkManagerInitializer"
210-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
211                android:value="androidx.startup" />
211-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
212            <meta-data
212-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.emoji2.text.EmojiCompatInitializer"
213-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
214                android:value="androidx.startup" />
214-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\d4c6ef3c9ad2344ce6f875061260c9ae\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
215            <meta-data
215-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
216-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
217                android:value="androidx.startup" />
217-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\66f3cc8775d4fcc2d9fb5136f8ec0d48\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
218            <meta-data
218-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
219-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
220                android:value="androidx.startup" />
220-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
221        </provider>
222
223        <service
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
224            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
224-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
225            android:directBootAware="false"
225-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
226            android:enabled="@bool/enable_system_alarm_service_default"
226-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
227            android:exported="false" />
227-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
228        <service
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
229            android:name="androidx.work.impl.background.systemjob.SystemJobService"
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
231            android:enabled="@bool/enable_system_job_service_default"
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
232            android:exported="true"
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
233            android:permission="android.permission.BIND_JOB_SERVICE" />
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
234        <service
234-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
235            android:name="androidx.work.impl.foreground.SystemForegroundService"
235-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
237            android:enabled="@bool/enable_system_foreground_service_default"
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
238            android:exported="false" />
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
239
240        <receiver
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
241            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
242            android:directBootAware="false"
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
243            android:enabled="true"
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
244            android:exported="false" />
244-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
245        <receiver
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
246            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
247            android:directBootAware="false"
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
248            android:enabled="false"
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
249            android:exported="false" >
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
250            <intent-filter>
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
251                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
252                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
253            </intent-filter>
254        </receiver>
255        <receiver
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
256            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
257            android:directBootAware="false"
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
258            android:enabled="false"
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
259            android:exported="false" >
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
260            <intent-filter>
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
261                <action android:name="android.intent.action.BATTERY_OKAY" />
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
262                <action android:name="android.intent.action.BATTERY_LOW" />
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
263            </intent-filter>
264        </receiver>
265        <receiver
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
266            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
267            android:directBootAware="false"
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
268            android:enabled="false"
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
269            android:exported="false" >
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
270            <intent-filter>
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
271                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
272                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
273            </intent-filter>
274        </receiver>
275        <receiver
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
276            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
278            android:enabled="false"
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
279            android:exported="false" >
279-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
280            <intent-filter>
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
281                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
282            </intent-filter>
283        </receiver>
284        <receiver
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
285            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
286            android:directBootAware="false"
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
287            android:enabled="false"
287-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
288            android:exported="false" >
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
289            <intent-filter>
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
290                <action android:name="android.intent.action.BOOT_COMPLETED" />
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
291                <action android:name="android.intent.action.TIME_SET" />
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
292                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
293            </intent-filter>
294        </receiver>
295        <receiver
295-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
296            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
296-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
297            android:directBootAware="false"
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
298            android:enabled="@bool/enable_system_alarm_service_default"
298-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
299            android:exported="false" >
299-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
300            <intent-filter>
300-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
301                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
301-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
301-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
302            </intent-filter>
303        </receiver>
304        <receiver
304-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
305            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
305-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
306            android:directBootAware="false"
306-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
307            android:enabled="true"
307-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
308            android:exported="true"
308-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
309            android:permission="android.permission.DUMP" >
309-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
310            <intent-filter>
310-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
311                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
311-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
311-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\3fe6b1a96e0473b11cddd82eef6049a9\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
312            </intent-filter>
313        </receiver>
314
315        <service
315-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
316            android:name="androidx.room.MultiInstanceInvalidationService"
316-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
317            android:directBootAware="true"
317-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
318            android:exported="false" />
318-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\4e5a177e635bb522e7b4ecb1f3a1b30c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
319
320        <uses-library
320-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
321            android:name="androidx.window.extensions"
321-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
322            android:required="false" />
322-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
323        <uses-library
323-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
324            android:name="androidx.window.sidecar"
324-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
325            android:required="false" />
325-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\9126021dcbc2a68bc0b811f26e4e5104\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
326
327        <receiver
327-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
328            android:name="androidx.profileinstaller.ProfileInstallReceiver"
328-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
329            android:directBootAware="false"
329-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
330            android:enabled="true"
330-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
331            android:exported="true"
331-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
332            android:permission="android.permission.DUMP" >
332-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
333            <intent-filter>
333-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
334                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
334-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
334-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
335            </intent-filter>
336            <intent-filter>
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
337                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
337-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
337-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
338            </intent-filter>
339            <intent-filter>
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
340                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
340-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
340-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
341            </intent-filter>
342            <intent-filter>
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
343                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
343-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
343-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\8ccf4dbe4732a6f12b72bf240e50775a\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
344            </intent-filter>
345        </receiver>
346    </application>
347
348</manifest>
