1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
19    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
20    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-77
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-74
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-79
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-76
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-81
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-85
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-82
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-80
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-77
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-81
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-78
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-76
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:5-75
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:5-76
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:22-73
30    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-79
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-76
32    <uses-permission
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-30:31
33        android:name="android.permission.NEARBY_WIFI_DEVICES"
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:9-62
34        android:usesPermissionFlags="neverForLocation" /> <!-- 蓝牙相关权限 -->
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:9-55
35    <uses-permission android:name="android.permission.BLUETOOTH" />
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:5-68
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:22-65
36    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-74
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-71
37    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-76
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-73
38    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-73
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-70
39    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-77
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-74
40    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:5-80
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:36:22-77
41    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-85
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-82
42    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 精确闹钟权限，用于MQTT服务 -->
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:5-73
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:22-70
43    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-79
43-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:22-76
44    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
44-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
44-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
45    <uses-feature
45-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
46        android:name="android.hardware.camera"
46-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
47        android:required="false" />
47-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
48    <uses-feature
48-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
49        android:name="android.hardware.camera.front"
49-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
50        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
51    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
52    <uses-feature
52-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
53        android:name="android.hardware.camera.autofocus"
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
54        android:required="false" />
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
55    <uses-feature
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
56        android:name="android.hardware.camera.flash"
56-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
57        android:required="false" />
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
58    <uses-feature
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
59        android:name="android.hardware.screen.landscape"
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
60        android:required="false" />
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
61    <uses-feature
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
62        android:name="android.hardware.wifi"
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
63        android:required="false" />
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
64
65    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
65-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
65-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
66
67    <permission
67-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
68        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
72
73    <application
73-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:41:5-151:19
74        android:name="com.srthinker.bbnice.App"
74-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:9-28
75        android:allowBackup="true"
75-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:43:9-35
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
77        android:debuggable="true"
78        android:extractNativeLibs="false"
79        android:icon="@mipmap/ic_launcher"
79-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:9-43
80        android:label="@string/app_name"
80-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-41
81        android:roundIcon="@mipmap/ic_launcher_round"
81-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-54
82        android:supportsRtl="true"
82-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-35
83        android:testOnly="true"
84        android:theme="@style/Theme.AppCompat.NoActionBar"
84-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-59
85        android:usesCleartextTraffic="true" >
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:9-44
86        <activity
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:9-52:40
87            android:name="com.srthinker.bbnice.chat.VideoChatActivity"
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:13-51
88            android:exported="false" />
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:13-37
89        <activity
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:9-55:40
90            android:name="com.srthinker.bbnice.setting.PrivateActivity"
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:13-52
91            android:exported="false" />
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:13-37
92        <activity
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:9-58:40
93            android:name="com.srthinker.bbnice.setting.AboutActivity"
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:13-50
94            android:exported="false" />
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:13-37
95        <activity
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:9-61:40
96            android:name="com.srthinker.bbnice.setting.VoiceActivity"
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:13-50
97            android:exported="false" />
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:13-37
98        <activity
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:9-64:40
99            android:name="com.srthinker.bbnice.setting.DisplayActivity"
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:13-52
100            android:exported="false" />
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:13-37
101        <activity
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:9-67:40
102            android:name="com.srthinker.bbnice.setting.LocationActivity"
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:13-53
103            android:exported="false" />
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:13-37
104        <activity
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:9-70:40
105            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:13-54
106            android:exported="false" />
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:13-37
107        <activity
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:9-73:40
108            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:13-58
109            android:exported="false" />
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:13-37
110        <activity
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:9-76:40
111            android:name="com.srthinker.bbnice.setting.WifiActivity"
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:13-49
112            android:exported="false" />
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:13-37
113        <activity
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:9-79:40
114            android:name="com.srthinker.bbnice.setting.SettingActivity"
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:13-52
115            android:exported="false" />
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:13-37
116        <activity
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:9-82:40
117            android:name="com.srthinker.bbnice.call.CallActivity"
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:13-46
118            android:exported="false" />
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:13-37
119        <activity
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:9-85:40
120            android:name="com.srthinker.bbnice.capture.CaptureActivity"
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:13-52
121            android:exported="false" />
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:13-37
122        <activity
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:9-88:40
123            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:13-60
124            android:exported="false" />
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:13-37
125        <activity
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:9-91:40
126            android:name="com.srthinker.bbnice.learn.LearnActivity"
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:13-48
127            android:exported="false" />
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:13-37
128        <activity
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:9-102:20
129            android:name="com.srthinker.bbnice.home.HomeActivity"
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:13-46
130            android:exported="false" >
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:13-37
131
132            <!-- <intent-filter> -->
133            <!-- <action android:name="android.intent.action.MAIN" /> -->
134
135
136            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
137            <!-- </intent-filter> -->
138        </activity>
139        <activity
139-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:103:9-105:40
140            android:name="com.srthinker.bbnice.chat.ChatActivity"
140-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:13-46
141            android:exported="false" />
141-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:105:13-37
142        <activity
142-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:9-115:20
143            android:name="com.srthinker.bbnice.SplashActivity"
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:107:13-43
144            android:exported="true"
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:13-36
145            android:theme="@style/Theme.AppCompat.NoActionBar" >
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:13-63
146            <intent-filter>
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-114:29
147                <action android:name="android.intent.action.MAIN" />
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:17-69
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:25-66
148
149                <category android:name="android.intent.category.LAUNCHER" />
149-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:17-77
149-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:27-74
150            </intent-filter>
151        </activity>
152        <activity
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:9-119:48
153            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:13-68
154            android:exported="false"
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:13-37
155            android:label="@string/register" />
155-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:119:13-45
156        <activity
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:120:9-123:57
157            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:13-61
158            android:exported="false"
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-37
159            android:label="@string/language_settings" /> <!-- MQTT服务 -->
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:13-54
160        <service android:name="org.eclipse.paho.android.service.MqttService" />
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:9-80
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:18-77
161        <service
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:125:9-127:38
162            android:name="com.srthinker.bbnice.mqtt.MqttService"
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:13-45
163            android:enabled="true" /> <!-- 位置服务 -->
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:13-35
164        <service
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:9-132:58
165            android:name="com.srthinker.bbnice.location.LocationService"
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:13-53
166            android:enabled="true"
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-35
167            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
167-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:131:13-53
168        <activity
168-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:133:9-145:20
169            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
169-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:134:13-56
170            android:exported="true"
170-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:135:13-36
171            android:label="Repository测试"
171-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:136:13-41
172            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
172-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:137:13-71
173
174            <!-- <intent-filter> -->
175            <!-- <action android:name="android.intent.action.MAIN" /> -->
176
177
178            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
179            <!-- </intent-filter> -->
180        </activity> <!-- 相册Activity -->
181        <activity
181-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:146:9-150:66
182            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
182-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:147:13-52
183            android:exported="false"
183-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:148:13-37
184            android:label="@string/gallery"
184-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:149:13-44
185            android:theme="@style/Theme.AppCompat.NoActionBar" />
185-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:150:13-63
186
187        <service
187-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
188            android:name="androidx.camera.core.impl.MetadataHolderService"
188-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
189            android:enabled="false"
189-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
190            android:exported="false" >
190-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
191            <meta-data
191-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
192                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
192-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
193                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
193-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
194        </service>
195
196        <activity
196-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
197            android:name="com.journeyapps.barcodescanner.CaptureActivity"
197-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
198            android:clearTaskOnLaunch="true"
198-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
199            android:screenOrientation="sensorLandscape"
199-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
200            android:stateNotNeeded="true"
200-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
201            android:theme="@style/zxing_CaptureTheme"
201-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
202            android:windowSoftInputMode="stateAlwaysHidden" />
202-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
203
204        <provider
204-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
205            android:name="androidx.startup.InitializationProvider"
205-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
206            android:authorities="com.srthinker.bbnice.androidx-startup"
206-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
207            android:exported="false" >
207-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
208            <meta-data
208-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
209                android:name="androidx.work.WorkManagerInitializer"
209-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
210                android:value="androidx.startup" />
210-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
211            <meta-data
211-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.emoji2.text.EmojiCompatInitializer"
212-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
213                android:value="androidx.startup" />
213-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
214            <meta-data
214-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
215-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
216                android:value="androidx.startup" />
216-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
217            <meta-data
217-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
218                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
218-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
219                android:value="androidx.startup" />
219-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
220        </provider>
221
222        <service
222-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
223            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
225            android:enabled="@bool/enable_system_alarm_service_default"
225-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
226            android:exported="false" />
226-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
227        <service
227-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
228            android:name="androidx.work.impl.background.systemjob.SystemJobService"
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
229            android:directBootAware="false"
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
230            android:enabled="@bool/enable_system_job_service_default"
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
231            android:exported="true"
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
232            android:permission="android.permission.BIND_JOB_SERVICE" />
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
233        <service
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
234            android:name="androidx.work.impl.foreground.SystemForegroundService"
234-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
236            android:enabled="@bool/enable_system_foreground_service_default"
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
237            android:exported="false" />
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
238
239        <receiver
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
240            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
242            android:enabled="true"
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
243            android:exported="false" />
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
244        <receiver
244-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
245            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
246            android:directBootAware="false"
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
247            android:enabled="false"
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
248            android:exported="false" >
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
249            <intent-filter>
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
250                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
251                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
252            </intent-filter>
253        </receiver>
254        <receiver
254-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
255            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
256            android:directBootAware="false"
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
257            android:enabled="false"
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
258            android:exported="false" >
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
259            <intent-filter>
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
260                <action android:name="android.intent.action.BATTERY_OKAY" />
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
261                <action android:name="android.intent.action.BATTERY_LOW" />
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
262            </intent-filter>
263        </receiver>
264        <receiver
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
265            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
267            android:enabled="false"
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
268            android:exported="false" >
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
269            <intent-filter>
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
270                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
271                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
272            </intent-filter>
273        </receiver>
274        <receiver
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
275            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
276            android:directBootAware="false"
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
277            android:enabled="false"
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
278            android:exported="false" >
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
279            <intent-filter>
279-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
280                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
281            </intent-filter>
282        </receiver>
283        <receiver
283-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
284            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
285            android:directBootAware="false"
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
286            android:enabled="false"
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
287            android:exported="false" >
287-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
288            <intent-filter>
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
289                <action android:name="android.intent.action.BOOT_COMPLETED" />
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
290                <action android:name="android.intent.action.TIME_SET" />
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
291                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
292            </intent-filter>
293        </receiver>
294        <receiver
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
295            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
295-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
296            android:directBootAware="false"
296-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
297            android:enabled="@bool/enable_system_alarm_service_default"
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
298            android:exported="false" >
298-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
299            <intent-filter>
299-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
300                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
300-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
300-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
301            </intent-filter>
302        </receiver>
303        <receiver
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
304            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
304-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
305            android:directBootAware="false"
305-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
306            android:enabled="true"
306-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
307            android:exported="true"
307-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
308            android:permission="android.permission.DUMP" >
308-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
309            <intent-filter>
309-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
310                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
310-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
310-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
311            </intent-filter>
312        </receiver>
313
314        <service
314-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
315            android:name="androidx.room.MultiInstanceInvalidationService"
315-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
316            android:directBootAware="true"
316-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
317            android:exported="false" />
317-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
318
319        <uses-library
319-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
320            android:name="androidx.window.extensions"
320-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
321            android:required="false" />
321-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
322        <uses-library
322-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
323            android:name="androidx.window.sidecar"
323-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
324            android:required="false" />
324-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
325
326        <receiver
326-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
327            android:name="androidx.profileinstaller.ProfileInstallReceiver"
327-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
328            android:directBootAware="false"
328-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
329            android:enabled="true"
329-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
330            android:exported="true"
330-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
331            android:permission="android.permission.DUMP" >
331-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
332            <intent-filter>
332-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
333                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
333-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
333-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
334            </intent-filter>
335            <intent-filter>
335-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
336                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
337            </intent-filter>
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
339                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
340            </intent-filter>
341            <intent-filter>
341-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
342                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
342-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
343            </intent-filter>
344        </receiver>
345    </application>
346
347</manifest>
