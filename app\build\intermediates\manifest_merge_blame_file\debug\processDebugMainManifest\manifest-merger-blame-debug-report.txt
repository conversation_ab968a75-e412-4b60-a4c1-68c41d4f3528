1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
19    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
20    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-77
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-74
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-79
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-76
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-81
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-85
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-82
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-80
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-77
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-81
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-78
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-76
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:5-75
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:5-76
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:22-73
30    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-79
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 蓝牙相关权限 -->
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-79
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.BLUETOOTH" />
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-68
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:22-65
34    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:5-74
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:22-71
35    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:5-76
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:22-73
36    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:5-73
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:22-70
37    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-77
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-74
38    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-80
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-77
39    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-85
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-82
40    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
41    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
41-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
41-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
42    <uses-feature
42-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
43        android:name="android.hardware.camera"
43-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
44        android:required="false" />
44-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
45    <uses-feature
45-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
46        android:name="android.hardware.camera.front"
46-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
47        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
47-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
48    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
49    <uses-feature
49-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
50        android:name="android.hardware.camera.autofocus"
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
51        android:required="false" />
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
52    <uses-feature
52-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
53        android:name="android.hardware.camera.flash"
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
54        android:required="false" />
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
55    <uses-feature
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
56        android:name="android.hardware.screen.landscape"
56-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
57        android:required="false" />
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
58    <uses-feature
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
59        android:name="android.hardware.wifi"
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
60        android:required="false" />
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
61
62    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
62-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
62-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
63
64    <permission
64-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
65        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
65-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
66        android:protectionLevel="signature" />
66-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
67
68    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
68-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
68-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
69
70    <application
70-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-132:19
71        android:name="com.srthinker.bbnice.App"
71-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:38:9-28
72        android:allowBackup="true"
72-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:9-35
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
74        android:debuggable="true"
75        android:extractNativeLibs="false"
76        android:icon="@mipmap/ic_launcher"
76-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:9-43
77        android:label="@string/app_name"
77-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:41:9-41
78        android:roundIcon="@mipmap/ic_launcher_round"
78-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:9-54
79        android:supportsRtl="true"
79-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:43:9-35
80        android:testOnly="true"
81        android:theme="@style/Theme.AppCompat.NoActionBar"
81-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:9-59
82        android:usesCleartextTraffic="true" >
82-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-44
83        <activity
83-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-48:40
84            android:name="com.srthinker.bbnice.setting.PrivateActivity"
84-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:13-52
85            android:exported="false" />
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:13-37
86        <activity
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:9-51:40
87            android:name="com.srthinker.bbnice.setting.AboutActivity"
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:13-50
88            android:exported="false" />
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:13-37
89        <activity
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:9-54:40
90            android:name="com.srthinker.bbnice.setting.VoiceActivity"
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:13-50
91            android:exported="false" />
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:13-37
92        <activity
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:9-57:40
93            android:name="com.srthinker.bbnice.setting.DisplayActivity"
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-52
94            android:exported="false" />
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:13-37
95        <activity
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:9-60:40
96            android:name="com.srthinker.bbnice.setting.LocationActivity"
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-53
97            android:exported="false" />
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:13-37
98        <activity
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:9-63:40
99            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-54
100            android:exported="false" />
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:13-37
101        <activity
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:9-66:40
102            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-58
103            android:exported="false" />
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:13-37
104        <activity
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:9-69:40
105            android:name="com.srthinker.bbnice.setting.WifiActivity"
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-49
106            android:exported="false" />
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:13-37
107        <activity
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:9-72:40
108            android:name="com.srthinker.bbnice.setting.SettingActivity"
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-52
109            android:exported="false" />
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:13-37
110        <activity
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:9-75:40
111            android:name="com.srthinker.bbnice.call.CallActivity"
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-46
112            android:exported="false" />
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:13-37
113        <activity
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:9-78:40
114            android:name="com.srthinker.bbnice.capture.CaptureActivity"
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-52
115            android:exported="false" />
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:13-37
116        <activity
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:9-81:40
117            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:13-60
118            android:exported="false" />
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:13-37
119        <activity
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:9-84:40
120            android:name="com.srthinker.bbnice.learn.LearnActivity"
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:13-48
121            android:exported="false" />
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:13-37
122        <activity
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:9-89:20
123            android:name="com.srthinker.bbnice.home.HomeActivity"
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-46
124            android:exported="true" >
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:13-36
125        </activity>
126        <activity
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:90:9-92:40
127            android:name="com.srthinker.bbnice.chat.ChatActivity"
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:91:13-46
128            android:exported="false" />
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:92:13-37
129        <activity
129-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:93:9-102:20
130            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
130-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:94:13-68
131            android:exported="true"
131-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:95:13-36
132            android:label="@string/register" > <!-- 语言设置Activity -->
132-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:13-45
133            <intent-filter>
133-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:13-101:29
134                <action android:name="android.intent.action.MAIN" />
134-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:17-69
134-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:25-66
135
136                <category android:name="android.intent.category.LAUNCHER" />
136-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:100:17-77
136-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:100:27-74
137            </intent-filter>
138        </activity>
139        <activity
139-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:103:9-106:57
140            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
140-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:13-61
141            android:exported="false"
141-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:105:13-37
142            android:label="@string/language_settings" /> <!-- MQTT服务 -->
142-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:13-54
143        <service android:name="org.eclipse.paho.android.service.MqttService" />
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:107:9-80
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:107:18-77
144        <service
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:108:9-110:38
145            android:name="com.srthinker.bbnice.mqtt.MqttService"
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:13-45
146            android:enabled="true" /> <!-- 位置服务 -->
146-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-35
147        <service
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:9-115:58
148            android:name="com.srthinker.bbnice.location.LocationService"
148-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:13-53
149            android:enabled="true"
149-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:13-35
150            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:13-53
151        <activity
151-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:9-126:20
152            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:13-56
153            android:exported="true"
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:13-36
154            android:label="Repository测试"
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:119:13-41
155            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
155-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:120:13-71
156            <intent-filter>
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:13-125:29
157                <action android:name="android.intent.action.VIEW" />
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:17-69
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:25-66
158
159                <category android:name="android.intent.category.DEFAULT" />
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:17-76
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:27-73
160            </intent-filter>
161        </activity> <!-- 相册Activity -->
162        <activity
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:9-131:66
163            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:13-52
164            android:exported="false"
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:13-37
165            android:label="@string/gallery"
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-44
166            android:theme="@style/Theme.AppCompat.NoActionBar" />
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:131:13-63
167
168        <service
168-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
169            android:name="androidx.camera.core.impl.MetadataHolderService"
169-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
170            android:enabled="false"
170-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
171            android:exported="false" >
171-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
172            <meta-data
172-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
173                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
173-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
174                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
174-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
175        </service>
176
177        <activity
177-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
178            android:name="com.journeyapps.barcodescanner.CaptureActivity"
178-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
179            android:clearTaskOnLaunch="true"
179-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
180            android:screenOrientation="sensorLandscape"
180-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
181            android:stateNotNeeded="true"
181-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
182            android:theme="@style/zxing_CaptureTheme"
182-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
183            android:windowSoftInputMode="stateAlwaysHidden" />
183-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
184
185        <provider
185-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
186            android:name="androidx.startup.InitializationProvider"
186-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
187            android:authorities="com.srthinker.bbnice.androidx-startup"
187-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
188            android:exported="false" >
188-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
189            <meta-data
189-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
190                android:name="androidx.work.WorkManagerInitializer"
190-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
191                android:value="androidx.startup" />
191-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
192            <meta-data
192-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
193                android:name="androidx.emoji2.text.EmojiCompatInitializer"
193-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
194                android:value="androidx.startup" />
194-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
195            <meta-data
195-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
196                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
196-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
197                android:value="androidx.startup" />
197-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
198            <meta-data
198-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
199                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
199-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
200                android:value="androidx.startup" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
201        </provider>
202
203        <service
203-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
204            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
204-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
205            android:directBootAware="false"
205-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
206            android:enabled="@bool/enable_system_alarm_service_default"
206-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
207            android:exported="false" />
207-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
208        <service
208-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
209            android:name="androidx.work.impl.background.systemjob.SystemJobService"
209-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
211            android:enabled="@bool/enable_system_job_service_default"
211-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
212            android:exported="true"
212-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
213            android:permission="android.permission.BIND_JOB_SERVICE" />
213-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
214        <service
214-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
215            android:name="androidx.work.impl.foreground.SystemForegroundService"
215-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
217            android:enabled="@bool/enable_system_foreground_service_default"
217-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
218            android:exported="false" />
218-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
219
220        <receiver
220-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
221            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
221-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
222            android:directBootAware="false"
222-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
223            android:enabled="true"
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
224            android:exported="false" />
224-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
225        <receiver
225-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
226            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
226-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
228            android:enabled="false"
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
229            android:exported="false" >
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
230            <intent-filter>
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
231                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
232                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
233            </intent-filter>
234        </receiver>
235        <receiver
235-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
236            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
236-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
237            android:directBootAware="false"
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
238            android:enabled="false"
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
239            android:exported="false" >
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
240            <intent-filter>
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
241                <action android:name="android.intent.action.BATTERY_OKAY" />
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
242                <action android:name="android.intent.action.BATTERY_LOW" />
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
243            </intent-filter>
244        </receiver>
245        <receiver
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
246            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
247            android:directBootAware="false"
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
248            android:enabled="false"
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
249            android:exported="false" >
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
250            <intent-filter>
250-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
251                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
251-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
252                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
253            </intent-filter>
254        </receiver>
255        <receiver
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
256            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
257            android:directBootAware="false"
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
258            android:enabled="false"
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
259            android:exported="false" >
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
260            <intent-filter>
260-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
261                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
261-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
262            </intent-filter>
263        </receiver>
264        <receiver
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
265            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
267            android:enabled="false"
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
268            android:exported="false" >
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
269            <intent-filter>
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
270                <action android:name="android.intent.action.BOOT_COMPLETED" />
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
270-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
271                <action android:name="android.intent.action.TIME_SET" />
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
271-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
272                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
273            </intent-filter>
274        </receiver>
275        <receiver
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
276            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
278            android:enabled="@bool/enable_system_alarm_service_default"
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
279            android:exported="false" >
279-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
280            <intent-filter>
280-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
281                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
282            </intent-filter>
283        </receiver>
284        <receiver
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
285            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
286            android:directBootAware="false"
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
287            android:enabled="true"
287-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
288            android:exported="true"
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
289            android:permission="android.permission.DUMP" >
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
290            <intent-filter>
290-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
291                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
291-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
292            </intent-filter>
293        </receiver>
294
295        <service
295-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
296            android:name="androidx.room.MultiInstanceInvalidationService"
296-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
297            android:directBootAware="true"
297-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
298            android:exported="false" />
298-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
299
300        <uses-library
300-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
301            android:name="androidx.window.extensions"
301-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
302            android:required="false" />
302-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
303        <uses-library
303-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
304            android:name="androidx.window.sidecar"
304-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
305            android:required="false" />
305-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
306
307        <receiver
307-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
308            android:name="androidx.profileinstaller.ProfileInstallReceiver"
308-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
309            android:directBootAware="false"
309-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
310            android:enabled="true"
310-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
311            android:exported="true"
311-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
312            android:permission="android.permission.DUMP" >
312-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
313            <intent-filter>
313-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
314                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
314-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
314-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
315            </intent-filter>
316            <intent-filter>
316-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
317                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
317-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
317-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
318            </intent-filter>
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
320                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
320-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
320-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
321            </intent-filter>
322            <intent-filter>
322-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
323                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
323-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
323-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
324            </intent-filter>
325        </receiver>
326    </application>
327
328</manifest>
