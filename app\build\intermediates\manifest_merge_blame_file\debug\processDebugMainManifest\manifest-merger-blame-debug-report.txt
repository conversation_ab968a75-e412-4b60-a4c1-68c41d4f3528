1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srthinker.bbnice"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:5-86
11-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:6:22-83
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:5-71
12-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:7:22-68
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:5-67
13-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.CAMERA" />
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:5-65
14-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:9:22-62
15    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:5-69
15-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:10:22-66
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:5-68
16-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:11:22-65
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 移动网络相关权限 -->
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
17-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:5-79
18-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:12:22-76
19    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
19-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
20    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:5-75
20-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:5-77
21-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:16:22-74
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:5-79
22-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:17:22-76
23    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:5-81
23-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:5-85
24-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:19:22-82
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:5-80
25-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:20:22-77
26    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:5-81
26-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:21:22-78
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:5-76
27-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:22:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- WiFi相关权限 -->
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:5-75
28-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:5-76
29-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:24:22-73
30    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:5-76
30-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:25:22-73
31    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:5-79
31-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:26:22-76
32    <uses-permission
32-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:5-145
33        android:name="android.permission.NEARBY_WIFI_DEVICES"
33-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:22-75
34        android:usesPermissionFlags="neverForLocation" /> <!-- 蓝牙相关权限 -->
34-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:27:76-122
35    <uses-permission android:name="android.permission.BLUETOOTH" />
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:5-68
35-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:28:22-65
36    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:5-74
36-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:29:22-71
37    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:5-76
37-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:30:22-73
38    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:5-73
38-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:31:22-70
39    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" /> <!-- 音频控制权限 -->
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:5-77
39-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:32:22-74
40    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:5-80
40-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:33:22-77
41    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" /> <!-- 屏幕亮度控制权限 -->
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:5-85
41-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:34:22-82
42    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:5-73
42-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:35:22-70
43    <!-- 精确闹钟权限，用于MQTT服务 -->
44    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:5-79
44-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:37:22-76
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
45-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:5-94
45-->[com.volcengine:VolcEngineRTC:3.58.1.19400] D:\Workspace\env\.gradle\caches\8.11.1\transforms\43b511c225fd918ddc21d5f58f4933c6\transformed\jetified-VolcEngineRTC-3.58.1.19400\AndroidManifest.xml:20:22-91
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
47        android:name="android.hardware.camera"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:26:9-47
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:27:9-33
49    <uses-feature
49-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
50        android:name="android.hardware.camera.front"
50-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
51        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
51-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
52    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
54        android:name="android.hardware.camera.autofocus"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:34:9-57
55        android:required="false" />
55-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:35:9-33
56    <uses-feature
56-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
57        android:name="android.hardware.camera.flash"
57-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
58        android:required="false" />
58-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
60        android:name="android.hardware.screen.landscape"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
62    <uses-feature
62-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
63        android:name="android.hardware.wifi"
63-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
64        android:required="false" />
64-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
65
66    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
66-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
66-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
67
68    <permission
68-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.srthinker.bbnice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:39:5-142:19
75        android:name="com.srthinker.bbnice.App"
75-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:40:9-28
76        android:allowBackup="true"
76-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:41:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.9.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\e33f3c5e78c7b440b2bd748a3baa2e42\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:icon="@mipmap/ic_launcher"
80-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:42:9-43
81        android:label="@string/app_name"
81-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:43:9-41
82        android:roundIcon="@mipmap/ic_launcher_round"
82-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:44:9-54
83        android:supportsRtl="true"
83-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:45:9-35
84        android:testOnly="true"
85        android:theme="@style/Theme.AppCompat.NoActionBar"
85-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:46:9-59
86        android:usesCleartextTraffic="true" >
86-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:47:9-44
87        <activity
87-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:48:9-50:40
88            android:name="com.srthinker.bbnice.setting.PrivateActivity"
88-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:49:13-52
89            android:exported="false" />
89-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:50:13-37
90        <activity
90-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:51:9-53:40
91            android:name="com.srthinker.bbnice.setting.AboutActivity"
91-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:52:13-50
92            android:exported="false" />
92-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:53:13-37
93        <activity
93-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:54:9-56:40
94            android:name="com.srthinker.bbnice.setting.VoiceActivity"
94-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:55:13-50
95            android:exported="false" />
95-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:56:13-37
96        <activity
96-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:57:9-59:40
97            android:name="com.srthinker.bbnice.setting.DisplayActivity"
97-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:58:13-52
98            android:exported="false" />
98-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:59:13-37
99        <activity
99-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:60:9-62:40
100            android:name="com.srthinker.bbnice.setting.LocationActivity"
100-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:61:13-53
101            android:exported="false" />
101-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:62:13-37
102        <activity
102-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:63:9-65:40
103            android:name="com.srthinker.bbnice.setting.BluetoothActivity"
103-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:64:13-54
104            android:exported="false" />
104-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:65:13-37
105        <activity
105-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:66:9-68:40
106            android:name="com.srthinker.bbnice.setting.MobileNetworkActivity"
106-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:67:13-58
107            android:exported="false" />
107-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:68:13-37
108        <activity
108-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:69:9-71:40
109            android:name="com.srthinker.bbnice.setting.WifiActivity"
109-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:70:13-49
110            android:exported="false" />
110-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:71:13-37
111        <activity
111-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:72:9-74:40
112            android:name="com.srthinker.bbnice.setting.SettingActivity"
112-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:73:13-52
113            android:exported="false" />
113-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:74:13-37
114        <activity
114-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:75:9-77:40
115            android:name="com.srthinker.bbnice.call.CallActivity"
115-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:76:13-46
116            android:exported="false" />
116-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:77:13-37
117        <activity
117-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:78:9-80:40
118            android:name="com.srthinker.bbnice.capture.CaptureActivity"
118-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:79:13-52
119            android:exported="false" />
119-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:80:13-37
120        <activity
120-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:81:9-83:40
121            android:name="com.srthinker.bbnice.recognition.RecognitionActivity"
121-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:82:13-60
122            android:exported="false" />
122-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:83:13-37
123        <activity
123-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:84:9-86:40
124            android:name="com.srthinker.bbnice.learn.LearnActivity"
124-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:85:13-48
125            android:exported="false" />
125-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:86:13-37
126        <activity
126-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:87:9-95:20
127            android:name="com.srthinker.bbnice.home.HomeActivity"
127-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:88:13-46
128            android:exported="false" >
128-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:89:13-37
129
130            <!-- <intent-filter> -->
131            <!-- <action android:name="android.intent.action.MAIN" /> -->
132
133
134            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
135            <!-- </intent-filter> -->
136        </activity>
137        <activity
137-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:96:9-98:40
138            android:name="com.srthinker.bbnice.chat.ChatActivity"
138-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:97:13-46
139            android:exported="false" />
139-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:98:13-37
140        <activity
140-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:99:9-108:20
141            android:name="com.srthinker.bbnice.SplashActivity"
141-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:100:13-43
142            android:exported="true"
142-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:101:13-36
143            android:theme="@style/Theme.AppCompat.NoActionBar" >
143-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:102:13-63
144            <intent-filter>
144-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:103:13-107:29
145                <action android:name="android.intent.action.MAIN" />
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:17-69
145-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:104:25-66
146
147                <category android:name="android.intent.category.LAUNCHER" />
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:17-77
147-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:106:27-74
148            </intent-filter>
149        </activity>
150        <activity
150-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:109:9-112:48
151            android:name="com.srthinker.bbnice.registration.DeviceRegistrationActivity"
151-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:110:13-68
152            android:exported="false"
152-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:111:13-37
153            android:label="@string/register" />
153-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:112:13-45
154        <activity
154-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:113:9-116:57
155            android:name="com.srthinker.bbnice.setting.LanguageSettingsActivity"
155-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:114:13-61
156            android:exported="false"
156-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:115:13-37
157            android:label="@string/language_settings" /> <!-- MQTT服务 -->
157-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:116:13-54
158        <service android:name="org.eclipse.paho.android.service.MqttService" />
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:9-80
158-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:117:18-77
159        <service
159-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:118:9-120:38
160            android:name="com.srthinker.bbnice.mqtt.MqttService"
160-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:119:13-45
161            android:enabled="true" /> <!-- 位置服务 -->
161-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:120:13-35
162        <service
162-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:121:9-125:58
163            android:name="com.srthinker.bbnice.location.LocationService"
163-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:122:13-53
164            android:enabled="true"
164-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:123:13-35
165            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
165-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:124:13-53
166        <activity
166-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:126:9-136:20
167            android:name="com.srthinker.bbnice.test.RepositoryTestActivity"
167-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:127:13-56
168            android:exported="true"
168-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:128:13-36
169            android:label="Repository测试"
169-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:129:13-41
170            android:theme="@style/Theme.AppCompat.Light.DarkActionBar" >
170-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:130:13-71
171
172            <!-- <intent-filter> -->
173            <!-- <action android:name="android.intent.action.MAIN" /> -->
174
175
176            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
177            <!-- </intent-filter> -->
178        </activity> <!-- 相册Activity -->
179        <activity
179-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:137:9-141:66
180            android:name="com.srthinker.bbnice.gallery.GalleryActivity"
180-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:138:13-52
181            android:exported="false"
181-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:139:13-37
182            android:label="@string/gallery"
182-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:140:13-44
183            android:theme="@style/Theme.AppCompat.NoActionBar" />
183-->D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\AndroidManifest.xml:141:13-63
184
185        <service
185-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
186            android:name="androidx.camera.core.impl.MetadataHolderService"
186-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
187            android:enabled="false"
187-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
188            android:exported="false" >
188-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
189            <meta-data
189-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
190                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
190-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
191                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
191-->[androidx.camera:camera-camera2:1.2.3] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ee78b997e60325588b25d440574c81bb\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
192        </service>
193
194        <activity
194-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
195            android:name="com.journeyapps.barcodescanner.CaptureActivity"
195-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
196            android:clearTaskOnLaunch="true"
196-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
197            android:screenOrientation="sensorLandscape"
197-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
198            android:stateNotNeeded="true"
198-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
199            android:theme="@style/zxing_CaptureTheme"
199-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
200            android:windowSoftInputMode="stateAlwaysHidden" />
200-->[com.journeyapps:zxing-android-embedded:4.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\cff9521c234b1cb1e27a4b1f3461c77d\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
201
202        <provider
202-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
203            android:name="androidx.startup.InitializationProvider"
203-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
204            android:authorities="com.srthinker.bbnice.androidx-startup"
204-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
206            <meta-data
206-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
207                android:name="androidx.work.WorkManagerInitializer"
207-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
208                android:value="androidx.startup" />
208-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
209            <meta-data
209-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.emoji2.text.EmojiCompatInitializer"
210-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
211                android:value="androidx.startup" />
211-->[androidx.emoji2:emoji2:1.2.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\ae4e1f047d3e77070bb9160dae7e621c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
212            <meta-data
212-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
213-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
214                android:value="androidx.startup" />
214-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\Workspace\env\.gradle\caches\8.11.1\transforms\af2886c0162a56e88b2e952c6667a848\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
215            <meta-data
215-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
216-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
217                android:value="androidx.startup" />
217-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
218        </provider>
219
220        <service
220-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
221            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
221-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
222            android:directBootAware="false"
222-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
223            android:enabled="@bool/enable_system_alarm_service_default"
223-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
224            android:exported="false" />
224-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
225        <service
225-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
226            android:name="androidx.work.impl.background.systemjob.SystemJobService"
226-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
228            android:enabled="@bool/enable_system_job_service_default"
228-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
229            android:exported="true"
229-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
230            android:permission="android.permission.BIND_JOB_SERVICE" />
230-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
231        <service
231-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
232            android:name="androidx.work.impl.foreground.SystemForegroundService"
232-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
233            android:directBootAware="false"
233-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
234            android:enabled="@bool/enable_system_foreground_service_default"
234-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
235            android:exported="false" />
235-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
236
237        <receiver
237-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
238            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
238-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
240            android:enabled="true"
240-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
241            android:exported="false" />
241-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
242        <receiver
242-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
243            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
243-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
244            android:directBootAware="false"
244-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
245            android:enabled="false"
245-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
246            android:exported="false" >
246-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
247            <intent-filter>
247-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
248                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
248-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
249                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
249-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
250            </intent-filter>
251        </receiver>
252        <receiver
252-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
253            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
253-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
254            android:directBootAware="false"
254-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
255            android:enabled="false"
255-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
256            android:exported="false" >
256-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
257            <intent-filter>
257-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
258                <action android:name="android.intent.action.BATTERY_OKAY" />
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
258-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
259                <action android:name="android.intent.action.BATTERY_LOW" />
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
259-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
260            </intent-filter>
261        </receiver>
262        <receiver
262-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
263            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
263-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
264            android:directBootAware="false"
264-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
265            android:enabled="false"
265-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
266            android:exported="false" >
266-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
267            <intent-filter>
267-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
268                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
268-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
269                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
269-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
270            </intent-filter>
271        </receiver>
272        <receiver
272-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
273            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
273-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
274            android:directBootAware="false"
274-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
275            android:enabled="false"
275-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
276            android:exported="false" >
276-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
277            <intent-filter>
277-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
278                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
278-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
279            </intent-filter>
280        </receiver>
281        <receiver
281-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
282            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
282-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
283            android:directBootAware="false"
283-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
284            android:enabled="false"
284-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
285            android:exported="false" >
285-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
286            <intent-filter>
286-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
287                <action android:name="android.intent.action.BOOT_COMPLETED" />
287-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
287-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
288                <action android:name="android.intent.action.TIME_SET" />
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
288-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
289                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
289-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
290            </intent-filter>
291        </receiver>
292        <receiver
292-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
293            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
293-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
294            android:directBootAware="false"
294-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
295            android:enabled="@bool/enable_system_alarm_service_default"
295-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
296            android:exported="false" >
296-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
297            <intent-filter>
297-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
298                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
298-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
298-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
299            </intent-filter>
300        </receiver>
301        <receiver
301-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
302            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
302-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
303            android:directBootAware="false"
303-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
304            android:enabled="true"
304-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
305            android:exported="true"
305-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
306            android:permission="android.permission.DUMP" >
306-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
307            <intent-filter>
307-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
308                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
308-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
308-->[androidx.work:work-runtime:2.8.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\20243f5941d08e59da533d3580b4bad6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
309            </intent-filter>
310        </receiver>
311
312        <service
312-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
313            android:name="androidx.room.MultiInstanceInvalidationService"
313-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
314            android:directBootAware="true"
314-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
315            android:exported="false" />
315-->[androidx.room:room-runtime:2.6.1] D:\Workspace\env\.gradle\caches\8.11.1\transforms\de23e0cf784895979ea98d9bb2fe4db5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
316
317        <uses-library
317-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
318            android:name="androidx.window.extensions"
318-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
319            android:required="false" />
319-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
320        <uses-library
320-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
321            android:name="androidx.window.sidecar"
321-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
322            android:required="false" />
322-->[androidx.window:window:1.0.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\5a5a99579a4757e90b452776b2bd8854\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
323
324        <receiver
324-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
325            android:name="androidx.profileinstaller.ProfileInstallReceiver"
325-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
326            android:directBootAware="false"
326-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
327            android:enabled="true"
327-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
328            android:exported="true"
328-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
329            android:permission="android.permission.DUMP" >
329-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
330            <intent-filter>
330-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
331                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
331-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
331-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
332            </intent-filter>
333            <intent-filter>
333-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
334                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
334-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
334-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
335            </intent-filter>
336            <intent-filter>
336-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
337                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
337-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
337-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
338            </intent-filter>
339            <intent-filter>
339-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
340                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
340-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
340-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\Workspace\env\.gradle\caches\8.11.1\transforms\56673d34f527a9af1a2304a7fe33c080\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
341            </intent-filter>
342        </receiver>
343    </application>
344
345</manifest>
