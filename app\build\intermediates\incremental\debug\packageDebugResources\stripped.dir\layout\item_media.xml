<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/img_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:contentDescription="@string/gallery"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@android:drawable/ic_menu_gallery" />

        <ImageView
            android:id="@+id/img_video_indicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_margin="8dp"
            android:contentDescription="@string/videos"
            android:src="@android:drawable/ic_media_play"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/img_thumbnail"
            app:layout_constraintStart_toStartOf="@id/img_thumbnail"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="#80000000"
            android:paddingStart="4dp"
            android:paddingTop="2dp"
            android:paddingEnd="4dp"
            android:paddingBottom="2dp"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/img_thumbnail"
            app:layout_constraintEnd_toEndOf="@id/img_thumbnail"
            tools:text="01:23"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:clickable="false"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
