<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.faceunity:model:8.7.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets"><file name="graphics/body_slim.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\body_slim.bundle"/><file name="graphics/controller_cpp.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\controller_cpp.bundle"/><file name="graphics/face_beautification.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\face_beautification.bundle"/><file name="graphics/face_makeup.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\face_makeup.bundle"/><file name="graphics/fuzzytoonfilter.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\fuzzytoonfilter.bundle"/><file name="graphics/fxaa.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\fxaa.bundle"/><file name="graphics/tongue.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\graphics\tongue.bundle"/><file name="model/ai_face_processor.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\model\ai_face_processor.bundle"/><file name="model/ai_hand_processor.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\model\ai_hand_processor.bundle"/><file name="model/ai_human_processor.bundle" path="D:\Workspace\env\.gradle\caches\8.11.1\transforms\6b053fd91d2c47ae782464fabb9f249a\transformed\jetified-model-8.7.0\assets\model\ai_human_processor.bundle"/></source></dataSet><dataSet config=":faceunity" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets"><file name="makeup/chaomo.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\chaomo.bundle"/><file name="makeup/chuju.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\chuju.bundle"/><file name="makeup/chuqiu.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\chuqiu.bundle"/><file name="makeup/gangfeng.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\gangfeng.bundle"/><file name="makeup/hongfeng.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\hongfeng.bundle"/><file name="makeup/jianling.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\jianling.bundle"/><file name="makeup/linjia.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\linjia.bundle"/><file name="makeup/nuandong.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\nuandong.bundle"/><file name="makeup/oumei.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\oumei.bundle"/><file name="makeup/qianzhihe.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\qianzhihe.bundle"/><file name="makeup/renyu.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\renyu.bundle"/><file name="makeup/rose.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\rose.bundle"/><file name="makeup/shaonv.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\shaonv.bundle"/><file name="makeup/tianmei.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\tianmei.bundle"/><file name="makeup/wumei.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\wumei.bundle"/><file name="makeup/xinggan.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\xinggan.bundle"/><file name="makeup/yanshimao.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\yanshimao.bundle"/><file name="makeup/ziyun.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\makeup\ziyun.bundle"/><file name="sticker/cat_sparks.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\sticker\cat_sparks.bundle"/><file name="sticker/fashi.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\sticker\fashi.bundle"/><file name="sticker/sdlr.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\sticker\sdlr.bundle"/><file name="sticker/sdlu.bundle" path="D:\Workspace\Projects\BBNice\AndroidClient\faceunity\build\intermediates\assets\debug\mergeDebugAssets\sticker\sdlu.bundle"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\assets"><file name="panda.jpg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\assets\panda.jpg"/><file name="sys.jpg" path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\main\assets\sys.jpg"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Workspace\Projects\BBNice\AndroidClient\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>