package com.srthinker.bbnice.api.repository;

import android.content.Context;

/**
 * Repository提供者
 * 用于获取各种Repository的实例
 * 这是一个临时解决方案，未来应该使用依赖注入框架
 */
public class RepositoryProvider {
    
    // 单例实例
    private static RepositoryProvider instance;
    
    // Repository实例
    private DeviceRepository deviceRepository;
    private LocationRepository locationRepository;
    private MqttRepository mqttRepository;
    
    // 认证Token
    private String authToken;
    
    /**
     * 获取单例实例
     * @param context 上下文
     * @return RepositoryProvider实例
     */
    public static synchronized RepositoryProvider getInstance(Context context) {
        if (instance == null) {
            instance = new RepositoryProvider(context);
        }
        return instance;
    }
    
    /**
     * 构造函数
     * @param context 上下文
     */
    private RepositoryProvider(Context context) {
        Context appContext = context.getApplicationContext();
        
        // 创建Repository实例
        deviceRepository = new DeviceRepositoryImpl(appContext);
        locationRepository = new LocationRepositoryImpl(appContext);
        mqttRepository = new MqttRepositoryImpl(appContext);
    }
    
    /**
     * 设置认证Token
     * @param token 认证Token
     */
    public void setAuthToken(String token) {
        this.authToken = token;
        
        // 更新所有Repository的Token
        if (deviceRepository instanceof DeviceRepositoryImpl) {
            ((DeviceRepositoryImpl) deviceRepository).setAuthToken(token);
        }
        
        if (locationRepository instanceof LocationRepositoryImpl) {
            ((LocationRepositoryImpl) locationRepository).setAuthToken(token);
        }
        
        if (mqttRepository instanceof MqttRepositoryImpl) {
            ((MqttRepositoryImpl) mqttRepository).setAuthToken(token);
        }
    }
    
    /**
     * 获取认证Token
     * @return 认证Token
     */
    public String getAuthToken() {
        return authToken;
    }
    
    /**
     * 获取DeviceRepository
     * @return DeviceRepository
     */
    public DeviceRepository getDeviceRepository() {
        return deviceRepository;
    }
    
    /**
     * 获取LocationRepository
     * @return LocationRepository
     */
    public LocationRepository getLocationRepository() {
        return locationRepository;
    }
    
    /**
     * 获取MqttRepository
     * @return MqttRepository
     */
    public MqttRepository getMqttRepository() {
        return mqttRepository;
    }
}
