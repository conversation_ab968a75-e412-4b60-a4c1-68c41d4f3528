<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_wifi_password" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\dialog_wifi_password.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_wifi_password_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="39" endOffset="14"/></Target><Target id="@+id/tv_wifi_name" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="14" endOffset="34"/></Target><Target id="@+id/et_wifi_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="25" startOffset="8" endLine="29" endOffset="46"/></Target><Target id="@+id/cb_show_password" view="CheckBox"><Expressions/><location startLine="33" startOffset="4" endLine="37" endOffset="29"/></Target></Targets></Layout>