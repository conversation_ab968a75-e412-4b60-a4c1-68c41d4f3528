<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">




    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="vertical">

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_gravity="bottom"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_join_room"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="加入房间" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_leave_room"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="退出房间" />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_start_chat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="开始对话" />

        <!-- 新增：打开配置界面按钮 -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_open_config"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="打开配置界面" />


        <androidx.appcompat.widget.AppCompatButton
            android:visibility="gone"
            android:id="@+id/btn_Change_Cam"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="切换摄像头" />

    </LinearLayout>


</androidx.appcompat.widget.LinearLayoutCompat>