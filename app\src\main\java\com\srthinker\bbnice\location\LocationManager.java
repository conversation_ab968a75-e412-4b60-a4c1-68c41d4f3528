package com.srthinker.bbnice.location;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.location.LocationListener;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.bean.LocationData;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 地理位置管理器类
 * 用于获取和管理地理位置信息
 */
public class LocationManager {
    private static final String TAG = "LocationManager";

    // 单例实例
    private static LocationManager instance;

    // 上下文
    private Context context;

    // 系统位置管理器
    private android.location.LocationManager systemLocationManager;

    // 当前位置
    private LocationData currentLocation;

    // 位置监听器列表
    private List<LocationChangeListener> listeners;

    // 系统位置监听器
    private LocationListener systemLocationListener;

    // 是否正在更新位置
    private boolean isUpdatingLocation;

    // 地理编码器
    private Geocoder geocoder;

    // 位置更新最小时间间隔（毫秒）
    private static final long MIN_TIME = 10000; // 10秒

    // 位置更新最小距离（米）
    private static final float MIN_DISTANCE = 10; // 10米

    // 高精度位置更新最小时间间隔（毫秒）
    private static final long HIGH_ACCURACY_MIN_TIME = 5000; // 5秒

    // 高精度位置更新最小距离（米）
    private static final float HIGH_ACCURACY_MIN_DISTANCE = 5; // 5米

    // 低功耗位置更新最小时间间隔（毫秒）
    private static final long LOW_POWER_MIN_TIME = 30000; // 30秒

    // 低功耗位置更新最小距离（米）
    private static final float LOW_POWER_MIN_DISTANCE = 50; // 50米

    // 位置提供者
    private String provider;

    // 位置超时处理器
    private Handler timeoutHandler;

    // 位置超时时间（毫秒）
    private static final long LOCATION_TIMEOUT = 30000; // 30秒

    /**
     * 获取单例实例
     * @param context 上下文
     * @return LocationManager实例
     */
    public static synchronized LocationManager getInstance(Context context) {
        if (instance == null) {
            instance = new LocationManager(context);
        }
        return instance;
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    private LocationManager(Context context) {
        this.context = context.getApplicationContext();
        this.listeners = new ArrayList<>();
        this.isUpdatingLocation = false;

        // 初始化系统位置管理器
        systemLocationManager = (android.location.LocationManager) context.getSystemService(Context.LOCATION_SERVICE);

        // 初始化地理编码器
        if (Geocoder.isPresent()) {
            geocoder = new Geocoder(context, Locale.getDefault());
        }

        // 初始化位置提供者
        initLocationProvider();

        // 初始化位置监听器
        initLocationListener();

        // 初始化超时处理器
        timeoutHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 初始化位置提供者
     */
    private void initLocationProvider() {
        // 默认使用高精度模式（优先GPS）
        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            provider = android.location.LocationManager.GPS_PROVIDER;
            Log.d(TAG, "Using GPS provider");
        } else if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
            provider = android.location.LocationManager.NETWORK_PROVIDER;
            Log.d(TAG, "Using network provider (GPS not available)");
        } else {
            provider = null;
            Log.e(TAG, "No location provider available");
        }

        Log.d(TAG, "Using location provider: " + provider);
    }

    /**
     * 初始化位置监听器
     */
    private void initLocationListener() {
        systemLocationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                // 处理位置更新
                handleLocationUpdate(location);
            }

            @Override
            public void onStatusChanged(String provider, int status, Bundle extras) {
                // 处理位置提供者状态变化
                Log.d(TAG, "Location provider status changed: " + provider + ", status: " + status);
            }

            @Override
            public void onProviderEnabled(String provider) {
                // 处理位置提供者启用
                Log.d(TAG, "Location provider enabled: " + provider);
            }

            @Override
            public void onProviderDisabled(String provider) {
                // 处理位置提供者禁用
                Log.d(TAG, "Location provider disabled: " + provider);
            }
        };
    }

    /**
     * 处理位置更新
     * @param location 位置
     */
    private void handleLocationUpdate(Location location) {
        if (location != null) {
            // 取消超时
            timeoutHandler.removeCallbacksAndMessages(null);

            // 转换为LocationData
            LocationData locationData = convertToLocationData(location);

            // 获取地址信息
            getAddressFromLocation(locationData);

            // 更新当前位置
            currentLocation = locationData;

            // 通知监听器
            notifyListeners(locationData);

            Log.d(TAG, "Location updated: " + locationData);
        }
    }

    /**
     * 转换为LocationData
     * @param location 位置
     * @return LocationData
     */
    private LocationData convertToLocationData(Location location) {
        LocationData locationData = new LocationData(
                location.getLatitude(),
                location.getLongitude(),
                location.getAltitude(),
                location.getAccuracy(),
                location.getSpeed(),
                location.getBearing(),
                location.getProvider()
        );
        locationData.setTime(location.getTime());
        return locationData;
    }

    /**
     * 获取地址信息
     * @param locationData 位置数据
     */
    private void getAddressFromLocation(final LocationData locationData) {
        if (geocoder == null) {
            return;
        }

        try {
            List<Address> addresses = geocoder.getFromLocation(
                    locationData.getLatitude(),
                    locationData.getLongitude(),
                    1
            );

            if (addresses != null && !addresses.isEmpty()) {
                Address address = addresses.get(0);

                // 设置地址信息
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i <= address.getMaxAddressLineIndex(); i++) {
                    if (i > 0) {
                        sb.append(", ");
                    }
                    sb.append(address.getAddressLine(i));
                }
                locationData.setAddress(sb.toString());

                // 设置详细地址信息
                locationData.setCountry(address.getCountryName());
                locationData.setProvince(address.getAdminArea());
                locationData.setCity(address.getLocality());
                locationData.setDistrict(address.getSubLocality());
                locationData.setStreet(address.getThoroughfare());
                locationData.setStreetNumber(address.getSubThoroughfare());
                locationData.setPostalCode(address.getPostalCode());

                Log.d(TAG, "Address: " + locationData.getAddress());
            }
        } catch (IOException e) {
            Log.e(TAG, "Error getting address: " + e.getMessage());
        }
    }

    /**
     * 通知监听器
     * @param locationData 位置数据
     */
    private void notifyListeners(LocationData locationData) {
        for (LocationChangeListener listener : listeners) {
            listener.onLocationChanged(locationData);
        }
    }

    /**
     * 获取当前位置
     * @param callback 回调接口
     */
    public void getCurrentLocation(final ApiCallback<LocationData> callback) {
        // 检查权限
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            callback.onError(ApiError.createNetworkError("没有位置权限"));
            return;
        }

        // 如果已经有当前位置，直接返回
        if (currentLocation != null) {
            callback.onSuccess(currentLocation);
            return;
        }

        // 获取最后已知位置
        Location lastKnownLocation = null;

        // 尝试从GPS获取最后已知位置
        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            lastKnownLocation = systemLocationManager.getLastKnownLocation(android.location.LocationManager.GPS_PROVIDER);
        }

        // 如果GPS没有位置，尝试从网络获取
        if (lastKnownLocation == null && systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
            lastKnownLocation = systemLocationManager.getLastKnownLocation(android.location.LocationManager.NETWORK_PROVIDER);
        }

        // 如果有最后已知位置，直接返回
        if (lastKnownLocation != null) {
            // 转换为LocationData
            LocationData locationData = convertToLocationData(lastKnownLocation);

            // 获取地址信息
            getAddressFromLocation(locationData);

            // 更新当前位置
            currentLocation = locationData;

            // 返回位置
            callback.onSuccess(locationData);

            Log.d(TAG, "Last known location: " + locationData);
        } else {
            // 没有最后已知位置，请求位置更新
            requestSingleLocationUpdate(callback);
        }
    }

    /**
     * 请求单次位置更新
     * @param callback 回调接口
     */
    private void requestSingleLocationUpdate(final ApiCallback<LocationData> callback) {
        // 检查权限
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            callback.onError(ApiError.createNetworkError("没有位置权限"));
            return;
        }

        // 创建一次性位置监听器
        final LocationListener oneTimeListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                // 移除位置更新
                systemLocationManager.removeUpdates(this);

                // 取消超时
                timeoutHandler.removeCallbacksAndMessages(null);

                // 转换为LocationData
                LocationData locationData = convertToLocationData(location);

                // 获取地址信息
                getAddressFromLocation(locationData);

                // 更新当前位置
                currentLocation = locationData;

                // 返回位置
                callback.onSuccess(locationData);

                Log.d(TAG, "Current location: " + locationData);
            }

            @Override
            public void onStatusChanged(String provider, int status, Bundle extras) {
                // 不处理
            }

            @Override
            public void onProviderEnabled(String provider) {
                // 不处理
            }

            @Override
            public void onProviderDisabled(String provider) {
                // 移除位置更新
                systemLocationManager.removeUpdates(this);

                // 取消超时
                timeoutHandler.removeCallbacksAndMessages(null);

                // 返回错误
                callback.onError(ApiError.createNetworkError("位置提供者已禁用: " + provider));
            }
        };

        // 设置超时
        timeoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 移除位置更新
                systemLocationManager.removeUpdates(oneTimeListener);

                // 返回错误
                callback.onError(ApiError.createNetworkError("获取位置超时"));

                Log.e(TAG, "Location request timed out");
            }
        }, LOCATION_TIMEOUT);

        // 请求位置更新
        if (provider != null) {
            systemLocationManager.requestLocationUpdates(
                    provider,
                    0,
                    0,
                    oneTimeListener,
                    Looper.getMainLooper()
            );

            Log.d(TAG, "Requested single location update from provider: " + provider);
        } else {
            // 尝试使用GPS和网络提供者
            boolean requestedUpdate = false;

            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                        android.location.LocationManager.GPS_PROVIDER,
                        0,
                        0,
                        oneTimeListener,
                        Looper.getMainLooper()
                );
                requestedUpdate = true;
                Log.d(TAG, "Requested single location update from GPS provider");
            }

            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                        android.location.LocationManager.NETWORK_PROVIDER,
                        0,
                        0,
                        oneTimeListener,
                        Looper.getMainLooper()
                );
                requestedUpdate = true;
                Log.d(TAG, "Requested single location update from network provider");
            }

            if (!requestedUpdate) {
                // 取消超时
                timeoutHandler.removeCallbacksAndMessages(null);

                // 返回错误
                callback.onError(ApiError.createNetworkError("没有可用的位置提供者"));

                Log.e(TAG, "No location provider available");
            }
        }
    }

    /**
     * 开始位置更新
     */
    public void startLocationUpdates() {
        // 检查权限
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "No location permission");
            return;
        }

        // 如果已经在更新位置，直接返回
        if (isUpdatingLocation) {
            return;
        }

        // 请求位置更新
        if (provider != null) {
            systemLocationManager.requestLocationUpdates(
                    provider,
                    MIN_TIME,
                    MIN_DISTANCE,
                    systemLocationListener,
                    Looper.getMainLooper()
            );

            isUpdatingLocation = true;

            Log.d(TAG, "Location updates started with provider: " + provider);
        } else {
            // 尝试使用GPS和网络提供者
            boolean requestedUpdate = false;

            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                        android.location.LocationManager.GPS_PROVIDER,
                        MIN_TIME,
                        MIN_DISTANCE,
                        systemLocationListener,
                        Looper.getMainLooper()
                );
                requestedUpdate = true;
                Log.d(TAG, "Location updates started with GPS provider");
            }

            if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                systemLocationManager.requestLocationUpdates(
                        android.location.LocationManager.NETWORK_PROVIDER,
                        MIN_TIME,
                        MIN_DISTANCE,
                        systemLocationListener,
                        Looper.getMainLooper()
                );
                requestedUpdate = true;
                Log.d(TAG, "Location updates started with network provider");
            }

            if (requestedUpdate) {
                isUpdatingLocation = true;
            } else {
                Log.e(TAG, "No location provider available");
            }
        }
    }

    /**
     * 停止位置更新
     */
    public void stopLocationUpdates() {
        // 如果没有在更新位置，直接返回
        if (!isUpdatingLocation) {
            return;
        }

        // 移除位置更新
        systemLocationManager.removeUpdates(systemLocationListener);

        isUpdatingLocation = false;

        Log.d(TAG, "Location updates stopped");
    }

    /**
     * 添加位置监听器
     * @param listener 位置监听器
     */
    public void addLocationListener(LocationChangeListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 移除位置监听器
     * @param listener 位置监听器
     */
    public void removeLocationListener(LocationChangeListener listener) {
        listeners.remove(listener);
    }

    /**
     * 获取当前位置
     * @return 当前位置
     */
    public LocationData getCurrentLocation() {
        return currentLocation;
    }

    /**
     * 获取最后已知位置
     * @return 最后已知位置，如果没有则返回null
     */
    public Location getLastKnownLocation() {
        // 检查权限
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "No location permission");
            return null;
        }

        // 获取最后已知位置
        Location lastKnownLocation = null;

        // 尝试从GPS获取最后已知位置
        if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
            lastKnownLocation = systemLocationManager.getLastKnownLocation(android.location.LocationManager.GPS_PROVIDER);
        }

        // 如果GPS没有位置，尝试从网络获取
        if (lastKnownLocation == null && systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
            lastKnownLocation = systemLocationManager.getLastKnownLocation(android.location.LocationManager.NETWORK_PROVIDER);
        }

        return lastKnownLocation;
    }

    /**
     * 位置更新模式
     */
    public enum LocationMode {
        /**
         * 高精度模式
         * 使用GPS和网络提供者，更新频率高，精度高，耗电量大
         */
        HIGH_ACCURACY,

        /**
         * 平衡模式
         * 使用GPS和网络提供者，更新频率中等，精度中等，耗电量中等
         */
        BALANCED,

        /**
         * 低功耗模式
         * 优先使用网络提供者，更新频率低，精度低，耗电量小
         */
        LOW_POWER
    }

    /**
     * 设置位置更新模式
     * @param mode 位置更新模式
     */
    public void setLocationMode(LocationMode mode) {
        // 如果正在更新位置，先停止更新
        if (isUpdatingLocation) {
            stopLocationUpdates();
        }

        // 根据模式选择合适的位置提供者
        switch (mode) {
            case HIGH_ACCURACY:
                // 高精度模式优先使用GPS，然后是网络
                if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
                    provider = android.location.LocationManager.GPS_PROVIDER;
                    Log.d(TAG, "Using GPS provider for high accuracy mode");
                } else if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                    provider = android.location.LocationManager.NETWORK_PROVIDER;
                    Log.d(TAG, "Using network provider for high accuracy mode (GPS not available)");
                } else {
                    provider = null;
                    Log.e(TAG, "No location provider available for high accuracy mode");
                }
                break;

            case BALANCED:
                // 平衡模式优先使用网络，但如果只有GPS可用，也会使用GPS
                if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                    provider = android.location.LocationManager.NETWORK_PROVIDER;
                    Log.d(TAG, "Using network provider for balanced mode");
                } else if (systemLocationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER)) {
                    provider = android.location.LocationManager.GPS_PROVIDER;
                    Log.d(TAG, "Using GPS provider for balanced mode (network not available)");
                } else {
                    provider = null;
                    Log.e(TAG, "No location provider available for balanced mode");
                }
                break;

            case LOW_POWER:
                // 低功耗模式只使用网络提供者
                if (systemLocationManager.isProviderEnabled(android.location.LocationManager.NETWORK_PROVIDER)) {
                    provider = android.location.LocationManager.NETWORK_PROVIDER;
                    Log.d(TAG, "Using network provider for low power mode");
                } else {
                    provider = null;
                    Log.e(TAG, "Network provider not available for low power mode");
                }
                break;
        }

        Log.d(TAG, "Location mode set to " + mode + ", using provider: " + provider);

        // 如果之前正在更新位置，重新开始更新
        if (isUpdatingLocation) {
            startLocationUpdates();
        }
    }

    /**
     * 位置监听器接口
     */
    public interface LocationChangeListener {
        /**
         * 位置改变回调
         * @param locationData 位置数据
         */
        void onLocationChanged(LocationData locationData);
    }
}
