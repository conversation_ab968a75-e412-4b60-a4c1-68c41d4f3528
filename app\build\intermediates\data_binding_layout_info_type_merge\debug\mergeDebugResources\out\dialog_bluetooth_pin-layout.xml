<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_bluetooth_pin" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\dialog_bluetooth_pin.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_bluetooth_pin_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="43" endOffset="14"/></Target><Target id="@+id/tv_device_name" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="14" endOffset="34"/></Target><Target id="@+id/et_pin_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="25" startOffset="8" endLine="30" endOffset="35"/></Target><Target id="@+id/tv_pin_info" view="TextView"><Expressions/><location startLine="34" startOffset="4" endLine="41" endOffset="56"/></Target></Targets></Layout>