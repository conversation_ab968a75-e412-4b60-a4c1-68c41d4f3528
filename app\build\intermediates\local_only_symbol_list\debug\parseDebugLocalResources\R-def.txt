R_DEF: Internal format may change without notice
local
color black
color colorAccent
color colorBackground
color colorDivider
color colorPrimary
color colorPrimaryDark
color colorTextPrimary
color colorTextSecondary
color white
drawable background_call_button
drawable background_received_message
drawable background_sent_message
drawable bubble_other
drawable bubble_self
drawable circle_shape
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable loadingbgn
id bluetooth_status_container
id bottom_action_bar
id btnRefresh
id btn_Change_Cam
id btn_apply
id btn_back
id btn_call
id btn_cap
id btn_chat
id btn_clear
id btn_delete
id btn_device_list
id btn_finish
id btn_get_location
id btn_join_room
id btn_language_settings
id btn_leave_room
id btn_login
id btn_open_config
id btn_play_pause
id btn_qr_code
id btn_refresh
id btn_report_command
id btn_report_location
id btn_report_status
id btn_select_all
id btn_select_mode
id btn_speak
id btn_start_chat
id btn_talk
id call_bg
id call_icon
id card_network_info
id cb_show_password
id checkbox
id control_panel
id data_roaming_container
id dialog_loading_view
id divider
id divider2
id et_pin_code
id et_wifi_password
id hint
id img_icon
id img_thumbnail
id img_video_indicator
id item_icon
id ivQrCode
id iv_bluetooth_icon
id iv_other_avatar
id iv_wifi_lock
id iv_wifi_signal
id language_radio_group
id local_view_container
id main
id mobile_data_container
id photo_view
id progressBar
id progressBar1
id progress_bar
id radio_chinese
id radio_english
id radio_system
id recyclerViewChat
id recycler_view
id result_bg
id result_bg_mask
id room_id_input
id rv_available_devices
id rv_paired_devices
id rv_wifi_list
id save_config_button
id scroll_view
id seek_bar
id server_host_input
id setting_icon
id setting_title
id surface
id swipe_refresh
id switch_bluetooth
id switch_data_roaming
id switch_mobile_data
id switch_wifi
id tab_layout
id textViewMessageReceived
id textViewMessageSent
id tipTextView
id toolbar
id tvDeviceId
id tvExpireTime
id tvInstructions
id tvStatus
id tvTitle
id tv_available_devices
id tv_available_networks
id tv_bluetooth_status_label
id tv_cn_result
id tv_data_roaming_description
id tv_data_roaming_label
id tv_device_address
id tv_device_name
id tv_device_status
id tv_duration
id tv_empty
id tv_empty_state
id tv_en_result
id tv_mobile_data_label
id tv_name
id tv_network_info_label
id tv_network_operator
id tv_network_status
id tv_network_type
id tv_paired_devices
id tv_permission_warning
id tv_pin_info
id tv_result
id tv_roaming
id tv_signal_strength
id tv_title
id tv_wifi_name
id tv_wifi_status
id tv_wifi_status_label
id user_id_input
id video_view
id viewPager
id view_pager
id wifi_status_container
layout activity_bluetooth
layout activity_call
layout activity_capturectivity
layout activity_chat
layout activity_chat_cnactivity
layout activity_config
layout activity_device_registration
layout activity_gallery
layout activity_home
layout activity_language_settings
layout activity_learn
layout activity_location
layout activity_main
layout activity_mobile_network
layout activity_recognition
layout activity_repository_test
layout activity_setting
layout activity_wifi
layout call_fragment_page
layout dialog_bluetooth_pin
layout dialog_loading
layout dialog_wifi_password
layout fragment_media_list
layout fragment_photo_detail
layout fragment_video_detail
layout home_list_item
layout item_bluetooth
layout item_media
layout item_message_received
layout item_message_sent
layout item_wifi
layout learn_fragment_page
layout setting_list_item
mipmap arrow_back
mipmap btn_capture
mipmap call_accept
mipmap call_mianti_off
mipmap call_mute_off
mipmap call_mute_on
mipmap call_refuse
mipmap camera_switch
mipmap chat_icon
mipmap dialog_loading_img
mipmap ic_launcher
mipmap ic_launcher_round
mipmap icon_4g
mipmap icon_about
mipmap icon_bind
mipmap icon_bluetooth
mipmap icon_call
mipmap icon_location
mipmap icon_private
mipmap icon_resume
mipmap icon_shutdwon
mipmap icon_voice
mipmap icon_wifi
mipmap icon_wifi_lock
mipmap iocn_wifi
mipmap item_bg_da
mipmap item_bg_gallery
mipmap item_bg_ji
mipmap item_bg_liao_en
mipmap item_bg_liao_zh
mipmap item_bg_pai
mipmap item_bg_setting
mipmap item_bg_shi
mipmap item_bg_xue
mipmap learn_bg_baijuyi
mipmap learn_bg_dufu
mipmap learn_bg_libai
mipmap learn_icon_baijuyi
mipmap learn_icon_dufu
mipmap learn_icon_libai
mipmap my_icon
mipmap recognize_result
mipmap shibie_bg
mipmap talking_static
string all_media
string api_test
string app_name
string apply
string available_devices
string available_networks
string battery_level
string bind_failed
string bind_success
string bluetooth
string bluetooth_already_connected
string bluetooth_connect
string bluetooth_connected
string bluetooth_connecting
string bluetooth_connection_failed
string bluetooth_device_address
string bluetooth_device_name
string bluetooth_device_type
string bluetooth_disconnect
string bluetooth_disconnected
string bluetooth_enter_pin
string bluetooth_not_supported
string bluetooth_pair
string bluetooth_paired
string bluetooth_pairing
string bluetooth_pairing_failed
string bluetooth_permission_required
string bluetooth_pin_empty
string bluetooth_scan_failed
string bluetooth_settings
string bluetooth_unpair
string camera
string cancel
string chat
string chat_cn
string chat_en
string chat_history
string chinese
string clear_history
string clear_history_confirm
string confirm
string current_location
string data_roaming
string data_roaming_description
string delete
string delete_confirm
string delete_selected
string deselect_all
string device_id
string device_info
string disable_mobile_data
string enable_bluetooth
string enable_location
string enable_mobile_data
string english
string error_already_bound
string error_authentication
string error_connection
string error_device_not_found
string error_invalid_input
string error_network
string error_not_bound
string error_server
string error_timeout
string error_unknown
string gallery
string go_to_settings
string history_cleared
string home
string language_changed
string language_settings
string last_updated
string loading
string loading_media
string location
string location_not_available
string location_permission_required
string location_required
string location_service_required
string location_settings
string location_switch
string location_updated
string login
string microphone
string mobile_data
string mobile_data_disabled
string mobile_data_enabled
string mobile_network
string mobile_network_connected
string mobile_network_disconnected
string mobile_network_error
string mobile_network_info
string mobile_network_not_available
string mobile_network_permission_denied
string mobile_network_permission_required
string mobile_network_settings
string mobile_network_status
string network_operator
string network_status
string network_type
string no_bluetooth_devices
string no_media_found
string no_more_history
string no_wifi_networks
string ok
string paired_devices
string pause
string permission_denied
string permission_denied_message
string permission_required
string phone_state
string photos
string play
string press_to_talk
string qr_code_expired
string qr_code_valid_for
string refresh
string register
string release_to_stop
string restart_app_message
string retry
string roaming
string roaming_disabled
string roaming_enabled
string save
string scan_failed
string scan_qr_code
string scanning_bluetooth
string scanning_wifi
string select_all
string settings
string signal_strength
string storage_permission_required
string system_language
string unbind_confirm
string unbind_device
string unbind_failed
string unbind_success
string unknown
string upload_failed
string upload_history
string upload_success
string videos
string wifi
string wifi_already_connected
string wifi_connect
string wifi_connected
string wifi_connecting
string wifi_connection_failed
string wifi_disconnected
string wifi_enter_password
string wifi_password
string wifi_password_empty
string wifi_settings
string wifi_show_password
style Base.Theme.BBNice
style MyDialogStyle
style Theme.BBNice
xml backup_rules
xml data_extraction_rules
