R_DEF: Internal format may change without notice
local
color black
color colorAccent
color colorBackground
color colorDivider
color colorPrimary
color colorPrimaryDark
color colorTextPrimary
color colorTextSecondary
color white
drawable background_call_button
drawable background_received_message
drawable background_sent_message
drawable bubble_other
drawable bubble_self
drawable circle_shape
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable loadingbgn
id auto_brightness_container
id bluetooth_status_container
id bottom_action_bar
id btnRefresh
id btn_Change_Cam
id btn_ai_agent
id btn_apply
id btn_back
id btn_call
id btn_cap
id btn_chat
id btn_clear
id btn_copy_app_version
id btn_copy_device_model
id btn_copy_device_name
id btn_copy_device_serial
id btn_copy_device_version
id btn_delete
id btn_device_list
id btn_finish
id btn_get_location
id btn_join_room
id btn_language_settings
id btn_leave_room
id btn_login
id btn_luminance_down
id btn_luminance_up
id btn_media_volume_down
id btn_media_volume_up
id btn_open_config
id btn_play_pause
id btn_qr_code
id btn_refresh
id btn_report_command
id btn_report_location
id btn_report_status
id btn_ring_volume_down
id btn_ring_volume_up
id btn_select_all
id btn_select_mode
id btn_speak
id btn_start_chat
id btn_talk
id btn_upload_media
id call_bg
id call_icon
id card_network_info
id cb_show_password
id checkbox
id content_container
id control_panel
id data_roaming_container
id dialog_loading_view
id divider
id divider2
id et_pin_code
id et_wifi_password
id hint
id img_icon
id img_thumbnail
id img_video_indicator
id item_icon
id ivLogo
id ivQrCode
id iv_bluetooth_icon
id iv_checked
id iv_other_avatar
id iv_wifi_lock
id iv_wifi_signal
id language_radio_group
id loc_status_container
id local_view_container
id main
id media_luminance_container
id media_volume_container
id mobile_data_container
id mute_container
id photo_view
id progressBar
id progressBar1
id progress_bar
id radio_chinese
id radio_english
id radio_system
id recyclerViewChat
id recycler_view
id result_bg
id result_bg_mask
id ring_volume_container
id room_id_input
id rv_available_devices
id rv_duration_list
id rv_paired_devices
id rv_wifi_list
id save_config_button
id scroll_view
id seek_bar
id seekbar_media_volume
id seekbar_ring_volume
id seekbar_screen_luminance
id server_host_input
id setting_icon
id setting_title
id surface
id swipe_refresh
id switch_auto_brightness
id switch_bluetooth
id switch_data_roaming
id switch_location
id switch_mobile_data
id switch_mute
id switch_wifi
id tab_layout
id textViewMessageReceived
id textViewMessageSent
id tipTextView
id title
id toolbar
id tvAppName
id tvDeviceId
id tvExpireTime
id tvInstructions
id tvStatus
id tvTitle
id tvVersion
id tv_app_build
id tv_app_version
id tv_auto_brightness_label
id tv_available_devices
id tv_available_networks
id tv_bluetooth_status_label
id tv_brightness_level
id tv_cn_result
id tv_data_roaming_description
id tv_data_roaming_label
id tv_device_address
id tv_device_model
id tv_device_name
id tv_device_serial
id tv_device_status
id tv_device_version
id tv_duration
id tv_empty
id tv_empty_state
id tv_en_result
id tv_loc_status_label
id tv_media_luminance_label
id tv_media_volume_label
id tv_media_volume_level
id tv_mobile_data_label
id tv_mute_label
id tv_name
id tv_network_info_label
id tv_network_operator
id tv_network_status
id tv_network_type
id tv_paired_devices
id tv_permission_warning
id tv_pin_info
id tv_result
id tv_ring_volume_label
id tv_ring_volume_level
id tv_roaming
id tv_signal_strength
id tv_title
id tv_wake_up_duration_label
id tv_wifi_name
id tv_wifi_status
id tv_wifi_status_label
id user_id_input
id video_view
id viewPager
id view_pager
id wifi_status_container
layout activity_about
layout activity_bluetooth
layout activity_call
layout activity_capturectivity
layout activity_chat
layout activity_chat_cnactivity
layout activity_config
layout activity_device_registration
layout activity_device_registration_new
layout activity_display
layout activity_gallery
layout activity_home
layout activity_language_settings
layout activity_learn
layout activity_location
layout activity_main
layout activity_mobile_network
layout activity_private
layout activity_recognition
layout activity_repository_test
layout activity_setting
layout activity_splash
layout activity_voice
layout activity_wifi
layout call_fragment_page
layout dialog_bluetooth_pin
layout dialog_loading
layout dialog_wifi_password
layout fragment_media_list
layout fragment_photo_detail
layout fragment_video_detail
layout home_list_item
layout item_bluetooth
layout item_media
layout item_message_received
layout item_message_sent
layout item_wake_up_duration
layout item_wifi
layout learn_fragment_page
layout setting_list_item
mipmap arrow_back
mipmap btn_capture
mipmap call_accept
mipmap call_mianti_off
mipmap call_mute_off
mipmap call_mute_on
mipmap call_refuse
mipmap camera_switch
mipmap chat_icon
mipmap dialog_loading_img
mipmap ic_ensure
mipmap ic_launcher
mipmap ic_launcher_round
mipmap ic_luminance_down
mipmap ic_luminance_up
mipmap icon_4g
mipmap icon_about
mipmap icon_bind
mipmap icon_bluetooth
mipmap icon_call
mipmap icon_location
mipmap icon_private
mipmap icon_resume
mipmap icon_shutdwon
mipmap icon_voice
mipmap icon_voice_minus
mipmap icon_voice_pluse
mipmap icon_wifi
mipmap icon_wifi_lock
mipmap iocn_wifi
mipmap item_bg_da
mipmap item_bg_gallery
mipmap item_bg_ji
mipmap item_bg_liao_en
mipmap item_bg_liao_zh
mipmap item_bg_pai
mipmap item_bg_setting
mipmap item_bg_shi
mipmap item_bg_xue
mipmap learn_bg_baijuyi
mipmap learn_bg_dufu
mipmap learn_bg_libai
mipmap learn_icon_baijuyi
mipmap learn_icon_dufu
mipmap learn_icon_libai
mipmap my_icon
mipmap recognize_result
mipmap shibie_bg
mipmap talking_static
string about_device
string alarm_volume
string all_media
string api_test
string app_build_number
string app_name
string app_version
string app_version_name
string apply
string audio_permission_required
string auto_brightness
string auto_brightness_disabled
string auto_brightness_enabled
string available_devices
string available_networks
string battery_level
string bind_failed
string bind_success
string bluetooth
string bluetooth_a2dp_connect_system_settings
string bluetooth_a2dp_disconnect_system_settings
string bluetooth_a2dp_profile_unavailable
string bluetooth_already_connected
string bluetooth_connect
string bluetooth_connected
string bluetooth_connecting
string bluetooth_connection_failed
string bluetooth_device_address
string bluetooth_device_name
string bluetooth_device_type
string bluetooth_disabled
string bluetooth_disconnect
string bluetooth_disconnected
string bluetooth_enter_pin
string bluetooth_error_a2dp_devices
string bluetooth_error_check_connected
string bluetooth_error_close_a2dp_proxy
string bluetooth_error_close_headset_proxy
string bluetooth_error_connecting
string bluetooth_error_disconnecting
string bluetooth_error_get_connected_devices
string bluetooth_error_get_device_uuid
string bluetooth_error_headset_devices
string bluetooth_error_pairing
string bluetooth_error_profile_proxy
string bluetooth_error_socket_close
string bluetooth_error_unpairing
string bluetooth_gatt_not_supported
string bluetooth_headset_connect_system_settings
string bluetooth_headset_disconnect_system_settings
string bluetooth_headset_profile_unavailable
string bluetooth_initiate_pairing_failed
string bluetooth_not_supported
string bluetooth_pair
string bluetooth_paired
string bluetooth_pairing
string bluetooth_pairing_failed
string bluetooth_permission_required
string bluetooth_permissions_not_granted
string bluetooth_pin_empty
string bluetooth_scan_failed
string bluetooth_scan_start_failed
string bluetooth_settings
string bluetooth_unpair
string bluetooth_unpair_failed
string brightness_down
string brightness_level
string brightness_permission_required
string brightness_up
string call_volume
string camera
string cancel
string chat
string chat_cn
string chat_en
string chat_history
string checking_login_status
string chinese
string clear_history
string clear_history_confirm
string confirm
string copied_to_clipboard
string copy_to_clipboard
string current_location
string data_roaming
string data_roaming_description
string delete
string delete_confirm
string delete_selected
string deselect_all
string device_id
string device_info
string device_model
string device_name
string device_registration
string device_serial
string device_version
string disable_mobile_data
string disable_mute
string display
string do_not_disturb_permission_required
string enable_bluetooth
string enable_location
string enable_mobile_data
string enable_mute
string english
string error_already_bound
string error_authentication
string error_connection
string error_device_not_found
string error_invalid_input
string error_network
string error_not_bound
string error_server
string error_timeout
string error_unknown
string gallery
string go_to_notification_settings
string go_to_settings
string go_to_settings_permission
string history_cleared
string home
string language_changed
string language_settings
string last_updated
string loading
string loading_media
string location
string location_not_available
string location_permission_required
string location_required
string location_service_required
string location_settings
string location_switch
string location_updated
string login
string login_failed
string login_success
string media_volume
string microphone
string mobile_data
string mobile_data_disabled
string mobile_data_enabled
string mobile_network
string mobile_network_connected
string mobile_network_disconnected
string mobile_network_error
string mobile_network_info
string mobile_network_not_available
string mobile_network_permission_denied
string mobile_network_permission_required
string mobile_network_settings
string mobile_network_status
string mute_disabled
string mute_enabled
string mute_mode
string network
string network_operator
string network_status
string network_type
string no_bluetooth_devices
string no_media_found
string no_more_history
string no_wifi_networks
string notification_volume
string ok
string paired_devices
string pause
string permission_denied
string permission_denied_message
string permission_required
string phone_permission_required
string phone_state
string photos
string play
string press_to_talk
string private_agreement
string private_agreement_content
string qr_code
string qr_code_expired
string qr_code_refresh_time
string qr_code_valid_for
string refresh
string refreshing_qr_code
string register
string release_to_stop
string restart_app_message
string retry
string ring_volume
string roaming
string roaming_disabled
string roaming_enabled
string save
string scan_failed
string scan_qr_code
string scan_qr_instruction
string scanning_bluetooth
string scanning_wifi
string screen_luminance
string screen_timeout_set
string screen_timeout_set_failed
string screen_wake_up
string screen_wake_up_disabled
string screen_wake_up_enabled
string second
string select_all
string settings
string signal_strength
string storage
string storage_permission_required
string system_language
string system_volume
string unbind_confirm
string unbind_device
string unbind_failed
string unbind_success
string unknown
string unknown_device
string upload_failed
string upload_history
string upload_success
string videos
string voice
string voice_settings
string volume_control
string volume_down
string volume_level
string volume_up
string waiting_for_scan
string wake_up_duration
string wifi
string wifi_already_connected
string wifi_connect
string wifi_connected
string wifi_connecting
string wifi_connection_failed
string wifi_disconnected
string wifi_enter_password
string wifi_password
string wifi_password_empty
string wifi_settings
string wifi_show_password
style Base.Theme.BBNice
style MyDialogStyle
style Theme.BBNice
xml backup_rules
xml data_extraction_rules
