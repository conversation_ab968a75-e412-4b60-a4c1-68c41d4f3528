package com.srthinker.bbnice;

import android.app.Application;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.core.ServiceManager;
import com.srthinker.bbnice.utils.LocaleHelper;
import com.srthinker.bbnice.utils.PermissionManager;

/**
 * Created by yzy on 2025/4/11.
 */
public class App extends Application {
    private static final String TAG = "BBNiceApp";
    private static App sInstance;
    private volatile Handler mainHandler;

    public static App getInstance() {
        return sInstance;
    }

    /**
     * 检查是否已授予所有必需的权限
     * @return 是否已授予所有权限
     */
    public boolean hasAllRequiredPermissions() {
        return PermissionManager.hasAllRequiredPermissions(this);
    }

    /**
     * 设置权限状态
     * @param granted 是否已授予所有权限
     */
    public void setPermissionsGranted(boolean granted) {
        // 如果权限已授予，初始化需要权限的功能
        if (granted) {
            initializeWithPermissions();
        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(LocaleHelper.setLocale(base, LocaleHelper.getLanguage(base)));
    }

    @Override
    public void onCreate() {
        super.onCreate();
        sInstance = this;
        mainHandler = new Handler(Looper.getMainLooper());

        // 初始化RepositoryProvider
        ApiRepositoryProvider.getInstance(this);

        // 检查权限
        if (hasAllRequiredPermissions()) {
            // 如果已经有所有权限，直接初始化
            setPermissionsGranted(true);
        }
        // 注意：如果没有所有权限，将在Activity中请求权限，
        // 然后通过setPermissionsGranted方法通知App类
    }

    /**
     * 初始化需要权限的功能
     */
    private void initializeWithPermissions() {
        Log.d(TAG, "初始化需要权限的功能");

        // 使用ServiceManager初始化所有服务
        ServiceManager.getInstance(this).initializeServices();
    }

    public void runOnUiThread(Runnable runnable) {
        mainHandler.post(runnable);
    }

    @Override
    public void onTerminate() {
        // 停止所有服务
        ServiceManager.getInstance(this).stopAllServices();

        super.onTerminate();
    }
}
