package com.srthinker.bbnice.location;

import android.content.Context;
import android.util.Log;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.repository.LocationRepository;
import com.srthinker.bbnice.api.repository.RepositoryProvider;

/**
 * 地理位置示例类
 * 演示如何使用地理位置功能
 */
public class LocationExample {
    private static final String TAG = "LocationExample";

    /**
     * 获取当前位置示例
     * @param context 上下文
     */
    public static void getCurrentLocationExample(Context context) {
        // 获取位置管理器
        LocationManager locationManager = LocationManager.getInstance(context);

        // 获取当前位置
        locationManager.getCurrentLocation(new ApiCallback<LocationData>() {
            @Override
            public void onSuccess(LocationData locationData) {
                Log.d(TAG, "Current location: " + locationData);

                // 上报位置
                reportLocationExample(context, locationData);
            }

            @Override
            public void onError(ApiError error) {
                Log.e(TAG, "Error getting current location: " + error.getMessage());
            }
        });
    }

    /**
     * 上报位置示例
     * @param context 上下文
     * @param locationData 位置数据
     */
    public static void reportLocationExample(Context context, LocationData locationData) {
        // 获取LocationRepository
        LocationRepository locationRepository = RepositoryProvider.getInstance(context).getLocationRepository();

        // 上报位置
        locationRepository.reportLocation(locationData).observeForever(result -> {
            if (result.isLoading()) {
                return;
            }

            if (result.isSuccess()) {
                BaseResponse response = result.getData();
                if (response.isSuccess()) {
                    Log.d(TAG, "Location reported successfully");
                } else {
                    Log.e(TAG, "Failed to report location: " + response.getMessage());
                }
            } else {
                Throwable error = result.getError();
                Log.e(TAG, "Error reporting location: " + (error != null ? error.getMessage() : "Unknown error"));
            }

            // 移除观察者，避免内存泄漏
            locationRepository.reportLocation(locationData).removeObserver(result1 -> {});
        });
    }


    /**
     * 启动位置服务示例
     * @param context 上下文
     */
    public static void startLocationServiceExample(Context context) {
        // 启动位置服务
        LocationService.startService(context);

        Log.d(TAG, "Location service started");
    }

    /**
     * 停止位置服务示例
     * @param context 上下文
     */
    public static void stopLocationServiceExample(Context context) {
        // 停止位置服务
        LocationService.stopService(context);

        Log.d(TAG, "Location service stopped");
    }
}
