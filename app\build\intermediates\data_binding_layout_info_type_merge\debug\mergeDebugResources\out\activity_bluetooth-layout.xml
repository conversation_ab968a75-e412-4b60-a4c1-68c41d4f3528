<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_bluetooth" modulePackage="com.srthinker.bbnice" filePath="app\src\main\res\layout\activity_bluetooth.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/activity_bluetooth_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="48"/></Target><Target id="@+id/bluetooth_status_container" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="22" startOffset="4" endLine="50" endOffset="55"/></Target><Target id="@+id/tv_bluetooth_status_label" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="40" endOffset="55"/></Target><Target id="@+id/switch_bluetooth" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="42" startOffset="8" endLine="48" endOffset="55"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="52" startOffset="4" endLine="57" endOffset="79"/></Target><Target id="@+id/tv_paired_devices" view="TextView"><Expressions/><location startLine="59" startOffset="4" endLine="67" endOffset="60"/></Target><Target id="@+id/rv_paired_devices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="69" startOffset="4" endLine="75" endOffset="70"/></Target><Target id="@+id/divider2" view="View"><Expressions/><location startLine="77" startOffset="4" endLine="83" endOffset="70"/></Target><Target id="@+id/tv_available_devices" view="TextView"><Expressions/><location startLine="85" startOffset="4" endLine="93" endOffset="61"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="95" startOffset="4" endLine="109" endOffset="59"/></Target><Target id="@+id/rv_available_devices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="102" startOffset="8" endLine="107" endOffset="35"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="111" startOffset="4" endLine="119" endOffset="73"/></Target><Target id="@+id/tv_empty_state" view="TextView"><Expressions/><location startLine="121" startOffset="4" endLine="131" endOffset="73"/></Target></Targets></Layout>