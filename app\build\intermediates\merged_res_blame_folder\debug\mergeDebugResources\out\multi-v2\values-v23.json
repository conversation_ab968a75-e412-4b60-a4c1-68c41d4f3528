{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-v23/values-v23.xml", "map": [{"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\b0472643768c140ca8312de9fe1ab51a\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "3361", "endLines": "54", "endColumns": "12", "endOffsets": "3506"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\a5d503cf4cb712363b2485f01c369623\\transformed\\material-1.10.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,9,13,16,21,25,28,31,34,39,42,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,413,574,784,1104,1341,1548,1755,1958,2290,2492,2757", "endLines": "4,8,12,15,20,24,27,30,33,38,41,45,49", "endColumns": "10,10,10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,408,569,779,1099,1336,1543,1750,1953,2285,2487,2752,3025"}, "to": {"startLines": "55,58,62,66,69,74,78,81,84,87,92,95,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3511,3727,3869,4030,4240,4560,4797,5004,5211,5414,5746,5948,6213", "endLines": "57,61,65,68,73,77,80,83,86,91,94,98,102", "endColumns": "10,10,10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "3722,3864,4025,4235,4555,4792,4999,5206,5409,5741,5943,6208,6481"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\3fe6b1a96e0473b11cddd82eef6049a9\\transformed\\work-runtime-2.8.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "D:\\Workspace\\env\\.gradle\\caches\\8.11.1\\transforms\\a794018af75eb11e6bdc047000971445\\transformed\\appcompat-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,41,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2572,2796,2911,3018,3131", "endLines": "4,5,6,7,21,35,36,37,40,44,45,46,47,51", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2567,2791,2906,3013,3126,3356"}}]}]}