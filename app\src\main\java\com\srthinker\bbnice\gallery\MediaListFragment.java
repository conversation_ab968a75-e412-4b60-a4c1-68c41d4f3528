package com.srthinker.bbnice.gallery;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 媒体列表Fragment，用于显示图片或视频列表
 */
public class MediaListFragment extends Fragment implements MediaAdapter.OnMediaItemClickListener {
    private static final String ARG_MEDIA_TYPE = "media_type";
    public static final int TYPE_ALL = 0;
    public static final int TYPE_IMAGES = 1;
    public static final int TYPE_VIDEOS = 2;

    private int mediaType;
    private RecyclerView recyclerView;
    private TextView tvEmpty;
    private ProgressBar progressBar;
    private MediaAdapter adapter;
    private List<MediaItem> mediaItems = new ArrayList<>();
    private OnMediaItemSelectedListener listener;
    private boolean selectMode = false;

    /**
     * 创建MediaListFragment实例
     * @param mediaType 媒体类型（TYPE_ALL, TYPE_IMAGES, TYPE_VIDEOS）
     * @return Fragment实例
     */
    public static MediaListFragment newInstance(int mediaType) {
        MediaListFragment fragment = new MediaListFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_MEDIA_TYPE, mediaType);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mediaType = getArguments().getInt(ARG_MEDIA_TYPE);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_media_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        recyclerView = view.findViewById(R.id.recycler_view);
        tvEmpty = view.findViewById(R.id.tv_empty);
        progressBar = view.findViewById(R.id.progress_bar);

        // 设置网格布局，每行显示3个项目
        recyclerView.setLayoutManager(new GridLayoutManager(getContext(), 3));

        // 创建适配器
        adapter = new MediaAdapter(getContext(), mediaItems);
        adapter.setOnMediaItemClickListener(this);
        recyclerView.setAdapter(adapter);

        // 加载媒体文件
        loadMediaItems();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof OnMediaItemSelectedListener) {
            listener = (OnMediaItemSelectedListener) context;
        } else {
            throw new RuntimeException(context + " must implement OnMediaItemSelectedListener");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        listener = null;
    }

    /**
     * 加载媒体文件
     */
    private void loadMediaItems() {
        if (getContext() == null) return;

        // 显示加载中状态
        showLoading();

        new Thread(() -> {
            List<MediaItem> items;

            try {
                // 根据类型加载不同的媒体文件
                switch (mediaType) {
                    case TYPE_IMAGES:
                        items = MediaUtils.getImages(getContext());
                        break;
                    case TYPE_VIDEOS:
                        items = MediaUtils.getVideos(getContext());
                        break;
                    case TYPE_ALL:
                    default:
                        items = MediaUtils.getAllMedia(getContext());
                        break;
                }

                // 在UI线程更新界面
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        mediaItems.clear();
                        mediaItems.addAll(items);
                        adapter.notifyDataSetChanged();

                        // 隐藏加载中状态
                        hideLoading();

                        // 显示空视图或列表
                        if (mediaItems.isEmpty()) {
                            showEmptyView();
                        } else {
                            hideEmptyView();
                        }
                    });
                }
            } catch (Exception e) {
                Log.e("MediaListFragment", "Error loading media items: " + e.getMessage());
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        hideLoading();
                        showEmptyView();
                    });
                }
            }
        }).start();
    }

    /**
     * 显示加载中状态
     */
    private void showLoading() {
        if (progressBar != null) {
            progressBar.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏加载中状态
     */
    private void hideLoading() {
        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }
    }

    /**
     * 显示空视图
     */
    private void showEmptyView() {
        if (tvEmpty != null && recyclerView != null) {
            tvEmpty.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
        }
    }

    /**
     * 隐藏空视图
     */
    private void hideEmptyView() {
        if (tvEmpty != null && recyclerView != null) {
            tvEmpty.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 设置选择模式
     * @param selectMode 是否为选择模式
     */
    public void setSelectMode(boolean selectMode) {
        this.selectMode = selectMode;
        adapter.setSelectMode(selectMode);
    }

    /**
     * 获取选择模式状态
     * @return 是否为选择模式
     */
    public boolean isSelectMode() {
        return selectMode;
    }

    /**
     * 获取已选择的媒体项
     * @return 已选择的媒体项列表
     */
    public List<MediaItem> getSelectedItems() {
        List<MediaItem> selectedItems = new ArrayList<>();
        for (MediaItem item : mediaItems) {
            if (item.isSelected()) {
                selectedItems.add(item);
            }
        }
        return selectedItems;
    }

    /**
     * 全选
     */
    public void selectAll() {
        for (MediaItem item : mediaItems) {
            item.setSelected(true);
        }
        adapter.notifyDataSetChanged();
    }

    /**
     * 取消全选
     */
    public void deselectAll() {
        for (MediaItem item : mediaItems) {
            item.setSelected(false);
        }
        adapter.notifyDataSetChanged();
    }

    /**
     * 删除选中的项目
     * @return 是否全部删除成功
     */
    public boolean deleteSelectedItems() {
        List<MediaItem> selectedItems = getSelectedItems();
        if (selectedItems.isEmpty()) {
            return false;
        }

        boolean allSuccess = true;
        List<MediaItem> itemsToRemove = new ArrayList<>();

        for (MediaItem item : selectedItems) {
            boolean success = MediaUtils.deleteMedia(requireContext(), item);
            if (success) {
                itemsToRemove.add(item);
            } else {
                allSuccess = false;
            }
        }

        // 从列表中移除成功删除的项
        if (!itemsToRemove.isEmpty()) {
            mediaItems.removeAll(itemsToRemove);

            // 在UI线程中更新适配器
            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(() -> {
                adapter.notifyDataSetChanged();

                // 如果列表为空，显示空视图
                if (mediaItems.isEmpty()) {
                    tvEmpty.setVisibility(View.VISIBLE);
                    recyclerView.setVisibility(View.GONE);
                }
            });
        }

        return allSuccess;
    }

    @Override
    public void onMediaItemClick(MediaItem item, int position) {
        if (selectMode) {
            // 选择模式下，切换选择状态
            item.setSelected(!item.isSelected());
            adapter.notifyItemChanged(position);
        } else {
            // 非选择模式下，打开详情查看
            if (listener != null) {
                listener.onMediaItemSelected(item);
            }
        }
    }

    /**
     * 媒体项选择监听器接口
     */
    public interface OnMediaItemSelectedListener {
        void onMediaItemSelected(MediaItem item);
    }
}
