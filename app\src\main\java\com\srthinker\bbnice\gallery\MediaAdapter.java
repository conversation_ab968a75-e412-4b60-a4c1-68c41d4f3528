package com.srthinker.bbnice.gallery;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.srthinker.bbnice.R;

import java.util.List;

/**
 * 媒体文件适配器，用于在RecyclerView中显示媒体项
 */
public class MediaAdapter extends RecyclerView.Adapter<MediaAdapter.MediaViewHolder> {
    private final Context context;
    private final List<MediaItem> mediaItems;
    private OnMediaItemClickListener listener;
    private boolean selectMode = false;

    public MediaAdapter(Context context, List<MediaItem> mediaItems) {
        this.context = context;
        this.mediaItems = mediaItems;
    }

    @NonNull
    @Override
    public MediaViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_media, parent, false);
        return new MediaViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MediaViewHolder holder, int position) {
        MediaItem item = mediaItems.get(position);
        
        // 使用Glide加载缩略图
        Glide.with(context)
                .load(item.getUri())
                .apply(new RequestOptions()
                        .centerCrop()
                        .diskCacheStrategy(DiskCacheStrategy.ALL))
                .into(holder.imgThumbnail);
        
        // 设置视频指示器和时长
        if (item.isVideo()) {
            holder.imgVideoIndicator.setVisibility(View.VISIBLE);
            holder.tvDuration.setVisibility(View.VISIBLE);
            holder.tvDuration.setText(item.getFormattedDuration());
        } else {
            holder.imgVideoIndicator.setVisibility(View.GONE);
            holder.tvDuration.setVisibility(View.GONE);
        }
        
        // 设置选择框状态
        if (selectMode) {
            holder.checkbox.setVisibility(View.VISIBLE);
            holder.checkbox.setChecked(item.isSelected());
        } else {
            holder.checkbox.setVisibility(View.GONE);
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onMediaItemClick(item, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mediaItems.size();
    }

    /**
     * 设置选择模式
     * @param selectMode 是否为选择模式
     */
    public void setSelectMode(boolean selectMode) {
        this.selectMode = selectMode;
        notifyDataSetChanged();
    }

    /**
     * 设置媒体项点击监听器
     * @param listener 监听器
     */
    public void setOnMediaItemClickListener(OnMediaItemClickListener listener) {
        this.listener = listener;
    }

    /**
     * 媒体项点击监听器接口
     */
    public interface OnMediaItemClickListener {
        void onMediaItemClick(MediaItem item, int position);
    }

    /**
     * 媒体项ViewHolder
     */
    static class MediaViewHolder extends RecyclerView.ViewHolder {
        ImageView imgThumbnail;
        ImageView imgVideoIndicator;
        TextView tvDuration;
        CheckBox checkbox;

        public MediaViewHolder(@NonNull View itemView) {
            super(itemView);
            imgThumbnail = itemView.findViewById(R.id.img_thumbnail);
            imgVideoIndicator = itemView.findViewById(R.id.img_video_indicator);
            tvDuration = itemView.findViewById(R.id.tv_duration);
            checkbox = itemView.findViewById(R.id.checkbox);
        }
    }
}
