package com.srthinker.bbnice.chat;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.chat.viewmodel.ChatViewModel;
import com.srthinker.bbnice.home.AbstractBaseAIActivity;
import com.srthinker.bbnice.utils.JsonUtils;
import com.srthinker.bbnice.utils.SubtitleUtil;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.type.MediaStreamType;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class ChatActivity extends AbstractBaseAIActivity {
    public static final String TAG = ChatActivity.class.getName();

    private RecyclerView recyclerViewChat;
    private ImageView btnChat;
    private ChatAdapter chatAdapter;
    private boolean preDefinite = true;

    // ViewModel
    private ChatViewModel viewModel;

    // 聊天消息列表
    private final List<ChatMessage> chatMessages = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setContentView(R.layout.activity_chat);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initData() {
        super.initData();

        // 初始化ViewModel
        viewModel = new ViewModelProvider(this).get(ChatViewModel.class);

        // 初始化ViewModel数据
        viewModel.init(roomMessage.systemMessage);

        // 观察聊天消息列表
        viewModel.chatMessages.observe(this, messages -> {
            if (messages != null) {
                // 更新本地消息列表
                chatMessages.clear();
                chatMessages.addAll(messages);

                // 通知适配器数据变化
                chatAdapter.notifyDataSetChanged();

                // 滚动到底部
                if (!chatMessages.isEmpty()) {
                    recyclerViewChat.scrollToPosition(chatMessages.size() - 1);
                }
            }
        });

        // 观察加载状态
        viewModel.isLoading.observe(this, isLoading -> {
            // 可以在这里显示加载指示器
        });

        // 观察加载更多状态
        viewModel.isLoadingMore.observe(this, isLoadingMore -> {
            // 可以在这里显示加载更多指示器
        });

        // 观察错误状态
        viewModel.error.observe(this, errorEvent -> {
            Throwable error = errorEvent.getContentIfNotHandled();
            if (error != null) {
                Toast.makeText(this, getString(R.string.error_unknown) + ": " + error.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void initUI() {
        recyclerViewChat = this.findViewById(R.id.recyclerViewChat);
        btnChat = this.findViewById(R.id.btn_chat);

        chatAdapter = new ChatAdapter(chatMessages);

        // 设置布局管理器
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        recyclerViewChat.setLayoutManager(layoutManager);
        recyclerViewChat.setAdapter(chatAdapter);

        // 设置滚动监听器，用于加载更多历史记录
        recyclerViewChat.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                // 当滚动到顶部时，加载更多历史记录
                if (layoutManager.findFirstVisibleItemPosition() == 0 && dy < 0) {
                    // 使用ViewModel加载更多历史记录
                    viewModel.loadMoreChatHistory();
                }
            }
        });

        btnChat.setOnLongClickListener(v -> {
            if (rtcRoom != null) {
                rtcRoom.publishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
            }
            return true;
        });

        btnChat.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (rtcRoom != null) {
                    rtcRoom.unpublishStream(MediaStreamType.RTC_MEDIA_STREAM_TYPE_AUDIO);
                }
            }
            return false; // 返回false确保不影响OnLongClickListener
        });

        recyclerViewChat.addItemDecoration(new SpaceItemDecoration(16)); // 16px 的间距
    }


    @Override
    protected IRTCRoomEventHandler getIRTCRoomEventHandler() {
        return new IRTCRoomEventHandler() {
            /**
             * 字幕回调
             * @param uid
             * @param message
             */
            @Override
            public void onRoomBinaryMessageReceived(String uid, ByteBuffer message) {
                Log.d(TAG, "onRoomBinaryMessageReceived: uid:" + uid);
                StringBuilder subtitles = new StringBuilder();
                MySubtitleMessage subtitleMessage = SubtitleUtil.unpack(message, subtitles);
                if (subtitleMessage != null) {
                    parseSubtitleData(subtitleMessage);
                }
            }
        };
    }

    @Override
    protected boolean enableAudioCapture() {
        return true;
    }

    @Override
    protected boolean enableVideoCapture() {
        return false;
    }

    // 解析字幕消息
    public void parseSubtitleData(MySubtitleMessage message) {
        try {

            Log.d(TAG,
                    "thread:" + Thread.currentThread().getName() +
                            " \n parseData===>" + message);
            runOnUiThread(() -> {
                if (!message.data.isEmpty()) {
                    MySubtitleMessage.SubtitleData subtitleData = message.data.get(0);
                    boolean sendByMe = !subtitleData.userId.equals("agent"); // ai的userId固定为uu

                    if (preDefinite) {
                        // 创建新消息
                        ChatMessage chatMessage = new ChatMessage(subtitleData.text, sendByMe);

                        // 使用ViewModel保存消息
                        viewModel.saveMessage(chatMessage);

                        // 添加到UI
                        chatAdapter.addMessage(chatMessage);
                    } else {
                        // 更新最后一条消息
                        ChatMessage lastMessage = chatMessages.get(chatMessages.size() - 1);
                        String messageId = lastMessage.getMessageId();
                        lastMessage.setContent(subtitleData.text);

                        // 使用ViewModel更新消息
                        viewModel.updateMessageContent(messageId, subtitleData.text);

                        // 更新UI
                        chatAdapter.updateMessage(lastMessage);
                    }

                    preDefinite = subtitleData.definite;
                    recyclerViewChat.scrollToPosition(chatAdapter.getItemCount() - 1);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error parsing subtitle message: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}