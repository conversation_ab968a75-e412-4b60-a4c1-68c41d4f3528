package com.srthinker.bbnice.work;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.location.Location;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.BatteryManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresPermission;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BBNiceApi;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.location.LocationManager;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 设备状态上报工作器，负责定期上报设备状态信息
 */
public class DeviceStatusWorker extends Worker {
    private static final String TAG = "DeviceStatusWorker";
    private BBNiceApi api;
    private int signalStrength = -1;

    public DeviceStatusWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
        api = BBNiceApi.getInstance(context);
    }

    @NonNull
    @Override
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    public Result doWork() {
        Log.d(TAG, "Starting device status report work");

        Context context = getApplicationContext();
        
        try {
            // 获取电池电量
            int batteryLevel = getBatteryLevel(context);
            
            // 获取网络状态
            String networkStatus = getNetworkStatus(context);
            
            // 获取信号强度
            int signalStrength = getSignalStrength(context);

            // 使用CountDownLatch等待上报完成
            final CountDownLatch latch = new CountDownLatch(1);
            final AtomicBoolean reportSuccess = new AtomicBoolean(false);

            // 获取位置信息
      
            
            // 尝试获取当前位置
            LocationManager locationManager = LocationManager.getInstance(context);
            locationManager.getCurrentLocation(new ApiCallback<LocationData>() {
                @Override
                public void onSuccess(LocationData locationData) {
                    double latitude = locationData.getLatitude();
                    double longitude = locationData.getLongitude();
                    // 获取固件版本
                    String firmwareVersion = Build.VERSION.RELEASE;

                    Log.d(TAG, "Device status: Battery=" + batteryLevel + "%, Network=" + networkStatus +
                            ", Signal=" + signalStrength + ", Location=(" + latitude + "," + longitude +
                            "), Firmware=" + firmwareVersion);

                    // 上报设备状态
                    api.reportDeviceStatus(batteryLevel,
                            networkStatus,
                            signalStrength,
                            latitude,
                            longitude,
                            firmwareVersion, new ApiCallback<BaseResponse>() {
                                @Override
                                public void onSuccess(BaseResponse response) {
                                    Log.d(TAG, "Device status reported successfully");
                                    reportSuccess.set(true);
                                    latch.countDown();
                                }

                                @Override
                                public void onError(ApiError error) {
                                    Log.e(TAG, "Failed to report device status: " + (error != null ? error.getMessage() : "Unknown error"));
                                    latch.countDown();
                                }

                            }
                    );
                }

                @Override
                public void onError(ApiError error) {
                    Log.e(TAG, "onError: 获取位置失败");
                    latch.countDown();
                }
            });

            try {
                // 等待上报完成，最多等待30秒
                boolean completed = latch.await(30, TimeUnit.SECONDS);
                
                if (!completed) {
                    Log.e(TAG, "Device status report timed out");
                    return Result.retry();
                }
                
                if (reportSuccess.get()) {
                    return Result.success();
                } else {
                    return Result.retry();
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "Device status report interrupted: " + e.getMessage());
                Thread.currentThread().interrupt();
                return Result.retry();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during device status report: " + e.getMessage());
            return Result.retry();
        }
    }
    
    /**
     * 获取电池电量
     * @param context 上下文
     * @return 电池电量百分比 (0-100)
     */
    private int getBatteryLevel(Context context) {
        IntentFilter iFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatus = context.registerReceiver(null, iFilter);
        
        int level = -1;
        int scale = -1;
        
        if (batteryStatus != null) {
            level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
            scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
        }
        
        if (level != -1 && scale != -1) {
            return (int) ((level / (float) scale) * 100);
        }
        
        return 50; // 默认值
    }
    
    /**
     * 获取网络状态
     * @param context 上下文
     * @return 网络状态 ("wifi", "4G", "5G", "offline" 等)
     */
    @RequiresPermission(Manifest.permission.READ_PHONE_STATE)
    private String getNetworkStatus(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        
        if (activeNetwork == null || !activeNetwork.isConnected()) {
            return "offline";
        }
        
        if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
            return "wifi";
        }
        
        if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE) {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

            switch (tm.getDataNetworkType()) {
                case TelephonyManager.NETWORK_TYPE_LTE:
                    return "4G";
                case TelephonyManager.NETWORK_TYPE_NR:
                    return "5G";
                default:
                    return "mobile";
            }
        }
        
        return "unknown";
    }
    
    /**
     * 获取信号强度
     * @param context 上下文
     * @return 信号强度 (0-5)
     */
    private int getSignalStrength(Context context) {
        // 简化实现，返回一个0-5的值
        // 实际应用中可以使用PhoneStateListener获取更准确的信号强度
        return 4; // 默认值
    }
}
