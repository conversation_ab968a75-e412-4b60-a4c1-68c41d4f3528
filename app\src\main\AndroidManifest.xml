<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.srthinker.bbnice">

    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- MQTT所需权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 存储权限 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" /> <!-- Android 13及以上版本的媒体权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- WiFi相关权限 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />


    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppCompat.NoActionBar"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".setting.WifiActivity"
            android:exported="false" />
        <activity
            android:name=".setting.SettingActivity"
            android:exported="false" />
        <activity
            android:name=".call.CallActivity"
            android:exported="false" />
        <activity
            android:name=".capture.CaptureActivity"
            android:exported="false" />
        <activity
            android:name=".recognition.RecognitionActivity"
            android:exported="false" />
        <activity
            android:name=".learn.LearnActivity"
            android:exported="false" />
        <activity
            android:name=".home.HomeActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".chat.ChatActivity"
            android:exported="false" />
        <activity
            android:name=".registration.DeviceRegistrationActivity"
            android:exported="false"
            android:label="@string/register" /> <!-- 语言设置Activity -->
        <activity
            android:name=".setting.LanguageSettingsActivity"
            android:exported="false"
            android:label="@string/language_settings" /> <!-- MQTT服务 -->
        <service android:name="org.eclipse.paho.android.service.MqttService" />
        <service
            android:name=".mqtt.MqttService"
            android:enabled="true" /> <!-- 位置服务 -->
        <service
            android:name=".location.LocationService"
            android:enabled="true"
            android:foregroundServiceType="location" /> <!-- Repository测试Activity -->
        <activity
            android:name=".test.RepositoryTestActivity"
            android:exported="true"
            android:label="Repository测试"
            android:theme="@style/Theme.AppCompat.Light.DarkActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity> <!-- 相册Activity -->
        <activity
            android:name=".gallery.GalleryActivity"
            android:exported="false"
            android:label="@string/gallery"
            android:theme="@style/Theme.AppCompat.NoActionBar" />
    </application>

</manifest>