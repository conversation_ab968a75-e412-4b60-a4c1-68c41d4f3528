{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh/values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "94,-1,-1,-1,108,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,98,97,96,-1,-1,124,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,91,-1,-1,-1,-1,-1,-1,-1,103,-1,-1,-1,122,123,-1,-1,-1,-1,-1,99,-1,109,-1,102,-1,-1,-1,-1,92,101,-1,-1,-1,-1,-1,-1,-1,-1,111,-1,110,95,-1,-1,100,-1,-1,-1,-1,-1,-1,-1,-1,93,107,121,118,112,114,115,113,119,116,120,106,117", "startColumns": "4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,-1,-1,-1,-1,-1,4,-1,4,-1,4,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,4,4,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3741,-1,-1,-1,4326,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3921,3872,3826,-1,-1,5215,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3626,-1,-1,-1,-1,-1,-1,-1,4171,-1,-1,-1,5063,5126,-1,-1,-1,-1,-1,3976,-1,4378,-1,4134,-1,-1,-1,-1,3665,4098,-1,-1,-1,-1,-1,-1,-1,-1,4489,-1,4435,3784,-1,-1,4027,-1,-1,-1,-1,-1,-1,-1,-1,3703,4288,5004,4849,4540,4637,4692,4587,4893,4752,4950,4239,4797", "endColumns": "42,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,54,48,45,-1,-1,50,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,38,-1,-1,-1,-1,-1,-1,-1,48,-1,-1,-1,62,88,-1,-1,-1,-1,-1,50,-1,56,-1,36,-1,-1,-1,-1,37,35,-1,-1,-1,-1,-1,-1,-1,-1,50,-1,53,41,-1,-1,70,-1,-1,-1,-1,-1,-1,-1,-1,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "3779,-1,-1,-1,4373,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3971,3916,3867,-1,-1,5261,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3660,-1,-1,-1,-1,-1,-1,-1,4215,-1,-1,-1,5121,5210,-1,-1,-1,-1,-1,4022,-1,4430,-1,4166,-1,-1,-1,-1,3698,4129,-1,-1,-1,-1,-1,-1,-1,-1,4535,-1,4484,3821,-1,-1,4093,-1,-1,-1,-1,-1,-1,-1,-1,3736,4321,5058,4888,4582,4687,4747,4632,4945,4792,4999,4283,4844"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,140,184,221,273,318,365,413,451,489,525,566,607,653,692,739,803,842,892,930,985,1034,1080,1123,1168,1219,1258,1312,1366,1416,1473,1526,1573,1623,1670,1717,1764,1803,1851,1903,1939,1990,2041,2093,2134,2183,2223,2280,2344,2407,2496,2547,2598,2635,2678,2726,2777,2830,2887,2921,2958,3010,3088,3141,3186,3224,3260,3307,3358,3416,3456,3505,3565,3602,3638,3689,3736,3790,3832,3872,3921,3992,4041,4095,4142,4191,4241,4290,4338,4388,4426,4464,4523,4567,4614,4669,4729,4779,4836,4881,4935,4984", "endColumns": "42,41,43,36,51,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,54,48,45,42,44,50,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,50,36,42,47,50,52,56,33,36,51,77,52,44,37,35,46,50,57,39,48,59,36,35,50,46,53,41,39,48,70,48,53,46,48,49,48,47,49,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "93,135,179,216,268,313,360,408,446,484,520,561,602,648,687,734,798,837,887,925,980,1029,1075,1118,1163,1214,1253,1307,1361,1411,1468,1521,1568,1618,1665,1712,1759,1798,1846,1898,1934,1985,2036,2088,2129,2178,2218,2275,2339,2402,2491,2542,2593,2630,2673,2721,2772,2825,2882,2916,2953,3005,3083,3136,3181,3219,3255,3302,3353,3411,3451,3500,3560,3597,3633,3684,3731,3785,3827,3867,3916,3987,4036,4090,4137,4186,4236,4285,4333,4383,4421,4459,4518,4562,4609,4664,4724,4774,4831,4876,4930,4979,5031"}}]}, {"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-zh\\values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "44,2,15,46,33,32,76,8,41,42,43,55,21,56,57,9,65,11,31,45,20,87,84,81,86,85,3,88,83,82,4,75,58,40,22,18,49,12,64,68,66,67,69,26,77,48,54,7,73,74,72,78,52,29,30,27,53,23,13,10,28,14,47,19,35,34,37,36,61,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1647,55,517,1734,1227,1179,3045,248,1529,1565,1606,2096,716,2142,2189,286,2515,361,1136,1689,677,3500,3336,3192,3443,3390,99,3554,3289,3242,146,2997,2253,1493,755,577,1876,399,2475,2680,2565,2629,2737,903,3083,1828,2043,214,2867,2919,2814,3126,1947,1027,1078,940,1994,806,440,325,980,477,1779,628,1321,1274,1425,1375,2403,2305,2353", "endColumns": "41,43,36,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,51,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "1684,94,549,1774,1269,1222,3078,281,1560,1601,1642,2137,750,2184,2248,320,2560,394,1174,1729,711,3549,3385,3237,3495,3438,141,3599,3331,3284,188,3040,2300,1524,801,623,1923,435,2510,2732,2624,2675,2783,935,3121,1871,2091,243,2914,2992,2862,3166,1989,1073,1131,975,2038,861,472,356,1022,512,1823,672,1370,1316,1469,1420,2447,2348,2398"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,97,141,178,223,270,318,356,394,430,471,512,558,597,644,708,747,797,835,878,923,962,1016,1070,1120,1177,1230,1277,1327,1374,1421,1468,1516,1568,1604,1655,1706,1758,1799,1839,1896,1960,2011,2062,2099,2142,2190,2243,2277,2329,2407,2460,2505,2552,2603,2661,2701,2750,2810,2847,2883,2930,2970,3019,3068,3122,3169,3218,3268,3317,3365", "endColumns": "41,43,36,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,51,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "92,136,173,218,265,313,351,389,425,466,507,553,592,639,703,742,792,830,873,918,957,1011,1065,1115,1172,1225,1272,1322,1369,1416,1463,1511,1563,1599,1650,1701,1753,1794,1834,1891,1955,2006,2057,2094,2137,2185,2238,2272,2324,2402,2455,2500,2547,2598,2656,2696,2745,2805,2842,2878,2925,2965,3014,3063,3117,3164,3213,3263,3312,3360,3410"}}]}]}