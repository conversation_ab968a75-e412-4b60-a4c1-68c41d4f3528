{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh/values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,142,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5859,5924,-1,5799,5740,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,64,64,-1,59,58,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5919,5984,-1,5854,5794,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,147,190,232,285,329,374,427,464,531,580,641,701,752,804,849,896,944,985,1085,1190,1265,1329,1378,1430,1490,1553,1617,1678,1739,1792,1846,1901,1956,2032,2110,2188,2272,2340,2413,2495,2572,2654,2719,2794,2868,2937,3013,3119,3230,3311,3383,3444,3490,3539,3597,3657,3722,3792,3848,3907,3972,4024,4074,4135,4184,4242,4318,4365,4410,4448,4486,4522,4563,4604,4650,4689,4736,4800,4839,4895,4948,4998,5044,5107,5145,5200,5249,5295,5338,5383,5429,5474,5520,5568,5623,5669,5708,5788,5838,5889,5943,5988,6027,6081,6135,6185,6242,6295,6342,6392,6439,6486,6533,6572,6635,6683,6744,6796,6832,6883,6934,6986,7027,7076,7116,7173,7237,7300,7389,7440,7487,7538,7575,7621,7664,7709,7766,7822,7870,7927,7987,8048,8103,8168,8247,8329,8388,8447,8497,8546,8589,8628,8685,8733,8785,8844,8895,8948,9005,9058,9092,9141,9178,9230,9308,9361,9430,9475,9513,9549,9596,9647,9810,9861,9919,9958,9998,10047,10107,10144,10189,10234,10281,10327,10363,10414,10461,10518,10572,10622,10687,10752,10802,10862,10921,10958,11000,11040,11089,11128,11199,11248,11295,11349,11396,11445,11495,11534,11580,11629,11677,11727,11765,11802,11850,11898,11943,11997,12040,12090,12128,12187,12231,12278,12333,12393,12443,12500,12545,12599,12648", "endColumns": "45,45,42,41,52,43,44,52,36,66,48,60,59,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,48,57,75,46,44,37,37,35,40,40,45,38,46,63,38,55,52,49,45,62,37,54,48,45,42,44,45,44,45,47,54,45,38,79,49,50,53,44,38,53,53,49,56,52,46,49,46,46,46,38,62,47,60,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,45,42,44,56,55,47,56,59,60,54,64,78,81,58,58,49,48,42,38,56,47,51,58,50,52,56,52,33,48,36,51,77,52,68,44,37,35,46,50,162,50,57,38,39,48,59,36,44,44,46,45,35,50,46,56,53,49,64,64,49,59,58,36,41,39,48,38,70,48,46,53,46,48,49,38,45,48,47,49,37,36,47,47,44,53,42,49,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "96,142,185,227,280,324,369,422,459,526,575,636,696,747,799,844,891,939,980,1080,1185,1260,1324,1373,1425,1485,1548,1612,1673,1734,1787,1841,1896,1951,2027,2105,2183,2267,2335,2408,2490,2567,2649,2714,2789,2863,2932,3008,3114,3225,3306,3378,3439,3485,3534,3592,3652,3717,3787,3843,3902,3967,4019,4069,4130,4179,4237,4313,4360,4405,4443,4481,4517,4558,4599,4645,4684,4731,4795,4834,4890,4943,4993,5039,5102,5140,5195,5244,5290,5333,5378,5424,5469,5515,5563,5618,5664,5703,5783,5833,5884,5938,5983,6022,6076,6130,6180,6237,6290,6337,6387,6434,6481,6528,6567,6630,6678,6739,6791,6827,6878,6929,6981,7022,7071,7111,7168,7232,7295,7384,7435,7482,7533,7570,7616,7659,7704,7761,7817,7865,7922,7982,8043,8098,8163,8242,8324,8383,8442,8492,8541,8584,8623,8680,8728,8780,8839,8890,8943,9000,9053,9087,9136,9173,9225,9303,9356,9425,9470,9508,9544,9591,9642,9805,9856,9914,9953,9993,10042,10102,10139,10184,10229,10276,10322,10358,10409,10456,10513,10567,10617,10682,10747,10797,10857,10916,10953,10995,11035,11084,11123,11194,11243,11290,11344,11391,11440,11490,11529,11575,11624,11672,11722,11760,11797,11845,11893,11938,11992,12035,12085,12123,12182,12226,12273,12328,12388,12438,12495,12540,12594,12643,12695"}}]}, {"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-zh\\values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "251,159,98,46,258,2,256,257,16,166,142,144,143,173,112,48,35,34,172,212,214,210,191,187,178,182,183,196,195,197,200,188,179,189,218,223,221,222,205,206,224,216,219,203,220,217,204,209,213,215,211,207,192,185,180,181,184,193,201,190,177,202,171,186,208,141,139,145,140,157,78,8,43,44,45,57,23,58,59,9,261,260,67,240,241,11,102,101,100,33,47,253,252,255,254,233,165,134,167,194,128,232,164,22,91,88,85,90,89,3,92,87,86,4,95,168,77,146,60,42,24,20,51,12,107,66,70,68,126,127,69,131,71,28,156,79,229,231,230,228,247,248,245,234,242,244,243,227,246,154,153,152,82,236,50,235,175,103,56,113,160,7,174,106,75,76,74,262,80,96,105,54,265,266,31,32,14,29,55,25,13,158,237,239,238,10,115,30,176,114,135,136,138,99,15,49,81,104,21,161,37,36,39,38,17,259,63,61,62,97,150,149,151,163,155,162,137,111,125,122,116,118,119,117,123,120,124,110,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12052,6686,3897,1725,12381,55,12283,12328,556,7011,5894,6003,5943,7338,4482,1812,1305,1257,7297,9620,9826,9464,8320,8106,7613,7827,7887,8621,8560,8685,8792,8155,7665,8209,10193,10588,10426,10504,9114,9182,10666,10042,10269,8980,10351,10119,9045,9388,9720,9931,9539,9255,8384,8010,7720,7769,7950,8445,8845,8264,7554,8915,7245,8056,9327,5845,5740,6064,5798,6596,3123,248,1607,1643,1684,2174,794,2220,2267,286,12533,12480,2593,11453,11499,361,4077,4028,3982,1214,1767,12143,12098,12237,12189,11096,6965,5514,7078,8510,5371,11042,6920,755,3656,3492,3348,3599,3546,99,3710,3445,3398,146,3782,7158,3075,6140,2331,1571,833,655,1954,399,4327,2553,2758,2643,5219,5282,2707,5445,2815,981,6550,3161,10884,10985,10929,10836,11908,11965,11788,11151,11562,11709,11627,10777,11849,6446,6397,6354,3288,11258,1906,11206,7438,4132,2121,4534,6732,214,7389,4290,2945,2997,2892,12589,3204,3821,4254,2025,12690,12741,1105,1156,477,1018,2072,884,440,6641,11315,11406,11360,325,4645,1058,7497,4591,5553,5603,5703,3940,516,1857,3249,4183,706,6785,1399,1352,1503,1453,593,12434,2481,2383,2431,3859,6269,6221,6306,6875,6496,6832,5653,4444,5160,5005,4696,4793,4848,4743,5049,4908,5106,4395,4953", "endColumns": "45,45,42,41,52,43,44,52,36,66,48,60,59,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,48,57,75,46,44,37,37,35,40,40,45,38,46,63,38,55,52,49,45,62,37,54,48,45,42,44,45,44,45,47,54,45,38,79,49,50,53,44,38,53,53,49,56,52,46,49,46,46,46,38,62,47,60,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,45,42,44,56,55,47,56,59,60,54,64,78,81,58,58,49,48,42,38,56,47,51,58,50,52,56,52,33,48,36,51,77,52,68,44,37,35,46,50,162,50,57,38,39,48,59,36,44,44,46,45,35,50,46,56,53,49,49,36,41,39,48,38,70,48,46,53,46,48,49,38,45,48,47,49,37,36,47,47,44,53,42,49,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "12093,6727,3935,1762,12429,94,12323,12376,588,7073,5938,6059,5998,7384,4529,1852,1347,1300,7333,9715,9926,9534,8379,8150,7660,7882,7945,8680,8616,8741,8840,8204,7715,8259,10264,10661,10499,10583,9177,9250,10743,10114,10346,9040,10421,10188,9109,9459,9821,10037,9615,9322,8440,8051,7764,7822,8005,8505,8910,8315,7608,8975,7292,8101,9383,5889,5793,6135,5840,6636,3156,281,1638,1679,1720,2215,828,2262,2326,320,12584,12528,2638,11494,11557,394,4127,4072,4023,1252,1807,12184,12138,12278,12232,11146,7006,5548,7153,8555,5417,11091,6960,789,3705,3541,3393,3651,3594,141,3755,3487,3440,188,3816,7216,3118,6196,2378,1602,879,701,2001,435,4371,2588,2810,2702,5277,5366,2753,5487,2861,1013,6591,3199,10924,11037,10980,10879,11960,12020,11844,11201,11622,11783,11704,10831,11903,6491,6441,6392,3322,11310,1949,11253,7492,4178,2169,4586,6780,243,7433,4322,2992,3070,2940,12653,3244,3854,4285,2067,12736,12899,1151,1209,511,1053,2116,939,472,6681,11355,11448,11401,356,4691,1100,7549,4640,5598,5648,5735,3977,551,1901,3283,4249,750,6827,1448,1394,1547,1498,627,12475,2525,2426,2476,3892,6301,6264,6349,6915,6545,6870,5698,4477,5214,5044,4738,4843,4903,4788,5101,4948,5155,4439,5000"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,147,190,232,285,329,374,427,464,531,580,641,701,752,804,849,896,944,985,1085,1190,1265,1329,1378,1430,1490,1553,1617,1678,1739,1792,1846,1901,1956,2032,2110,2188,2272,2340,2413,2495,2572,2654,2719,2794,2868,2937,3013,3119,3230,3311,3383,3444,3490,3539,3597,3657,3722,3792,3848,3907,3972,4024,4074,4135,4184,4242,4318,4365,4410,4448,4486,4522,4563,4604,4650,4689,4736,4800,4839,4895,4948,4998,5044,5107,5145,5200,5249,5295,5338,5383,5429,5474,5520,5568,5623,5669,5708,5788,5838,5889,5943,5988,6027,6081,6135,6185,6242,6295,6342,6392,6439,6486,6533,6572,6635,6683,6744,6796,6832,6883,6934,6986,7027,7076,7116,7173,7237,7300,7389,7440,7487,7538,7575,7621,7664,7709,7766,7822,7870,7927,7987,8048,8103,8168,8247,8329,8388,8447,8497,8546,8589,8628,8685,8733,8785,8844,8895,8948,9005,9058,9092,9141,9178,9230,9308,9361,9430,9475,9513,9549,9596,9647,9810,9861,9919,9958,9998,10047,10107,10144,10189,10234,10281,10327,10363,10414,10461,10518,10572,10622,10672,10709,10751,10791,10840,10879,10950,10999,11046,11100,11147,11196,11246,11285,11331,11380,11428,11478,11516,11553,11601,11649,11694,11748,11791,11841,11879,11938,11982,12029,12084,12144,12194,12251,12296,12350,12399", "endColumns": "45,45,42,41,52,43,44,52,36,66,48,60,59,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,48,57,75,46,44,37,37,35,40,40,45,38,46,63,38,55,52,49,45,62,37,54,48,45,42,44,45,44,45,47,54,45,38,79,49,50,53,44,38,53,53,49,56,52,46,49,46,46,46,38,62,47,60,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,45,42,44,56,55,47,56,59,60,54,64,78,81,58,58,49,48,42,38,56,47,51,58,50,52,56,52,33,48,36,51,77,52,68,44,37,35,46,50,162,50,57,38,39,48,59,36,44,44,46,45,35,50,46,56,53,49,49,36,41,39,48,38,70,48,46,53,46,48,49,38,45,48,47,49,37,36,47,47,44,53,42,49,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "96,142,185,227,280,324,369,422,459,526,575,636,696,747,799,844,891,939,980,1080,1185,1260,1324,1373,1425,1485,1548,1612,1673,1734,1787,1841,1896,1951,2027,2105,2183,2267,2335,2408,2490,2567,2649,2714,2789,2863,2932,3008,3114,3225,3306,3378,3439,3485,3534,3592,3652,3717,3787,3843,3902,3967,4019,4069,4130,4179,4237,4313,4360,4405,4443,4481,4517,4558,4599,4645,4684,4731,4795,4834,4890,4943,4993,5039,5102,5140,5195,5244,5290,5333,5378,5424,5469,5515,5563,5618,5664,5703,5783,5833,5884,5938,5983,6022,6076,6130,6180,6237,6290,6337,6387,6434,6481,6528,6567,6630,6678,6739,6791,6827,6878,6929,6981,7022,7071,7111,7168,7232,7295,7384,7435,7482,7533,7570,7616,7659,7704,7761,7817,7865,7922,7982,8043,8098,8163,8242,8324,8383,8442,8492,8541,8584,8623,8680,8728,8780,8839,8890,8943,9000,9053,9087,9136,9173,9225,9303,9356,9425,9470,9508,9544,9591,9642,9805,9856,9914,9953,9993,10042,10102,10139,10184,10229,10276,10322,10358,10409,10456,10513,10567,10617,10667,10704,10746,10786,10835,10874,10945,10994,11041,11095,11142,11191,11241,11280,11326,11375,11423,11473,11511,11548,11596,11644,11689,11743,11786,11836,11874,11933,11977,12024,12079,12139,12189,12246,12291,12345,12394,12446"}}]}]}