{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh/values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,142,144,143,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,139,145,140,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,146,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,5894,6003,5943,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5845,5740,6064,5798,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6140,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,48,60,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,48,57,75,46,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,60,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,5938,6059,5998,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5889,5793,6135,5840,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6196,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,144,186,230,267,334,383,444,504,555,607,652,699,747,788,888,993,1068,1132,1181,1233,1293,1356,1420,1481,1542,1595,1649,1704,1759,1835,1913,1991,2075,2143,2216,2298,2375,2457,2522,2597,2671,2740,2816,2922,3033,3114,3186,3247,3293,3342,3400,3460,3525,3595,3651,3710,3775,3827,3877,3938,3987,4045,4121,4168,4213,4251,4289,4325,4366,4407,4453,4492,4539,4603,4642,4692,4738,4801,4839,4894,4943,4989,5032,5077,5132,5178,5217,5297,5347,5398,5452,5497,5536,5590,5644,5694,5751,5804,5851,5901,5948,5995,6042,6081,6144,6192,6253,6305,6341,6392,6443,6495,6536,6585,6625,6682,6746,6809,6898,6949,6996,7047,7084,7130,7173,7218,7275,7331,7379,7436,7496,7557,7612,7677,7756,7838,7897,7956,8006,8055,8098,8137,8194,8242,8294,8353,8404,8457,8514,8567,8601,8650,8687,8739,8817,8870,8915,8953,8989,9036,9087,9145,9184,9224,9273,9333,9370,9415,9460,9507,9553,9589,9640,9687,9744,9798,9848,9898,9935,9977,10017,10066,10105,10176,10225,10272,10326,10373,10422,10472,10511,10560,10608,10658,10696,10733,10781,10829,10874,10928,10971,11021,11059,11118,11162,11209,11264,11324,11374,11431,11476,11530,11579", "endColumns": "45,42,41,43,36,66,48,60,59,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,48,57,75,46,44,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,45,38,79,49,50,53,44,38,53,53,49,56,52,46,49,46,46,46,38,62,47,60,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,45,42,44,56,55,47,56,59,60,54,64,78,81,58,58,49,48,42,38,56,47,51,58,50,52,56,52,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,44,46,45,35,50,46,56,53,49,49,36,41,39,48,38,70,48,46,53,46,48,49,38,48,47,49,37,36,47,47,44,53,42,49,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "96,139,181,225,262,329,378,439,499,550,602,647,694,742,783,883,988,1063,1127,1176,1228,1288,1351,1415,1476,1537,1590,1644,1699,1754,1830,1908,1986,2070,2138,2211,2293,2370,2452,2517,2592,2666,2735,2811,2917,3028,3109,3181,3242,3288,3337,3395,3455,3520,3590,3646,3705,3770,3822,3872,3933,3982,4040,4116,4163,4208,4246,4284,4320,4361,4402,4448,4487,4534,4598,4637,4687,4733,4796,4834,4889,4938,4984,5027,5072,5127,5173,5212,5292,5342,5393,5447,5492,5531,5585,5639,5689,5746,5799,5846,5896,5943,5990,6037,6076,6139,6187,6248,6300,6336,6387,6438,6490,6531,6580,6620,6677,6741,6804,6893,6944,6991,7042,7079,7125,7168,7213,7270,7326,7374,7431,7491,7552,7607,7672,7751,7833,7892,7951,8001,8050,8093,8132,8189,8237,8289,8348,8399,8452,8509,8562,8596,8645,8682,8734,8812,8865,8910,8948,8984,9031,9082,9140,9179,9219,9268,9328,9365,9410,9455,9502,9548,9584,9635,9682,9739,9793,9843,9893,9930,9972,10012,10061,10100,10171,10220,10267,10321,10368,10417,10467,10506,10555,10603,10653,10691,10728,10776,10824,10869,10923,10966,11016,11054,11113,11157,11204,11259,11319,11369,11426,11471,11525,11574,11626"}}]}, {"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-zh\\values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "144,98,46,2,16,151,158,112,48,35,34,157,197,199,195,176,172,163,167,168,181,180,182,185,173,164,174,203,208,206,207,190,191,209,201,204,188,205,202,189,194,198,200,196,192,177,170,165,166,169,178,186,175,162,187,156,171,193,142,78,8,43,44,45,57,23,58,59,9,67,225,226,11,102,101,100,33,47,218,150,152,179,128,217,149,22,91,88,85,90,89,3,92,87,86,4,95,153,77,60,42,24,20,51,12,107,66,70,68,126,127,69,131,71,28,141,79,214,216,215,213,232,233,230,219,227,229,228,212,231,139,138,137,82,221,50,220,160,103,56,113,145,7,159,106,75,76,74,80,96,105,54,31,32,14,29,55,25,13,143,222,224,223,10,115,30,161,114,99,15,49,81,104,21,146,37,36,39,38,17,63,61,62,97,135,134,136,148,140,147,111,125,122,116,118,119,117,123,120,124,110,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5977,3897,1725,55,556,6302,6629,4482,1812,1305,1257,6588,8911,9117,8755,7611,7397,6904,7118,7178,7912,7851,7976,8083,7446,6956,7500,9484,9879,9717,9795,8405,8473,9957,9333,9560,8271,9642,9410,8336,8679,9011,9222,8830,8546,7675,7301,7011,7060,7241,7736,8136,7555,6845,8206,6536,7347,8618,5887,3123,248,1607,1643,1684,2174,794,2220,2267,286,2593,10744,10790,361,4077,4028,3982,1214,1767,10387,6256,6369,7801,5371,10333,6211,755,3656,3492,3348,3599,3546,99,3710,3445,3398,146,3782,6449,3075,2331,1571,833,655,1954,399,4327,2553,2758,2643,5219,5282,2707,5445,2815,981,5841,3161,10175,10276,10220,10127,11199,11256,11079,10442,10853,11000,10918,10068,11140,5737,5688,5645,3288,10549,1906,10497,6729,4132,2121,4534,6023,214,6680,4290,2945,2997,2892,3204,3821,4254,2025,1105,1156,477,1018,2072,884,440,5932,10606,10697,10651,325,4645,1058,6788,4591,3940,516,1857,3249,4183,706,6076,1399,1352,1503,1453,593,2481,2383,2431,3859,5560,5512,5597,6166,5787,6123,4444,5160,5005,4696,4793,4848,4743,5049,4908,5106,4395,4953", "endColumns": "45,42,41,43,36,66,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,44,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,45,79,49,50,53,44,38,53,53,49,56,52,46,49,46,46,46,38,62,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,45,42,44,56,55,47,56,59,60,54,64,78,81,58,58,49,48,42,38,56,47,51,58,50,52,56,52,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,44,46,45,35,50,46,56,53,41,39,48,38,70,48,46,53,46,48,49,38,48,47,49,37,36,47,47,44,53,42,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "6018,3935,1762,94,588,6364,6675,4529,1852,1347,1300,6624,9006,9217,8825,7670,7441,6951,7173,7236,7971,7907,8032,8131,7495,7006,7550,9555,9952,9790,9874,8468,8541,10034,9405,9637,8331,9712,9479,8400,8750,9112,9328,8906,8613,7731,7342,7055,7113,7296,7796,8201,7606,6899,8266,6583,7392,8674,5927,3156,281,1638,1679,1720,2215,828,2262,2326,320,2638,10785,10848,394,4127,4072,4023,1252,1807,10437,6297,6444,7846,5417,10382,6251,789,3705,3541,3393,3651,3594,141,3755,3487,3440,188,3816,6507,3118,2378,1602,879,701,2001,435,4371,2588,2810,2702,5277,5366,2753,5487,2861,1013,5882,3199,10215,10328,10271,10170,11251,11311,11135,10492,10913,11074,10995,10122,11194,5782,5732,5683,3322,10601,1949,10544,6783,4178,2169,4586,6071,243,6724,4322,2992,3070,2940,3244,3854,4285,2067,1151,1209,511,1053,2116,939,472,5972,10646,10739,10692,356,4691,1100,6840,4640,3977,551,1901,3283,4249,750,6118,1448,1394,1547,1498,627,2525,2426,2476,3892,5592,5555,5640,6206,5836,6161,4477,5214,5044,4738,4843,4903,4788,5101,4948,5155,4439,5000"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,144,186,230,267,334,385,437,482,529,577,618,718,823,898,962,1011,1063,1123,1186,1250,1311,1372,1425,1479,1534,1589,1665,1743,1821,1905,1973,2046,2128,2205,2287,2352,2427,2501,2570,2646,2752,2863,2944,3016,3077,3123,3172,3230,3290,3355,3425,3481,3540,3605,3657,3707,3768,3813,3851,3889,3925,3966,4007,4053,4092,4139,4203,4242,4292,4338,4401,4439,4494,4543,4589,4632,4677,4732,4778,4858,4908,4959,5013,5058,5097,5151,5205,5255,5312,5365,5412,5462,5509,5556,5603,5642,5705,5753,5805,5841,5892,5943,5995,6036,6085,6125,6182,6246,6309,6398,6449,6496,6547,6584,6630,6673,6718,6775,6831,6879,6936,6996,7057,7112,7177,7256,7338,7397,7456,7506,7555,7598,7637,7694,7742,7794,7853,7904,7957,8014,8067,8101,8150,8187,8239,8317,8370,8415,8453,8489,8536,8587,8645,8684,8724,8773,8833,8870,8915,8960,9007,9053,9089,9140,9187,9244,9298,9340,9380,9429,9468,9539,9588,9635,9689,9736,9785,9835,9874,9923,9971,10021,10059,10096,10144,10192,10237,10291,10334,10372,10431,10475,10522,10577,10637,10687,10744,10789,10843,10892", "endColumns": "45,42,41,43,36,66,50,51,44,46,47,40,99,104,74,63,48,51,59,62,63,60,60,52,53,54,54,75,77,77,83,67,72,81,76,81,64,74,73,68,75,105,110,80,71,60,45,48,57,59,64,69,55,58,64,51,49,60,44,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,45,79,49,50,53,44,38,53,53,49,56,52,46,49,46,46,46,38,62,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,45,42,44,56,55,47,56,59,60,54,64,78,81,58,58,49,48,42,38,56,47,51,58,50,52,56,52,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,44,46,45,35,50,46,56,53,41,39,48,38,70,48,46,53,46,48,49,38,48,47,49,37,36,47,47,44,53,42,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "96,139,181,225,262,329,380,432,477,524,572,613,713,818,893,957,1006,1058,1118,1181,1245,1306,1367,1420,1474,1529,1584,1660,1738,1816,1900,1968,2041,2123,2200,2282,2347,2422,2496,2565,2641,2747,2858,2939,3011,3072,3118,3167,3225,3285,3350,3420,3476,3535,3600,3652,3702,3763,3808,3846,3884,3920,3961,4002,4048,4087,4134,4198,4237,4287,4333,4396,4434,4489,4538,4584,4627,4672,4727,4773,4853,4903,4954,5008,5053,5092,5146,5200,5250,5307,5360,5407,5457,5504,5551,5598,5637,5700,5748,5800,5836,5887,5938,5990,6031,6080,6120,6177,6241,6304,6393,6444,6491,6542,6579,6625,6668,6713,6770,6826,6874,6931,6991,7052,7107,7172,7251,7333,7392,7451,7501,7550,7593,7632,7689,7737,7789,7848,7899,7952,8009,8062,8096,8145,8182,8234,8312,8365,8410,8448,8484,8531,8582,8640,8679,8719,8768,8828,8865,8910,8955,9002,9048,9084,9135,9182,9239,9293,9335,9375,9424,9463,9534,9583,9630,9684,9731,9780,9830,9869,9918,9966,10016,10054,10091,10139,10187,10232,10286,10329,10367,10426,10470,10517,10572,10632,10682,10739,10784,10838,10887,10939"}}]}]}