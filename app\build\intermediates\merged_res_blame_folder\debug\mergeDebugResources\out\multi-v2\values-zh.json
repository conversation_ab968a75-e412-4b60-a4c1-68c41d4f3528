{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh/values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "98,46,2,16,136,112,48,35,34,135,154,150,141,145,146,159,158,160,151,142,152,155,148,143,144,147,156,153,140,134,149,78,8,43,44,45,57,23,58,59,9,67,176,177,11,102,101,100,33,47,169,157,128,168,22,91,88,85,90,89,3,92,87,86,4,95,77,60,42,24,20,51,12,107,66,70,68,126,127,69,131,71,28,79,165,167,166,164,183,184,181,170,178,180,179,163,182,82,172,50,171,138,103,56,113,7,137,106,75,76,74,80,96,105,54,31,32,14,29,55,25,13,173,175,174,10,115,30,139,114,99,15,49,81,104,21,37,36,39,38,17,63,61,62,97,111,125,122,116,118,119,117,123,120,124,110,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3897,1725,55,556,5609,4482,1812,1305,1257,5568,6591,6377,5884,6098,6158,6892,6831,6956,6426,5936,6480,6655,6281,5991,6040,6221,6716,6535,5825,5516,6327,3123,248,1607,1643,1684,2174,794,2220,2267,286,2593,7722,7768,361,4077,4028,3982,1214,1767,7365,6781,5371,7311,755,3656,3492,3348,3599,3546,99,3710,3445,3398,146,3782,3075,2331,1571,833,655,1954,399,4327,2553,2758,2643,5219,5282,2707,5445,2815,981,3161,7153,7254,7198,7105,8177,8234,8057,7420,7831,7978,7896,7046,8118,3288,7527,1906,7475,5709,4132,2121,4534,214,5660,4290,2945,2997,2892,3204,3821,4254,2025,1105,1156,477,1018,2072,884,440,7584,7675,7629,325,4645,1058,5768,4591,3940,516,1857,3249,4183,706,1399,1352,1503,1453,593,2481,2383,2431,3859,4444,5160,5005,4696,4793,4848,4743,5049,4908,5106,4395,4953", "endColumns": "42,41,43,36,50,51,44,46,47,40,63,48,51,59,62,63,60,60,53,54,54,60,45,48,57,59,64,55,58,51,49,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,49,50,53,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,42,44,56,55,47,56,59,60,54,64,78,81,58,58,38,56,47,51,58,50,52,56,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,46,45,35,50,46,56,53,41,39,48,38,70,48,53,46,48,49,38,48,47,49,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "3935,1762,94,588,5655,4529,1852,1347,1300,5604,6650,6421,5931,6153,6216,6951,6887,7012,6475,5986,6530,6711,6322,6035,6093,6276,6776,6586,5879,5563,6372,3156,281,1638,1679,1720,2215,828,2262,2326,320,2638,7763,7826,394,4127,4072,4023,1252,1807,7415,6826,5417,7360,789,3705,3541,3393,3651,3594,141,3755,3487,3440,188,3816,3118,2378,1602,879,701,2001,435,4371,2588,2810,2702,5277,5366,2753,5487,2861,1013,3199,7193,7306,7249,7148,8229,8289,8113,7470,7891,8052,7973,7100,8172,3322,7579,1949,7522,5763,4178,2169,4586,243,5704,4322,2992,3070,2940,3244,3854,4285,2067,1151,1209,511,1053,2116,939,472,7624,7717,7670,356,4691,1100,5820,4640,3977,551,1901,3283,4249,750,1448,1394,1547,1498,627,2525,2426,2476,3892,4477,5214,5044,4738,4843,4903,4788,5101,4948,5155,4439,5000"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,140,184,221,272,324,369,416,464,505,569,618,670,730,793,857,918,979,1033,1088,1143,1204,1250,1299,1357,1417,1482,1538,1597,1649,1699,1737,1775,1811,1852,1893,1939,1978,2025,2089,2128,2178,2224,2287,2325,2380,2429,2475,2518,2563,2618,2668,2719,2773,2812,2866,2920,2970,3027,3080,3127,3177,3224,3271,3318,3357,3405,3457,3493,3544,3595,3647,3688,3737,3777,3834,3898,3961,4050,4101,4148,4199,4236,4279,4324,4381,4437,4485,4542,4602,4663,4718,4783,4862,4944,5003,5062,5101,5158,5206,5258,5317,5368,5421,5478,5512,5561,5598,5650,5728,5781,5826,5864,5900,5947,5998,6056,6095,6135,6184,6244,6281,6326,6373,6419,6455,6506,6553,6610,6664,6706,6746,6795,6834,6905,6954,7008,7055,7104,7154,7193,7242,7290,7340,7378,7416,7475,7519,7566,7621,7681,7731,7788,7833,7887,7936", "endColumns": "42,41,43,36,50,51,44,46,47,40,63,48,51,59,62,63,60,60,53,54,54,60,45,48,57,59,64,55,58,51,49,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,49,50,53,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,42,44,56,55,47,56,59,60,54,64,78,81,58,58,38,56,47,51,58,50,52,56,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,46,45,35,50,46,56,53,41,39,48,38,70,48,53,46,48,49,38,48,47,49,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "93,135,179,216,267,319,364,411,459,500,564,613,665,725,788,852,913,974,1028,1083,1138,1199,1245,1294,1352,1412,1477,1533,1592,1644,1694,1732,1770,1806,1847,1888,1934,1973,2020,2084,2123,2173,2219,2282,2320,2375,2424,2470,2513,2558,2613,2663,2714,2768,2807,2861,2915,2965,3022,3075,3122,3172,3219,3266,3313,3352,3400,3452,3488,3539,3590,3642,3683,3732,3772,3829,3893,3956,4045,4096,4143,4194,4231,4274,4319,4376,4432,4480,4537,4597,4658,4713,4778,4857,4939,4998,5057,5096,5153,5201,5253,5312,5363,5416,5473,5507,5556,5593,5645,5723,5776,5821,5859,5895,5942,5993,6051,6090,6130,6179,6239,6276,6321,6368,6414,6450,6501,6548,6605,6659,6701,6741,6790,6829,6900,6949,7003,7050,7099,7149,7188,7237,7285,7335,7373,7411,7470,7514,7561,7616,7676,7726,7783,7828,7882,7931,7983"}}]}]}