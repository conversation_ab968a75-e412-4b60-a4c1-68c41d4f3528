{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh/values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "44,2,15,46,33,32,76,8,41,42,43,55,21,56,57,9,65,11,31,45,20,87,84,81,86,85,3,88,83,82,4,75,58,40,22,18,49,12,64,68,66,67,69,26,77,48,54,7,73,74,72,78,52,29,30,27,53,23,13,10,28,14,47,19,35,34,37,36,61,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1647,55,517,1734,1227,1179,3045,248,1529,1565,1606,2096,716,2142,2189,286,2515,361,1136,1689,677,3500,3336,3192,3443,3390,99,3554,3289,3242,146,2997,2253,1493,755,577,1876,399,2475,2680,2565,2629,2737,903,3083,1828,2043,214,2867,2919,2814,3126,1947,1027,1078,940,1994,806,440,325,980,477,1779,628,1321,1274,1425,1375,2403,2305,2353", "endColumns": "41,43,36,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,51,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "1684,94,549,1774,1269,1222,3078,281,1560,1601,1642,2137,750,2184,2248,320,2560,394,1174,1729,711,3549,3385,3237,3495,3438,141,3599,3331,3284,188,3040,2300,1524,801,623,1923,435,2510,2732,2624,2675,2783,935,3121,1871,2091,243,2914,2992,2862,3166,1989,1073,1131,975,2038,861,472,356,1022,512,1823,672,1370,1316,1469,1420,2447,2348,2398"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,97,141,178,223,270,318,356,394,430,471,512,558,597,644,708,747,797,835,878,923,962,1016,1070,1120,1177,1230,1277,1327,1374,1421,1468,1516,1568,1604,1655,1706,1758,1799,1839,1896,1960,2011,2062,2099,2142,2190,2243,2277,2329,2407,2460,2505,2552,2603,2661,2701,2750,2810,2847,2883,2930,2970,3019,3068,3122,3169,3218,3268,3317,3365", "endColumns": "41,43,36,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,51,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "92,136,173,218,265,313,351,389,425,466,507,553,592,639,703,742,792,830,873,918,957,1011,1065,1115,1172,1225,1272,1322,1369,1416,1463,1511,1563,1599,1650,1701,1753,1794,1834,1891,1955,2006,2057,2094,2137,2185,2238,2272,2324,2402,2455,2500,2547,2598,2656,2696,2745,2805,2842,2878,2925,2965,3014,3063,3117,3164,3213,3263,3312,3360,3410"}}]}]}