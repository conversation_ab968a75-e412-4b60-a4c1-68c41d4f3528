{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-zh/values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,134,-1,-1,-1,-1,133,152,148,139,143,144,157,156,158,149,140,150,153,146,141,142,145,154,151,138,132,147,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,174,175,-1,-1,-1,-1,-1,-1,167,155,-1,166,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,129,-1,-1,-1,163,165,164,162,181,182,179,168,176,178,177,161,180,170,-1,169,136,-1,-1,-1,-1,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,14,-1,-1,-1,-1,171,173,172,-1,-1,-1,137,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,17,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,4,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,5531,-1,-1,-1,-1,5490,6513,6299,5806,6020,6080,6814,6753,6878,6348,5858,6402,6577,6203,5913,5962,6143,6638,6457,5747,5438,6249,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,7644,7690,-1,-1,-1,-1,-1,-1,7287,6703,-1,7233,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5367,-1,-1,-1,7075,7176,7120,7027,8099,8156,7979,7342,7753,7900,7818,6968,8040,7449,-1,7397,5631,-1,-1,-1,-1,5582,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,477,-1,-1,-1,-1,7506,7597,7551,-1,-1,-1,5690,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,593,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,50,-1,-1,-1,-1,40,63,48,51,59,62,63,60,60,53,54,54,60,45,48,57,59,64,55,58,51,49,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,45,62,-1,-1,-1,-1,-1,-1,54,49,-1,53,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,46,-1,-1,-1,44,56,55,47,56,59,60,54,64,78,81,58,58,56,-1,51,58,-1,-1,-1,-1,48,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,38,-1,-1,-1,-1,44,46,45,-1,-1,-1,56,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,38,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,5577,-1,-1,-1,-1,5526,6572,6343,5853,6075,6138,6873,6809,6934,6397,5908,6452,6633,6244,5957,6015,6198,6698,6508,5801,5485,6294,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,7685,7748,-1,-1,-1,-1,-1,-1,7337,6748,-1,7282,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5409,-1,-1,-1,7115,7228,7171,7070,8151,8211,8035,7392,7813,7974,7895,7022,8094,7501,-1,7444,5685,-1,-1,-1,-1,5626,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,511,-1,-1,-1,-1,7546,7639,7592,-1,-1,-1,5742,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,627,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,140,184,221,272,324,369,416,464,505,569,618,670,730,793,857,918,979,1033,1088,1143,1204,1250,1299,1357,1417,1482,1538,1597,1649,1699,1737,1775,1811,1852,1893,1939,1978,2025,2089,2128,2178,2224,2287,2325,2380,2429,2475,2518,2563,2618,2668,2719,2773,2812,2866,2920,2970,3027,3080,3127,3177,3224,3271,3318,3357,3405,3457,3493,3544,3595,3647,3688,3737,3777,3834,3898,3961,4050,4101,4148,4199,4236,4279,4324,4381,4437,4485,4542,4602,4663,4718,4783,4862,4944,5003,5062,5119,5167,5219,5278,5329,5382,5439,5473,5522,5559,5611,5689,5742,5787,5825,5861,5908,5959,6017,6056,6096,6145,6205,6242,6287,6334,6380,6416,6467,6514,6571,6625,6667,6707,6756,6827,6876,6930,6977,7026,7076,7115,7164,7212,7262,7300,7338,7397,7441,7488,7543,7603,7653,7710,7755,7809,7858", "endColumns": "42,41,43,36,50,51,44,46,47,40,63,48,51,59,62,63,60,60,53,54,54,60,45,48,57,59,64,55,58,51,49,37,37,35,40,40,45,38,46,63,38,49,45,62,37,54,48,45,42,44,54,49,50,53,38,53,53,49,56,52,46,49,46,46,46,38,47,51,35,50,50,51,40,48,39,56,63,62,88,50,46,50,36,42,44,56,55,47,56,59,60,54,64,78,81,58,58,56,47,51,58,50,52,56,33,48,36,51,77,52,44,37,35,46,50,57,38,39,48,59,36,44,46,45,35,50,46,56,53,41,39,48,70,48,53,46,48,49,38,48,47,49,37,37,58,43,46,54,59,49,56,44,53,48,51", "endOffsets": "93,135,179,216,267,319,364,411,459,500,564,613,665,725,788,852,913,974,1028,1083,1138,1199,1245,1294,1352,1412,1477,1533,1592,1644,1694,1732,1770,1806,1847,1888,1934,1973,2020,2084,2123,2173,2219,2282,2320,2375,2424,2470,2513,2558,2613,2663,2714,2768,2807,2861,2915,2965,3022,3075,3122,3172,3219,3266,3313,3352,3400,3452,3488,3539,3590,3642,3683,3732,3772,3829,3893,3956,4045,4096,4143,4194,4231,4274,4319,4376,4432,4480,4537,4597,4658,4713,4778,4857,4939,4998,5057,5114,5162,5214,5273,5324,5377,5434,5468,5517,5554,5606,5684,5737,5782,5820,5856,5903,5954,6012,6051,6091,6140,6200,6237,6282,6329,6375,6411,6462,6509,6566,6620,6662,6702,6751,6822,6871,6925,6972,7021,7071,7110,7159,7207,7257,7295,7333,7392,7436,7483,7538,7598,7648,7705,7750,7804,7853,7905"}}]}, {"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-zh\\values-zh.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-zh\\strings.xml", "from": {"startLines": "44,2,15,46,33,32,76,8,41,42,43,55,21,56,57,9,65,11,31,45,20,87,84,81,86,85,3,88,83,82,4,75,58,40,22,18,49,12,64,68,66,67,69,26,77,48,54,7,73,74,72,78,52,29,30,27,53,23,13,10,28,14,47,19,35,34,37,36,61,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1647,55,517,1734,1227,1179,3045,248,1529,1565,1606,2096,716,2142,2189,286,2515,361,1136,1689,677,3500,3336,3192,3443,3390,99,3554,3289,3242,146,2997,2253,1493,755,577,1876,399,2475,2680,2565,2629,2737,903,3083,1828,2043,214,2867,2919,2814,3126,1947,1027,1078,940,1994,806,440,325,980,477,1779,628,1321,1274,1425,1375,2403,2305,2353", "endColumns": "41,43,36,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,51,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "1684,94,549,1774,1269,1222,3078,281,1560,1601,1642,2137,750,2184,2248,320,2560,394,1174,1729,711,3549,3385,3237,3495,3438,141,3599,3331,3284,188,3040,2300,1524,801,623,1923,435,2510,2732,2624,2675,2783,935,3121,1871,2091,243,2914,2992,2862,3166,1989,1073,1131,975,2038,861,472,356,1022,512,1823,672,1370,1316,1469,1420,2447,2348,2398"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,97,141,178,223,270,318,356,394,430,471,512,558,597,644,708,747,797,835,878,923,962,1016,1070,1120,1177,1230,1277,1327,1374,1421,1468,1516,1568,1604,1655,1706,1758,1799,1839,1896,1960,2011,2062,2099,2142,2190,2243,2277,2329,2407,2460,2505,2552,2603,2661,2701,2750,2810,2847,2883,2930,2970,3019,3068,3122,3169,3218,3268,3317,3365", "endColumns": "41,43,36,44,46,47,37,37,35,40,40,45,38,46,63,38,49,37,42,44,38,53,53,49,56,52,46,49,46,46,46,47,51,35,50,50,51,40,39,56,63,50,50,36,42,47,52,33,51,77,52,44,46,50,57,39,48,59,36,35,46,39,48,48,53,46,48,49,48,47,49", "endOffsets": "92,136,173,218,265,313,351,389,425,466,507,553,592,639,703,742,792,830,873,918,957,1011,1065,1115,1172,1225,1272,1322,1369,1416,1463,1511,1563,1599,1650,1701,1753,1794,1834,1891,1955,2006,2057,2094,2137,2185,2238,2272,2324,2402,2455,2500,2547,2598,2656,2696,2745,2805,2842,2878,2925,2965,3014,3063,3117,3164,3213,3263,3312,3360,3410"}}]}]}