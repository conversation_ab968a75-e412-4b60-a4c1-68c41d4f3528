package com.srthinker.bbnice.setting;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.registration.DeviceRegistrationActivity;

import java.util.ArrayList;
import java.util.List;


public class SettingItemAdapter extends RecyclerView.Adapter<SettingItemAdapter.ViewHolder> {
    private List<SettingItemType> itemList;
    private final Context context;

    /**
     * 构造函数
     * @param context 上下文
     * @param itemList 项目列表
     */
    public SettingItemAdapter(Context context, List<SettingItemType> itemList) {
        this.context = context;
        this.itemList = new ArrayList<>(itemList);
    }

    /**
     * 更新项目列表
     * @param newItems 新的项目列表
     */
    public void updateItems(List<SettingItemType> newItems) {
        // 使用DiffUtil计算差异
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return itemList.size();
            }

            @Override
            public int getNewListSize() {
                return newItems.size();
            }

            @Override
            public boolean areItemsTheSame(int oldPosition, int newPosition) {
                return itemList.get(oldPosition) == newItems.get(newPosition);
            }

            @Override
            public boolean areContentsTheSame(int oldPosition, int newPosition) {
                return itemList.get(oldPosition) == newItems.get(newPosition);
            }
        });

        // 更新数据
        this.itemList = new ArrayList<>(newItems);

        // 分发更新
        diffResult.dispatchUpdatesTo(this);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // 创建视图
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.setting_list_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SettingItemType itemType = itemList.get(position);

        holder.icon.setImageResource(itemType.getIconResId());
        holder.title.setText(itemType.getTitle());

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            switch (itemType) {
                case WIFI:
                    // 打开WiFi设置页面
                    context.startActivity(new Intent(context, WifiActivity.class));
                    break;
                case Bluetooth:
                    // 打开蓝牙设置页面
                    context.startActivity(new Intent(context, BluetoothActivity.class));
                    break;
                case Net:
                    // 打开移动网络设置页面
                    context.startActivity(new Intent(context, MobileNetworkActivity.class));
                    break;
                case Bind:
                    // 打开设备绑定页面
                    context.startActivity(new Intent(context, DeviceRegistrationActivity.class));
                    break;
                default:
                    // 其他设置项暂未实现
                    android.widget.Toast.makeText(context, "功能开发中: " + itemType.getTitle(), android.widget.Toast.LENGTH_SHORT).show();
                    break;
            }
        });
    }

    @Override
    public int getItemCount() {
        return itemList.size();
    }

    /**
     * ViewHolder类
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView icon;
        TextView title;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.setting_icon);
            title = itemView.findViewById(R.id.setting_title);
        }
    }
}
