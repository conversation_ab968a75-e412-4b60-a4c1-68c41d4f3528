{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-en/values-en.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "78,45,2,16,133,111,49,36,35,132,151,147,138,142,143,156,155,157,148,139,149,152,145,140,141,144,153,150,137,131,146,94,8,44,46,47,58,24,59,60,9,68,173,174,11,82,81,80,34,48,166,154,127,165,23,105,102,99,104,103,3,106,101,100,4,75,93,61,43,25,21,52,12,87,67,71,69,125,126,70,130,72,29,95,162,164,163,161,180,181,178,167,175,177,176,160,179,169,51,168,135,83,57,112,7,134,86,91,92,90,96,76,85,55,32,33,14,30,56,26,13,170,172,171,10,114,31,136,113,79,15,50,84,22,38,37,40,39,17,64,62,63,77,110,124,121,115,117,118,116,122,119,123,109,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3630,1954,55,607,6779,5388,2150,1516,1449,6731,7963,7714,7143,7382,7450,8367,8299,8441,7768,7201,7828,8052,7614,7266,7318,7534,8146,7891,7060,6665,7662,4604,266,1916,2000,2049,2588,883,2642,2698,308,3129,9351,9405,390,3842,3782,3728,1401,2098,8934,8237,6529,8868,839,5145,4945,4769,5077,5015,99,5214,4891,4831,155,3502,4546,2797,1878,927,715,2324,432,4188,3083,3344,3191,6304,6404,3280,6612,3418,1128,4646,8672,8795,8724,8614,9989,10052,9837,9002,9488,9709,9577,8538,9926,9137,2266,9077,6901,3937,2528,5454,232,6843,4148,4339,4403,4271,4696,3546,4110,2403,1268,1332,517,1168,2468,989,477,9205,9303,9255,352,5596,1214,6981,5526,3678,561,2206,4001,779,1634,1578,1793,1722,647,2993,2862,2920,3588,5350,6219,6030,5665,5778,5841,5718,6079,5918,6147,5294,5969", "endColumns": "47,45,43,39,63,65,55,61,66,47,88,53,57,67,83,73,67,67,59,64,62,93,47,51,63,79,90,71,82,65,51,41,41,37,48,48,53,43,55,98,43,61,53,82,41,94,59,53,47,51,67,61,59,65,43,68,69,61,67,61,55,60,53,59,55,43,57,64,37,61,63,59,44,56,45,73,88,99,124,63,52,61,39,49,51,72,70,57,62,68,88,74,88,127,131,75,62,67,57,59,79,63,59,71,33,57,39,63,142,67,51,41,37,64,63,68,43,45,59,101,39,49,47,47,37,68,53,78,69,49,45,59,108,59,87,55,65,70,43,66,57,72,41,37,84,48,52,62,76,59,67,50,71,55,60", "endOffsets": "3673,1995,94,642,6838,5449,2201,1573,1511,6774,8047,7763,7196,7445,7529,8436,8362,8504,7823,7261,7886,8141,7657,7313,7377,7609,8232,7958,7138,6726,7709,4641,303,1949,2044,2093,2637,922,2693,2792,347,3186,9400,9483,427,3932,3837,3777,1444,2145,8997,8294,6584,8929,878,5209,5010,4826,5140,5072,150,5270,4940,4886,206,3541,4599,2857,1911,984,774,2379,472,4240,3124,3413,3275,6399,6524,3339,6660,3475,1163,4691,8719,8863,8790,8667,10047,10116,9921,9072,9572,9832,9704,8609,9984,9200,2319,9132,6976,3996,2583,5521,261,6896,4183,4398,4541,4334,4743,3583,4143,2463,1327,1396,556,1209,2523,1086,512,9250,9346,9298,385,5660,1263,7055,5591,3723,602,2261,4105,834,1717,1629,1854,1788,686,3055,2915,2988,3625,5383,6299,6074,5713,5836,5913,5773,6142,5964,6214,5345,6025"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,103,149,193,233,297,363,419,481,548,596,685,739,797,865,949,1023,1091,1159,1219,1284,1347,1441,1489,1541,1605,1685,1776,1848,1931,1997,2049,2091,2133,2171,2220,2269,2323,2367,2423,2522,2566,2628,2682,2765,2807,2902,2962,3016,3064,3116,3184,3246,3306,3372,3416,3485,3555,3617,3685,3747,3803,3864,3918,3978,4034,4078,4136,4201,4239,4301,4365,4425,4470,4527,4573,4647,4736,4836,4961,5025,5078,5140,5180,5230,5282,5355,5426,5484,5547,5616,5705,5780,5869,5997,6129,6205,6268,6336,6394,6454,6534,6598,6658,6730,6764,6822,6862,6926,7069,7137,7189,7231,7269,7334,7398,7467,7511,7557,7617,7719,7759,7809,7857,7905,7943,8012,8066,8145,8215,8265,8311,8371,8480,8540,8628,8684,8750,8821,8865,8932,8990,9063,9105,9143,9228,9277,9330,9393,9470,9530,9598,9649,9721,9777", "endColumns": "47,45,43,39,63,65,55,61,66,47,88,53,57,67,83,73,67,67,59,64,62,93,47,51,63,79,90,71,82,65,51,41,41,37,48,48,53,43,55,98,43,61,53,82,41,94,59,53,47,51,67,61,59,65,43,68,69,61,67,61,55,60,53,59,55,43,57,64,37,61,63,59,44,56,45,73,88,99,124,63,52,61,39,49,51,72,70,57,62,68,88,74,88,127,131,75,62,67,57,59,79,63,59,71,33,57,39,63,142,67,51,41,37,64,63,68,43,45,59,101,39,49,47,47,37,68,53,78,69,49,45,59,108,59,87,55,65,70,43,66,57,72,41,37,84,48,52,62,76,59,67,50,71,55,60", "endOffsets": "98,144,188,228,292,358,414,476,543,591,680,734,792,860,944,1018,1086,1154,1214,1279,1342,1436,1484,1536,1600,1680,1771,1843,1926,1992,2044,2086,2128,2166,2215,2264,2318,2362,2418,2517,2561,2623,2677,2760,2802,2897,2957,3011,3059,3111,3179,3241,3301,3367,3411,3480,3550,3612,3680,3742,3798,3859,3913,3973,4029,4073,4131,4196,4234,4296,4360,4420,4465,4522,4568,4642,4731,4831,4956,5020,5073,5135,5175,5225,5277,5350,5421,5479,5542,5611,5700,5775,5864,5992,6124,6200,6263,6331,6389,6449,6529,6593,6653,6725,6759,6817,6857,6921,7064,7132,7184,7226,7264,7329,7393,7462,7506,7552,7612,7714,7754,7804,7852,7900,7938,8007,8061,8140,8210,8260,8306,8366,8475,8535,8623,8679,8745,8816,8860,8927,8985,9058,9100,9138,9223,9272,9325,9388,9465,9525,9593,9644,9716,9772,9833"}}]}]}