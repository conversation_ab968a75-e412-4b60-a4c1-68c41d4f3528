{"logs": [{"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-en\\values-en.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,133,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,134,135,136,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6706,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6750,6812,6866,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,61,53,71,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6745,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6807,6861,6933,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,157,203,247,287,388,452,518,574,636,703,751,933,1124,1217,1306,1360,1418,1486,1570,1644,1712,1780,1849,1909,1974,2037,2137,2241,2335,2435,2524,2616,2720,2812,2918,2997,3089,3175,3258,3364,3552,3749,3848,3949,4043,4091,4143,4207,4287,4378,4474,4546,4629,4716,4782,4834,4910,4962,5004,5046,5084,5133,5182,5236,5280,5336,5435,5479,5541,5595,5678,5720,5815,5875,5929,5977,6029,6097,6151,6195,6317,6379,6439,6505,6557,6601,6670,6740,6802,6870,6932,6988,7049,7103,7163,7219,7263,7347,7405,7470,7508,7570,7634,7694,7739,7796,7842,7916,8005,8105,8230,8294,8347,8409,8449,8503,8553,8605,8678,8749,8807,8870,8939,9028,9103,9192,9320,9452,9528,9591,9655,9717,9765,9809,9877,9935,9995,10075,10139,10199,10271,10339,10373,10431,10471,10535,10678,10746,10798,10840,10878,10943,11007,11076,11120,11166,11226,11328,11368,11420,11470,11518,11566,11604,11673,11727,11806,11876,11938,11992,12064,12114,12160,12220,12264,12373,12433,12489,12577,12633,12699,12770,12814,12881,12939,13012,13054,13094,13152,13210,13262,13324,13372,13410,13495,13544,13597,13660,13737,13797,13865,13916,13988,14044", "endColumns": "53,47,45,43,39,100,63,65,55,61,66,47,181,190,92,88,53,57,67,83,73,67,67,68,59,64,62,99,103,93,99,88,91,103,91,105,78,91,85,82,105,187,196,98,100,93,47,51,63,79,90,95,71,82,86,65,51,75,51,41,41,37,48,48,53,43,55,98,43,61,53,82,41,94,59,53,47,51,67,53,43,121,61,59,65,51,43,68,69,61,67,61,55,60,53,59,55,43,83,57,64,37,61,63,59,44,56,45,73,88,99,124,63,52,61,39,53,49,51,72,70,57,62,68,88,74,88,127,131,75,62,63,61,47,43,67,57,59,79,63,59,71,67,33,57,39,63,142,67,51,41,37,64,63,68,43,45,59,101,39,51,49,47,47,37,68,53,78,69,61,53,71,49,45,59,43,108,59,55,87,55,65,70,43,66,57,72,41,39,57,57,51,61,47,37,84,48,52,62,76,59,67,50,71,55,60", "endOffsets": "104,152,198,242,282,383,447,513,569,631,698,746,928,1119,1212,1301,1355,1413,1481,1565,1639,1707,1775,1844,1904,1969,2032,2132,2236,2330,2430,2519,2611,2715,2807,2913,2992,3084,3170,3253,3359,3547,3744,3843,3944,4038,4086,4138,4202,4282,4373,4469,4541,4624,4711,4777,4829,4905,4957,4999,5041,5079,5128,5177,5231,5275,5331,5430,5474,5536,5590,5673,5715,5810,5870,5924,5972,6024,6092,6146,6190,6312,6374,6434,6500,6552,6596,6665,6735,6797,6865,6927,6983,7044,7098,7158,7214,7258,7342,7400,7465,7503,7565,7629,7689,7734,7791,7837,7911,8000,8100,8225,8289,8342,8404,8444,8498,8548,8600,8673,8744,8802,8865,8934,9023,9098,9187,9315,9447,9523,9586,9650,9712,9760,9804,9872,9930,9990,10070,10134,10194,10266,10334,10368,10426,10466,10530,10673,10741,10793,10835,10873,10938,11002,11071,11115,11161,11221,11323,11363,11415,11465,11513,11561,11599,11668,11722,11801,11871,11933,11987,12059,12109,12155,12215,12259,12368,12428,12484,12572,12628,12694,12765,12809,12876,12934,13007,13049,13089,13147,13205,13257,13319,13367,13405,13490,13539,13592,13655,13732,13792,13860,13911,13983,14039,14100"}}]}, {"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-en/values-en.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,135,137,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,136,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6812,6932,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6870,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,57,39,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,61,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6865,6967,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6927,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,157,203,247,287,388,452,518,574,636,703,751,933,1124,1217,1306,1360,1418,1486,1570,1644,1712,1780,1849,1909,1974,2037,2137,2241,2335,2435,2524,2616,2720,2812,2918,2997,3089,3175,3258,3364,3552,3749,3848,3949,4043,4091,4143,4207,4287,4378,4474,4546,4629,4716,4782,4834,4910,4962,5004,5046,5084,5133,5182,5236,5280,5336,5435,5479,5541,5595,5678,5720,5815,5875,5929,5977,6029,6097,6151,6195,6317,6379,6439,6505,6557,6601,6670,6740,6802,6870,6932,6988,7049,7103,7163,7219,7263,7347,7405,7470,7508,7570,7634,7694,7739,7796,7842,7916,8005,8105,8230,8294,8347,8409,8449,8503,8553,8605,8678,8749,8807,8870,8939,9028,9103,9192,9320,9452,9528,9591,9655,9717,9765,9809,9877,9935,9995,10075,10139,10199,10271,10339,10373,10431,10471,10535,10678,10746,10798,10840,10878,10943,11007,11076,11120,11166,11226,11328,11368,11420,11470,11518,11566,11604,11673,11727,11806,11876,11938,11996,12036,12086,12132,12192,12236,12345,12405,12461,12549,12605,12671,12742,12786,12853,12911,12984,13026,13066,13124,13182,13234,13296,13344,13406,13444,13529,13578,13631,13694,13771,13831,13899,13950,14022,14078", "endColumns": "53,47,45,43,39,100,63,65,55,61,66,47,181,190,92,88,53,57,67,83,73,67,67,68,59,64,62,99,103,93,99,88,91,103,91,105,78,91,85,82,105,187,196,98,100,93,47,51,63,79,90,95,71,82,86,65,51,75,51,41,41,37,48,48,53,43,55,98,43,61,53,82,41,94,59,53,47,51,67,53,43,121,61,59,65,51,43,68,69,61,67,61,55,60,53,59,55,43,83,57,64,37,61,63,59,44,56,45,73,88,99,124,63,52,61,39,53,49,51,72,70,57,62,68,88,74,88,127,131,75,62,63,61,47,43,67,57,59,79,63,59,71,67,33,57,39,63,142,67,51,41,37,64,63,68,43,45,59,101,39,51,49,47,47,37,68,53,78,69,61,57,39,49,45,59,43,108,59,55,87,55,65,70,43,66,57,72,41,39,57,57,51,61,47,61,37,84,48,52,62,76,59,67,50,71,55,60", "endOffsets": "104,152,198,242,282,383,447,513,569,631,698,746,928,1119,1212,1301,1355,1413,1481,1565,1639,1707,1775,1844,1904,1969,2032,2132,2236,2330,2430,2519,2611,2715,2807,2913,2992,3084,3170,3253,3359,3547,3744,3843,3944,4038,4086,4138,4202,4282,4373,4469,4541,4624,4711,4777,4829,4905,4957,4999,5041,5079,5128,5177,5231,5275,5331,5430,5474,5536,5590,5673,5715,5810,5870,5924,5972,6024,6092,6146,6190,6312,6374,6434,6500,6552,6596,6665,6735,6797,6865,6927,6983,7044,7098,7158,7214,7258,7342,7400,7465,7503,7565,7629,7689,7734,7791,7837,7911,8000,8100,8225,8289,8342,8404,8444,8498,8548,8600,8673,8744,8802,8865,8934,9023,9098,9187,9315,9447,9523,9586,9650,9712,9760,9804,9872,9930,9990,10070,10134,10194,10266,10334,10368,10426,10466,10530,10673,10741,10793,10835,10873,10938,11002,11071,11115,11161,11221,11323,11363,11415,11465,11513,11561,11599,11668,11722,11801,11871,11933,11991,12031,12081,12127,12187,12231,12340,12400,12456,12544,12600,12666,12737,12781,12848,12906,12979,13021,13061,13119,13177,13229,13291,13339,13401,13439,13524,13573,13626,13689,13766,13826,13894,13945,14017,14073,14134"}}]}]}