{"logs": [{"outputFile": "D:\\Workspace\\env\\.gradle\\daemon\\8.11.1\\com.srthinker.bbnice.app-mergeDebugResources-2:\\values-en\\values-en.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "2,15,45,33,32,75,8,41,42,43,54,21,55,56,9,64,11,31,44,20,86,83,80,85,84,3,87,82,81,4,74,57,40,22,18,48,12,63,67,65,66,68,26,76,47,53,7,72,73,71,77,51,29,30,27,52,23,13,10,28,14,46,19,35,34,37,36,60,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,563,2015,1427,1360,3704,266,1827,1865,1914,2453,794,2507,2563,308,2994,390,1312,1963,750,4245,4045,3869,4177,4115,99,4314,3991,3931,155,3646,2662,1789,838,626,2189,432,2948,3209,3056,3145,3283,1039,3746,2131,2393,232,3439,3503,3371,3796,2268,1179,1243,1079,2333,900,477,352,1125,517,2071,690,1545,1489,1704,1633,2858,2727,2785", "endColumns": "43,39,55,61,66,41,41,37,48,48,53,43,55,98,43,61,41,47,51,43,68,69,61,67,61,55,60,53,59,55,57,64,37,61,63,59,44,45,73,88,63,61,39,49,57,59,33,63,142,67,51,64,63,68,45,59,101,39,37,53,45,59,59,87,55,65,70,66,57,72", "endOffsets": "94,598,2066,1484,1422,3741,303,1860,1909,1958,2502,833,2558,2657,347,3051,427,1355,2010,789,4309,4110,3926,4240,4172,150,4370,4040,3986,206,3699,2722,1822,895,685,2244,472,2989,3278,3140,3204,3340,1074,3791,2184,2448,261,3498,3641,3434,3843,2328,1238,1307,1120,2388,997,512,385,1174,558,2126,745,1628,1540,1765,1699,2920,2780,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,99,139,195,257,324,366,408,446,495,544,598,642,698,797,841,903,945,993,1045,1089,1158,1228,1290,1358,1420,1476,1537,1591,1651,1707,1765,1830,1868,1930,1994,2054,2099,2145,2219,2308,2372,2434,2474,2524,2582,2642,2676,2740,2883,2951,3003,3068,3132,3201,3247,3307,3409,3449,3487,3541,3587,3647,3707,3795,3851,3917,3988,4055,4113", "endColumns": "43,39,55,61,66,41,41,37,48,48,53,43,55,98,43,61,41,47,51,43,68,69,61,67,61,55,60,53,59,55,57,64,37,61,63,59,44,45,73,88,63,61,39,49,57,59,33,63,142,67,51,64,63,68,45,59,101,39,37,53,45,59,59,87,55,65,70,66,57,72", "endOffsets": "94,134,190,252,319,361,403,441,490,539,593,637,693,792,836,898,940,988,1040,1084,1153,1223,1285,1353,1415,1471,1532,1586,1646,1702,1760,1825,1863,1925,1989,2049,2094,2140,2214,2303,2367,2429,2469,2519,2577,2637,2671,2735,2878,2946,2998,3063,3127,3196,3242,3302,3404,3444,3482,3536,3582,3642,3702,3790,3846,3912,3983,4050,4108,4181"}}]}, {"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-en/values-en.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "78,45,-1,-1,133,111,-1,-1,-1,132,151,147,138,142,143,156,155,157,148,139,149,152,145,140,141,144,153,150,137,131,146,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,173,174,-1,82,81,80,-1,-1,166,154,127,165,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,75,-1,-1,-1,-1,-1,-1,-1,87,-1,-1,-1,125,126,-1,130,-1,-1,-1,162,164,163,161,180,181,178,167,175,177,176,160,179,169,-1,168,135,83,-1,112,-1,134,86,-1,-1,-1,-1,76,85,-1,-1,-1,14,-1,-1,-1,-1,170,172,171,-1,114,-1,136,113,79,-1,-1,84,-1,-1,-1,-1,-1,17,-1,-1,-1,77,110,124,121,115,117,118,116,122,119,123,109,120", "startColumns": "4,4,-1,-1,4,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,4,4,-1,-1,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,-1,4,-1,4,4,-1,-1,-1,-1,4,4,-1,-1,-1,4,-1,-1,-1,-1,4,4,4,-1,4,-1,4,4,4,-1,-1,4,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3630,1954,-1,-1,6779,5388,-1,-1,-1,6731,7963,7714,7143,7382,7450,8367,8299,8441,7768,7201,7828,8052,7614,7266,7318,7534,8146,7891,7060,6665,7662,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9351,9405,-1,3842,3782,3728,-1,-1,8934,8237,6529,8868,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3502,-1,-1,-1,-1,-1,-1,-1,4188,-1,-1,-1,6304,6404,-1,6612,-1,-1,-1,8672,8795,8724,8614,9989,10052,9837,9002,9488,9709,9577,8538,9926,9137,-1,9077,6901,3937,-1,5454,-1,6843,4148,-1,-1,-1,-1,3546,4110,-1,-1,-1,517,-1,-1,-1,-1,9205,9303,9255,-1,5596,-1,6981,5526,3678,-1,-1,4001,-1,-1,-1,-1,-1,647,-1,-1,-1,3588,5350,6219,6030,5665,5778,5841,5718,6079,5918,6147,5294,5969", "endColumns": "47,45,-1,-1,63,65,-1,-1,-1,47,88,53,57,67,83,73,67,67,59,64,62,93,47,51,63,79,90,71,82,65,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,53,82,-1,94,59,53,-1,-1,67,61,59,65,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,-1,-1,-1,-1,-1,-1,-1,56,-1,-1,-1,99,124,-1,52,-1,-1,-1,51,72,70,57,62,68,88,74,88,127,131,75,62,67,-1,59,79,63,-1,71,-1,57,39,-1,-1,-1,-1,41,37,-1,-1,-1,43,-1,-1,-1,-1,49,47,47,-1,68,-1,78,69,49,-1,-1,108,-1,-1,-1,-1,-1,43,-1,-1,-1,41,37,84,48,52,62,76,59,67,50,71,55,60", "endOffsets": "3673,1995,-1,-1,6838,5449,-1,-1,-1,6774,8047,7763,7196,7445,7529,8436,8362,8504,7823,7261,7886,8141,7657,7313,7377,7609,8232,7958,7138,6726,7709,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9400,9483,-1,3932,3837,3777,-1,-1,8997,8294,6584,8929,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3541,-1,-1,-1,-1,-1,-1,-1,4240,-1,-1,-1,6399,6524,-1,6660,-1,-1,-1,8719,8863,8790,8667,10047,10116,9921,9072,9572,9832,9704,8609,9984,9200,-1,9132,6976,3996,-1,5521,-1,6896,4183,-1,-1,-1,-1,3583,4143,-1,-1,-1,556,-1,-1,-1,-1,9250,9346,9298,-1,5660,-1,7055,5591,3723,-1,-1,4105,-1,-1,-1,-1,-1,686,-1,-1,-1,3625,5383,6299,6074,5713,5836,5913,5773,6142,5964,6214,5345,6025"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,103,149,193,233,297,363,419,481,548,596,685,739,797,865,949,1023,1091,1159,1219,1284,1347,1441,1489,1541,1605,1685,1776,1848,1931,1997,2049,2091,2133,2171,2220,2269,2323,2367,2423,2522,2566,2628,2682,2765,2807,2902,2962,3016,3064,3116,3184,3246,3306,3372,3416,3485,3555,3617,3685,3747,3803,3864,3918,3978,4034,4078,4136,4201,4239,4301,4365,4425,4470,4527,4573,4647,4736,4836,4961,5025,5078,5140,5180,5230,5282,5355,5426,5484,5547,5616,5705,5780,5869,5997,6129,6205,6268,6336,6394,6454,6534,6598,6658,6730,6764,6822,6862,6926,7069,7137,7189,7231,7269,7334,7398,7467,7511,7557,7617,7719,7759,7809,7857,7905,7943,8012,8066,8145,8215,8265,8311,8371,8480,8540,8628,8684,8750,8821,8865,8932,8990,9063,9105,9143,9228,9277,9330,9393,9470,9530,9598,9649,9721,9777", "endColumns": "47,45,43,39,63,65,55,61,66,47,88,53,57,67,83,73,67,67,59,64,62,93,47,51,63,79,90,71,82,65,51,41,41,37,48,48,53,43,55,98,43,61,53,82,41,94,59,53,47,51,67,61,59,65,43,68,69,61,67,61,55,60,53,59,55,43,57,64,37,61,63,59,44,56,45,73,88,99,124,63,52,61,39,49,51,72,70,57,62,68,88,74,88,127,131,75,62,67,57,59,79,63,59,71,33,57,39,63,142,67,51,41,37,64,63,68,43,45,59,101,39,49,47,47,37,68,53,78,69,49,45,59,108,59,87,55,65,70,43,66,57,72,41,37,84,48,52,62,76,59,67,50,71,55,60", "endOffsets": "98,144,188,228,292,358,414,476,543,591,680,734,792,860,944,1018,1086,1154,1214,1279,1342,1436,1484,1536,1600,1680,1771,1843,1926,1992,2044,2086,2128,2166,2215,2264,2318,2362,2418,2517,2561,2623,2677,2760,2802,2897,2957,3011,3059,3111,3179,3241,3301,3367,3411,3480,3550,3612,3680,3742,3798,3859,3913,3973,4029,4073,4131,4196,4234,4296,4360,4420,4465,4522,4568,4642,4731,4831,4956,5020,5073,5135,5175,5225,5277,5350,5421,5479,5542,5611,5700,5775,5864,5992,6124,6200,6263,6331,6389,6449,6529,6593,6653,6725,6759,6817,6857,6921,7064,7132,7184,7226,7264,7329,7393,7462,7506,7552,7612,7714,7754,7804,7852,7900,7938,8007,8061,8140,8210,8260,8306,8366,8475,8535,8623,8679,8745,8816,8860,8927,8985,9058,9100,9138,9223,9272,9325,9388,9465,9525,9593,9644,9716,9772,9833"}}]}]}