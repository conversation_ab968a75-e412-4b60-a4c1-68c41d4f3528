{"logs": [{"outputFile": "com.srthinker.bbnice.app-mergeDebugResources-2:/values-en/values-en.xml", "map": [{"source": "D:\\Workspace\\Projects\\BBNice\\AndroidClient\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "2,15,45,33,32,75,8,41,42,43,54,21,55,56,9,64,11,31,44,20,86,83,80,85,84,3,87,82,81,4,74,57,40,22,18,48,12,63,67,65,66,68,26,76,47,53,7,72,73,71,77,51,29,30,27,52,23,13,10,28,14,46,19,35,34,37,36,60,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,563,2015,1427,1360,3704,266,1827,1865,1914,2453,794,2507,2563,308,2994,390,1312,1963,750,4245,4045,3869,4177,4115,99,4314,3991,3931,155,3646,2662,1789,838,626,2189,432,2948,3209,3056,3145,3283,1039,3746,2131,2393,232,3439,3503,3371,3796,2268,1179,1243,1079,2333,900,477,352,1125,517,2071,690,1545,1489,1704,1633,2858,2727,2785", "endColumns": "43,39,55,61,66,41,41,37,48,48,53,43,55,98,43,61,41,47,51,43,68,69,61,67,61,55,60,53,59,55,57,64,37,61,63,59,44,45,73,88,63,61,39,49,57,59,33,63,142,67,51,64,63,68,45,59,101,39,37,53,45,59,59,87,55,65,70,66,57,72", "endOffsets": "94,598,2066,1484,1422,3741,303,1860,1909,1958,2502,833,2558,2657,347,3051,427,1355,2010,789,4309,4110,3926,4240,4172,150,4370,4040,3986,206,3699,2722,1822,895,685,2244,472,2989,3278,3140,3204,3340,1074,3791,2184,2448,261,3498,3641,3434,3843,2328,1238,1307,1120,2388,997,512,385,1174,558,2126,745,1628,1540,1765,1699,2920,2780,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,99,139,195,257,324,366,408,446,495,544,598,642,698,797,841,903,945,993,1045,1089,1158,1228,1290,1358,1420,1476,1537,1591,1651,1707,1765,1830,1868,1930,1994,2054,2099,2145,2219,2308,2372,2434,2474,2524,2582,2642,2676,2740,2883,2951,3003,3068,3132,3201,3247,3307,3409,3449,3487,3541,3587,3647,3707,3795,3851,3917,3988,4055,4113", "endColumns": "43,39,55,61,66,41,41,37,48,48,53,43,55,98,43,61,41,47,51,43,68,69,61,67,61,55,60,53,59,55,57,64,37,61,63,59,44,45,73,88,63,61,39,49,57,59,33,63,142,67,51,64,63,68,45,59,101,39,37,53,45,59,59,87,55,65,70,66,57,72", "endOffsets": "94,134,190,252,319,361,403,441,490,539,593,637,693,792,836,898,940,988,1040,1084,1153,1223,1285,1353,1415,1471,1532,1586,1646,1702,1760,1825,1863,1925,1989,2049,2094,2140,2214,2303,2367,2429,2469,2519,2577,2637,2671,2735,2878,2946,2998,3063,3127,3196,3242,3302,3404,3444,3482,3536,3582,3642,3702,3790,3846,3912,3983,4050,4108,4181"}}]}]}