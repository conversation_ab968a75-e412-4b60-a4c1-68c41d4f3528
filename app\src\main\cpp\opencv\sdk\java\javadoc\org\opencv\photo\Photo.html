<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (17) on Thu Jan 09 09:33:49 UTC 2025 -->
<title>Photo (OpenCV 4.11.0 Java documentation)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-01-09">
<meta name="description" content="declaration: package: org.opencv.photo, class: Photo">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<div class="about-language">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">org.opencv.photo</a></div>
<h1 title="Class Photo" class="title">Class Photo</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.opencv.photo.Photo</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Photo</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#INPAINT_NS" class="member-name-link">INPAINT_NS</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#INPAINT_TELEA" class="member-name-link">INPAINT_TELEA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#LDR_SIZE" class="member-name-link">LDR_SIZE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MIXED_CLONE" class="member-name-link">MIXED_CLONE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MIXED_CLONE_WIDE" class="member-name-link">MIXED_CLONE_WIDE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MONOCHROME_TRANSFER" class="member-name-link">MONOCHROME_TRANSFER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MONOCHROME_TRANSFER_WIDE" class="member-name-link">MONOCHROME_TRANSFER_WIDE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NORMAL_CLONE" class="member-name-link">NORMAL_CLONE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#NORMAL_CLONE_WIDE" class="member-name-link">NORMAL_CLONE_WIDE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#NORMCONV_FILTER" class="member-name-link">NORMCONV_FILTER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#RECURS_FILTER" class="member-name-link">RECURS_FILTER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Photo</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">colorChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">colorChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;red_mul)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">colorChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;red_mul,
 float&nbsp;green_mul)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float,float)" class="member-name-link">colorChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;red_mul,
 float&nbsp;green_mul,
 float&nbsp;blue_mul)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createAlignMTB()" class="member-name-link">createAlignMTB</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates AlignMTB object

 usually good enough (31 and 63 pixels shift respectively).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createAlignMTB(int)" class="member-name-link">createAlignMTB</a><wbr>(int&nbsp;max_bits)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates AlignMTB object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createAlignMTB(int,int)" class="member-name-link">createAlignMTB</a><wbr>(int&nbsp;max_bits,
 int&nbsp;exclude_range)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates AlignMTB object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createAlignMTB(int,int,boolean)" class="member-name-link">createAlignMTB</a><wbr>(int&nbsp;max_bits,
 int&nbsp;exclude_range,
 boolean&nbsp;cut)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates AlignMTB object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateDebevec()" class="member-name-link">createCalibrateDebevec</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateDebevec object

 response.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateDebevec(int)" class="member-name-link">createCalibrateDebevec</a><wbr>(int&nbsp;samples)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateDebevec object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateDebevec(int,float)" class="member-name-link">createCalibrateDebevec</a><wbr>(int&nbsp;samples,
 float&nbsp;lambda)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateDebevec object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateDebevec(int,float,boolean)" class="member-name-link">createCalibrateDebevec</a><wbr>(int&nbsp;samples,
 float&nbsp;lambda,
 boolean&nbsp;random)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateDebevec object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateRobertson()" class="member-name-link">createCalibrateRobertson</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateRobertson object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateRobertson(int)" class="member-name-link">createCalibrateRobertson</a><wbr>(int&nbsp;max_iter)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateRobertson object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createCalibrateRobertson(int,float)" class="member-name-link">createCalibrateRobertson</a><wbr>(int&nbsp;max_iter,
 float&nbsp;threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates CalibrateRobertson object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createMergeDebevec()" class="member-name-link">createMergeDebevec</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MergeDebevec object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createMergeMertens()" class="member-name-link">createMergeMertens</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MergeMertens object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createMergeMertens(float)" class="member-name-link">createMergeMertens</a><wbr>(float&nbsp;contrast_weight)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MergeMertens object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createMergeMertens(float,float)" class="member-name-link">createMergeMertens</a><wbr>(float&nbsp;contrast_weight,
 float&nbsp;saturation_weight)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MergeMertens object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createMergeMertens(float,float,float)" class="member-name-link">createMergeMertens</a><wbr>(float&nbsp;contrast_weight,
 float&nbsp;saturation_weight,
 float&nbsp;exposure_weight)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MergeMertens object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createMergeRobertson()" class="member-name-link">createMergeRobertson</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates MergeRobertson object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Tonemap.html" title="class in org.opencv.photo">Tonemap</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemap()" class="member-name-link">createTonemap</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates simple linear mapper with gamma correction

 equal to 2.2f is suitable for most displays.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Tonemap.html" title="class in org.opencv.photo">Tonemap</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemap(float)" class="member-name-link">createTonemap</a><wbr>(float&nbsp;gamma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates simple linear mapper with gamma correction</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapDrago()" class="member-name-link">createTonemapDrago</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapDrago object

 than 1 increase saturation and values less than 1 decrease it.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapDrago(float)" class="member-name-link">createTonemapDrago</a><wbr>(float&nbsp;gamma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapDrago object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapDrago(float,float)" class="member-name-link">createTonemapDrago</a><wbr>(float&nbsp;gamma,
 float&nbsp;saturation)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapDrago object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapDrago(float,float,float)" class="member-name-link">createTonemapDrago</a><wbr>(float&nbsp;gamma,
 float&nbsp;saturation,
 float&nbsp;bias)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapDrago object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapMantiuk()" class="member-name-link">createTonemapMantiuk</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapMantiuk object

 dynamic range.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapMantiuk(float)" class="member-name-link">createTonemapMantiuk</a><wbr>(float&nbsp;gamma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapMantiuk object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapMantiuk(float,float)" class="member-name-link">createTonemapMantiuk</a><wbr>(float&nbsp;gamma,
 float&nbsp;scale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapMantiuk object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapMantiuk(float,float,float)" class="member-name-link">createTonemapMantiuk</a><wbr>(float&nbsp;gamma,
 float&nbsp;scale,
 float&nbsp;saturation)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapMantiuk object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapReinhard()" class="member-name-link">createTonemapReinhard</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapReinhard object

 value, if 0 it's global, otherwise it's a weighted mean of this two cases.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapReinhard(float)" class="member-name-link">createTonemapReinhard</a><wbr>(float&nbsp;gamma)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapReinhard object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapReinhard(float,float)" class="member-name-link">createTonemapReinhard</a><wbr>(float&nbsp;gamma,
 float&nbsp;intensity)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapReinhard object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapReinhard(float,float,float)" class="member-name-link">createTonemapReinhard</a><wbr>(float&nbsp;gamma,
 float&nbsp;intensity,
 float&nbsp;light_adapt)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapReinhard object</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#createTonemapReinhard(float,float,float,float)" class="member-name-link">createTonemapReinhard</a><wbr>(float&nbsp;gamma,
 float&nbsp;intensity,
 float&nbsp;light_adapt,
 float&nbsp;color_adapt)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates TonemapReinhard object</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#decolor(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">decolor</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grayscale,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;color_boost)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Transforms a color image to a grayscale image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#denoise_TVL1(java.util.List,org.opencv.core.Mat)" class="member-name-link">denoise_TVL1</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#denoise_TVL1(java.util.List,org.opencv.core.Mat,double)" class="member-name-link">denoise_TVL1</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
 double&nbsp;lambda)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#denoise_TVL1(java.util.List,org.opencv.core.Mat,double,int)" class="member-name-link">denoise_TVL1</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
 double&nbsp;lambda,
 int&nbsp;niters)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#detailEnhance(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">detailEnhance</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">This filter enhances the details of a particular image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#detailEnhance(org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">detailEnhance</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">This filter enhances the details of a particular image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#detailEnhance(org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">detailEnhance</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">This filter enhances the details of a particular image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">edgePreservingFilter</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat,int)" class="member-name-link">edgePreservingFilter</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat,int,float)" class="member-name-link">edgePreservingFilter</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags,
 float&nbsp;sigma_s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat,int,float,float)" class="member-name-link">edgePreservingFilter</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Filtering is the fundamental operation in image and video processing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,float,int)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 int&nbsp;templateWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,float,int,int)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat,int)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat,int,int)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat,int,int,int)" class="member-name-link">fastNlMeansDenoising</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize,
 int&nbsp;normType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">fastNlMeansDenoisingColored</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">fastNlMeansDenoisingColored</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">fastNlMeansDenoisingColored</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 float&nbsp;hColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float,float,int)" class="member-name-link">fastNlMeansDenoisingColored</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float,float,int,int)" class="member-name-link">fastNlMeansDenoisingColored</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int)" class="member-name-link">fastNlMeansDenoisingColoredMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float)" class="member-name-link">fastNlMeansDenoisingColoredMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float,float)" class="member-name-link">fastNlMeansDenoisingColoredMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 float&nbsp;hColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float,float,int)" class="member-name-link">fastNlMeansDenoisingColoredMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float,float,int,int)" class="member-name-link">fastNlMeansDenoisingColoredMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,float)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,float,int)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 int&nbsp;templateWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,float,int,int)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat,int)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat,int,int)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat,int,int,int)" class="member-name-link">fastNlMeansDenoisingMulti</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize,
 int&nbsp;normType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#illuminationChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">illuminationChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#illuminationChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">illuminationChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;alpha)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#illuminationChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">illuminationChange</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;alpha,
 float&nbsp;beta)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#inpaint(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,double,int)" class="member-name-link">inpaint</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inpaintMask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 double&nbsp;inpaintRadius,
 int&nbsp;flags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Restores the selected region in an image using the region neighborhood.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">pencilSketch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Pencil-like non-photorealistic line drawing</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">pencilSketch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
 float&nbsp;sigma_s)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Pencil-like non-photorealistic line drawing</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">pencilSketch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Pencil-like non-photorealistic line drawing</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float,float)" class="member-name-link">pencilSketch</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r,
 float&nbsp;shade_factor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Pencil-like non-photorealistic line drawing</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#seamlessClone(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Point,org.opencv.core.Mat,int)" class="member-name-link">seamlessClone</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;p,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blend,
 int&nbsp;flags)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Performs seamless cloning to blend a region from a source image into a destination image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stylization(org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">stylization</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stylization(org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">stylization</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stylization(org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">stylization</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)" class="member-name-link">textureFlattening</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)" class="member-name-link">textureFlattening</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;low_threshold)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)" class="member-name-link">textureFlattening</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;low_threshold,
 float&nbsp;high_threshold)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float,int)" class="member-name-link">textureFlattening</a><wbr>(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;low_threshold,
 float&nbsp;high_threshold,
 int&nbsp;kernel_size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="INPAINT_NS">
<h3>INPAINT_NS</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">INPAINT_NS</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.INPAINT_NS">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="INPAINT_TELEA">
<h3>INPAINT_TELEA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">INPAINT_TELEA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.INPAINT_TELEA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="LDR_SIZE">
<h3>LDR_SIZE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">LDR_SIZE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.LDR_SIZE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="RECURS_FILTER">
<h3>RECURS_FILTER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">RECURS_FILTER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.RECURS_FILTER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NORMCONV_FILTER">
<h3>NORMCONV_FILTER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NORMCONV_FILTER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMCONV_FILTER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NORMAL_CLONE">
<h3>NORMAL_CLONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NORMAL_CLONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMAL_CLONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MIXED_CLONE">
<h3>MIXED_CLONE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MIXED_CLONE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.MIXED_CLONE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MONOCHROME_TRANSFER">
<h3>MONOCHROME_TRANSFER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MONOCHROME_TRANSFER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.MONOCHROME_TRANSFER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="NORMAL_CLONE_WIDE">
<h3>NORMAL_CLONE_WIDE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">NORMAL_CLONE_WIDE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.NORMAL_CLONE_WIDE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MIXED_CLONE_WIDE">
<h3>MIXED_CLONE_WIDE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MIXED_CLONE_WIDE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.MIXED_CLONE_WIDE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MONOCHROME_TRANSFER_WIDE">
<h3>MONOCHROME_TRANSFER_WIDE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MONOCHROME_TRANSFER_WIDE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="see-list">
<li><a href="../../../constant-values.html#org.opencv.photo.Photo.MONOCHROME_TRANSFER_WIDE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Photo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Photo</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="inpaint(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,double,int)">
<h3>inpaint</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">inpaint</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;inpaintMask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 double&nbsp;inpaintRadius,
 int&nbsp;flags)</span></div>
<div class="block">Restores the selected region in an image using the region neighborhood.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit, 16-bit unsigned or 32-bit float 1-channel or 8-bit 3-channel image.</dd>
<dd><code>inpaintMask</code> - Inpainting mask, 8-bit 1-channel image. Non-zero pixels indicate the area that
 needs to be inpainted.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>inpaintRadius</code> - Radius of a circular neighborhood of each point inpainted that is considered
 by the algorithm.</dd>
<dd><code>flags</code> - Inpainting method that could be cv::INPAINT_NS or cv::INPAINT_TELEA

 The function reconstructs the selected image area from the pixel near the area boundary. The
 function may be used to remove dust and scratches from a scanned photo, or to remove undesirable
 objects from still images or video. See &lt;http://en.wikipedia.org/wiki/Inpainting&gt; for more details.

 <b>Note:</b>
 <ul>
   <li>
       An example using the inpainting technique can be found at
         opencv_source_code/samples/cpp/inpaint.cpp
   </li>
   <li>
       (Python) An example using the inpainting technique can be found at
         opencv_source_code/samples/python/inpaint.py
   </li>
 </ul></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,float,int,int)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Big h value perfectly removes noise but also
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,float,int)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 int&nbsp;templateWindowSize)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Big h value perfectly removes noise but also
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Big h value perfectly removes noise but also
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 1-channel, 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 removes image details, smaller h value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat,int,int,int)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize,
 int&nbsp;normType)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
<dd><code>normType</code> - Type of norm used for weight calculation. Can be either NORM_L2 or NORM_L1

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat,int,int)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat,int)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoising(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.MatOfFloat)">
<h3>fastNlMeansDenoising</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoising</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</span></div>
<div class="block">Perform image denoising using Non-local Means Denoising algorithm
 &lt;http://www.ipol.im/pub/algo/bcm_non_local_means_denoising/&gt; with several computational
 optimizations. Noise expected to be a gaussian white noise</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise

 This function expected to be applied to grayscale images. For colored images look at
 fastNlMeansDenoisingColored. Advanced usage of this functions can be manual denoising of colored
 image in different colorspaces. Such approach is used in fastNlMeansDenoisingColored by converting
 image to CIELAB colorspace and then separately denoise L and AB components with different h
 parameter.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float,float,int,int)">
<h3>fastNlMeansDenoisingColored</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColored</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise</dd>
<dd><code>hColor</code> - The same as h but for color components. For most images value equals 10
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float,float,int)">
<h3>fastNlMeansDenoisingColored</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColored</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise</dd>
<dd><code>hColor</code> - The same as h but for color components. For most images value equals 10
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>fastNlMeansDenoisingColored</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColored</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h,
 float&nbsp;hColor)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise</dd>
<dd><code>hColor</code> - The same as h but for color components. For most images value equals 10
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>fastNlMeansDenoisingColored</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColored</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;h)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColored(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>fastNlMeansDenoisingColored</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColored</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for colored images</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise
 will be enough to remove colored noise and do not distort colors

 The function converts image to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoising function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,float,int,int)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Bigger h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,float,int)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 int&nbsp;templateWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Bigger h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,float)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength. Bigger h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 1-channel, 2-channel, 3-channel or
 4-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat,int,int,int)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize,
 int&nbsp;normType)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
<dd><code>normType</code> - Type of norm used for weight calculation. Can be either NORM_L2 or NORM_L1</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat,int,int)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat,int)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h,
 int&nbsp;templateWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingMulti(java.util.List,org.opencv.core.Mat,int,int,org.opencv.core.MatOfFloat)">
<h3>fastNlMeansDenoisingMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 <a href="../core/MatOfFloat.html" title="class in org.opencv.core">MatOfFloat</a>&nbsp;h)</span></div>
<div class="block">Modification of fastNlMeansDenoising function for images sequence where consecutive images have been
 captured in small period of time. For example video. This version of the function is for grayscale
 images or for manual manipulation with colorspaces. See CITE: Buades2005DenoisingIS for more details
 (open access [here](https://static.aminer.org/pdf/PDF/000/317/196/spatio_temporal_wiener_filtering_of_image_sequences_using_a_parametric.pdf)).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit or 16-bit (only with NORM_L1) 1-channel,
 2-channel, 3-channel or 4-channel images sequence. All images should
 have the same type and size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Array of parameters regulating filter strength, either one
 parameter applied to all channels or one per channel in dst. Big h value
 perfectly removes noise but also removes image details, smaller h
 value preserves details but also preserves some noise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float,float,int,int)">
<h3>fastNlMeansDenoisingColoredMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColoredMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize,
 int&nbsp;searchWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels</dd>
<dd><code>searchWindowSize</code> - Size in pixels of the window that is used to compute weighted average for
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.</dd>
<dd><code>hColor</code> - The same as h but for color components.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float,float,int)">
<h3>fastNlMeansDenoisingColoredMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColoredMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 float&nbsp;hColor,
 int&nbsp;templateWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.</dd>
<dd><code>templateWindowSize</code> - Size in pixels of the template patch that is used to compute weights.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.</dd>
<dd><code>hColor</code> - The same as h but for color components.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float,float)">
<h3>fastNlMeansDenoisingColoredMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColoredMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h,
 float&nbsp;hColor)</span></div>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.</dd>
<dd><code>hColor</code> - The same as h but for color components.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int,float)">
<h3>fastNlMeansDenoisingColoredMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColoredMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize,
 float&nbsp;h)</span></div>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels</dd>
<dd><code>h</code> - Parameter regulating filter strength for luminance component. Bigger h value perfectly
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="fastNlMeansDenoisingColoredMulti(java.util.List,org.opencv.core.Mat,int,int)">
<h3>fastNlMeansDenoisingColoredMulti</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">fastNlMeansDenoisingColoredMulti</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;srcImgs,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;imgToDenoiseIndex,
 int&nbsp;temporalWindowSize)</span></div>
<div class="block">Modification of fastNlMeansDenoisingMulti function for colored images sequences</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>srcImgs</code> - Input 8-bit 3-channel images sequence. All images should have the same type and
 size.</dd>
<dd><code>imgToDenoiseIndex</code> - Target image to denoise index in srcImgs sequence</dd>
<dd><code>temporalWindowSize</code> - Number of surrounding images to use for target image denoising. Should
 be odd. Images from imgToDenoiseIndex - temporalWindowSize / 2 to
 imgToDenoiseIndex + temporalWindowSize / 2 from srcImgs will be used to denoise
 srcImgs[imgToDenoiseIndex] image.</dd>
<dd><code>dst</code> - Output image with the same size and type as srcImgs images.
 Should be odd. Recommended value 7 pixels
 given pixel. Should be odd. Affect performance linearly: greater searchWindowsSize - greater
 denoising time. Recommended value 21 pixels
 removes noise but also removes image details, smaller h value preserves details but also preserves
 some noise.

 The function converts images to CIELAB colorspace and then separately denoise L and AB components
 with given h parameters using fastNlMeansDenoisingMulti function.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="denoise_TVL1(java.util.List,org.opencv.core.Mat,double,int)">
<h3>denoise_TVL1</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">denoise_TVL1</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
 double&nbsp;lambda,
 int&nbsp;niters)</span></div>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional). As the image denoising, in particular, may be seen
 as the variational problem, primal-dual algorithm then can be used to perform denoising and this is
 exactly what is implemented.

 It should be noted, that this implementation was taken from the July 2013 blog entry
 CITE: MA13 , which also contained (slightly more general) ready-to-use source code on Python.
 Subsequently, that code was rewritten on C++ with the usage of openCV by Vadim Pisarevsky at the end
 of July 2013 and finally it was slightly adapted by later authors.

 Although the thorough discussion and justification of the algorithm involved may be found in
 CITE: ChambolleEtAl, it might make sense to skim over it here, following CITE: MA13 . To begin
 with, we consider the 1-byte gray-level images as the functions from the rectangular domain of
 pixels (it may be seen as set
 \(\left\{(x,y)\in\mathbb{N}\times\mathbb{N}\mid 1\leq x\leq n,\;1\leq y\leq m\right\}\) for some
 \(m,\;n\in\mathbb{N}\)) into \(\{0,1,\dots,255\}\). We shall denote the noised images as \(f_i\) and with
 this view, given some image \(x\) of the same size, we may measure how bad it is by the formula

 \(\left\|\left\|\nabla x\right\|\right\| + \lambda\sum_i\left\|\left\|x-f_i\right\|\right\|\)

 \(\|\|\cdot\|\|\) here denotes \(L_2\)-norm and as you see, the first addend states that we want our
 image to be smooth (ideally, having zero gradient, thus being constant) and the second states that
 we want our result to be close to the observations we've got. If we treat \(x\) as a function, this is
 exactly the functional what we seek to minimize and here the Primal-Dual algorithm comes into play.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>observations</code> - This array should contain one or more noised versions of the image that is to
 be restored.</dd>
<dd><code>result</code> - Here the denoised image will be stored. There is no need to do pre-allocation of
 storage space, as it will be automatically allocated, if necessary.</dd>
<dd><code>lambda</code> - Corresponds to \(\lambda\) in the formulas above. As it is enlarged, the smooth
 (blurred) images are treated more favorably than detailed (but maybe more noised) ones. Roughly
 speaking, as it becomes smaller, the result will be more blur but more sever outliers will be
 removed.</dd>
<dd><code>niters</code> - Number of iterations that the algorithm will run. Of course, as more iterations as
 better, but it is hard to quantitatively refine this statement, so just use the default and
 increase it if the results are poor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="denoise_TVL1(java.util.List,org.opencv.core.Mat,double)">
<h3>denoise_TVL1</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">denoise_TVL1</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result,
 double&nbsp;lambda)</span></div>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional). As the image denoising, in particular, may be seen
 as the variational problem, primal-dual algorithm then can be used to perform denoising and this is
 exactly what is implemented.

 It should be noted, that this implementation was taken from the July 2013 blog entry
 CITE: MA13 , which also contained (slightly more general) ready-to-use source code on Python.
 Subsequently, that code was rewritten on C++ with the usage of openCV by Vadim Pisarevsky at the end
 of July 2013 and finally it was slightly adapted by later authors.

 Although the thorough discussion and justification of the algorithm involved may be found in
 CITE: ChambolleEtAl, it might make sense to skim over it here, following CITE: MA13 . To begin
 with, we consider the 1-byte gray-level images as the functions from the rectangular domain of
 pixels (it may be seen as set
 \(\left\{(x,y)\in\mathbb{N}\times\mathbb{N}\mid 1\leq x\leq n,\;1\leq y\leq m\right\}\) for some
 \(m,\;n\in\mathbb{N}\)) into \(\{0,1,\dots,255\}\). We shall denote the noised images as \(f_i\) and with
 this view, given some image \(x\) of the same size, we may measure how bad it is by the formula

 \(\left\|\left\|\nabla x\right\|\right\| + \lambda\sum_i\left\|\left\|x-f_i\right\|\right\|\)

 \(\|\|\cdot\|\|\) here denotes \(L_2\)-norm and as you see, the first addend states that we want our
 image to be smooth (ideally, having zero gradient, thus being constant) and the second states that
 we want our result to be close to the observations we've got. If we treat \(x\) as a function, this is
 exactly the functional what we seek to minimize and here the Primal-Dual algorithm comes into play.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>observations</code> - This array should contain one or more noised versions of the image that is to
 be restored.</dd>
<dd><code>result</code> - Here the denoised image will be stored. There is no need to do pre-allocation of
 storage space, as it will be automatically allocated, if necessary.</dd>
<dd><code>lambda</code> - Corresponds to \(\lambda\) in the formulas above. As it is enlarged, the smooth
 (blurred) images are treated more favorably than detailed (but maybe more noised) ones. Roughly
 speaking, as it becomes smaller, the result will be more blur but more sever outliers will be
 removed.
 better, but it is hard to quantitatively refine this statement, so just use the default and
 increase it if the results are poor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="denoise_TVL1(java.util.List,org.opencv.core.Mat)">
<h3>denoise_TVL1</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">denoise_TVL1</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;observations,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;result)</span></div>
<div class="block">Primal-dual algorithm is an algorithm for solving special types of variational problems (that is,
 finding a function to minimize some functional). As the image denoising, in particular, may be seen
 as the variational problem, primal-dual algorithm then can be used to perform denoising and this is
 exactly what is implemented.

 It should be noted, that this implementation was taken from the July 2013 blog entry
 CITE: MA13 , which also contained (slightly more general) ready-to-use source code on Python.
 Subsequently, that code was rewritten on C++ with the usage of openCV by Vadim Pisarevsky at the end
 of July 2013 and finally it was slightly adapted by later authors.

 Although the thorough discussion and justification of the algorithm involved may be found in
 CITE: ChambolleEtAl, it might make sense to skim over it here, following CITE: MA13 . To begin
 with, we consider the 1-byte gray-level images as the functions from the rectangular domain of
 pixels (it may be seen as set
 \(\left\{(x,y)\in\mathbb{N}\times\mathbb{N}\mid 1\leq x\leq n,\;1\leq y\leq m\right\}\) for some
 \(m,\;n\in\mathbb{N}\)) into \(\{0,1,\dots,255\}\). We shall denote the noised images as \(f_i\) and with
 this view, given some image \(x\) of the same size, we may measure how bad it is by the formula

 \(\left\|\left\|\nabla x\right\|\right\| + \lambda\sum_i\left\|\left\|x-f_i\right\|\right\|\)

 \(\|\|\cdot\|\|\) here denotes \(L_2\)-norm and as you see, the first addend states that we want our
 image to be smooth (ideally, having zero gradient, thus being constant) and the second states that
 we want our result to be close to the observations we've got. If we treat \(x\) as a function, this is
 exactly the functional what we seek to minimize and here the Primal-Dual algorithm comes into play.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>observations</code> - This array should contain one or more noised versions of the image that is to
 be restored.</dd>
<dd><code>result</code> - Here the denoised image will be stored. There is no need to do pre-allocation of
 storage space, as it will be automatically allocated, if necessary.
 (blurred) images are treated more favorably than detailed (but maybe more noised) ones. Roughly
 speaking, as it becomes smaller, the result will be more blur but more sever outliers will be
 removed.
 better, but it is hard to quantitatively refine this statement, so just use the default and
 increase it if the results are poor.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemap(float)">
<h3>createTonemap</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Tonemap.html" title="class in org.opencv.photo">Tonemap</a></span>&nbsp;<span class="element-name">createTonemap</span><wbr><span class="parameters">(float&nbsp;gamma)</span></div>
<div class="block">Creates simple linear mapper with gamma correction</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - positive value for gamma correction. Gamma value of 1.0 implies no correction, gamma
 equal to 2.2f is suitable for most displays.
 Generally gamma &gt; 1 brightens the image and gamma &lt; 1 darkens it.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemap()">
<h3>createTonemap</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Tonemap.html" title="class in org.opencv.photo">Tonemap</a></span>&nbsp;<span class="element-name">createTonemap</span>()</div>
<div class="block">Creates simple linear mapper with gamma correction

 equal to 2.2f is suitable for most displays.
 Generally gamma &gt; 1 brightens the image and gamma &lt; 1 darkens it.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapDrago(float,float,float)">
<h3>createTonemapDrago</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></span>&nbsp;<span class="element-name">createTonemapDrago</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;saturation,
 float&nbsp;bias)</span></div>
<div class="block">Creates TonemapDrago object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>saturation</code> - positive saturation enhancement value. 1.0 preserves saturation, values greater
 than 1 increase saturation and values less than 1 decrease it.</dd>
<dd><code>bias</code> - value for bias function in [0, 1] range. Values from 0.7 to 0.9 usually give best
 results, default value is 0.85.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapDrago(float,float)">
<h3>createTonemapDrago</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></span>&nbsp;<span class="element-name">createTonemapDrago</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;saturation)</span></div>
<div class="block">Creates TonemapDrago object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>saturation</code> - positive saturation enhancement value. 1.0 preserves saturation, values greater
 than 1 increase saturation and values less than 1 decrease it.
 results, default value is 0.85.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapDrago(float)">
<h3>createTonemapDrago</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></span>&nbsp;<span class="element-name">createTonemapDrago</span><wbr><span class="parameters">(float&nbsp;gamma)</span></div>
<div class="block">Creates TonemapDrago object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap
 than 1 increase saturation and values less than 1 decrease it.
 results, default value is 0.85.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapDrago()">
<h3>createTonemapDrago</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapDrago.html" title="class in org.opencv.photo">TonemapDrago</a></span>&nbsp;<span class="element-name">createTonemapDrago</span>()</div>
<div class="block">Creates TonemapDrago object

 than 1 increase saturation and values less than 1 decrease it.
 results, default value is 0.85.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapReinhard(float,float,float,float)">
<h3>createTonemapReinhard</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></span>&nbsp;<span class="element-name">createTonemapReinhard</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;intensity,
 float&nbsp;light_adapt,
 float&nbsp;color_adapt)</span></div>
<div class="block">Creates TonemapReinhard object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>intensity</code> - result intensity in [-8, 8] range. Greater intensity produces brighter results.</dd>
<dd><code>light_adapt</code> - light adaptation in [0, 1] range. If 1 adaptation is based only on pixel
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.</dd>
<dd><code>color_adapt</code> - chromatic adaptation in [0, 1] range. If 1 channels are treated independently,
 if 0 adaptation level is the same for each channel.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapReinhard(float,float,float)">
<h3>createTonemapReinhard</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></span>&nbsp;<span class="element-name">createTonemapReinhard</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;intensity,
 float&nbsp;light_adapt)</span></div>
<div class="block">Creates TonemapReinhard object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>intensity</code> - result intensity in [-8, 8] range. Greater intensity produces brighter results.</dd>
<dd><code>light_adapt</code> - light adaptation in [0, 1] range. If 1 adaptation is based only on pixel
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapReinhard(float,float)">
<h3>createTonemapReinhard</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></span>&nbsp;<span class="element-name">createTonemapReinhard</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;intensity)</span></div>
<div class="block">Creates TonemapReinhard object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>intensity</code> - result intensity in [-8, 8] range. Greater intensity produces brighter results.
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapReinhard(float)">
<h3>createTonemapReinhard</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></span>&nbsp;<span class="element-name">createTonemapReinhard</span><wbr><span class="parameters">(float&nbsp;gamma)</span></div>
<div class="block">Creates TonemapReinhard object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap
 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapReinhard()">
<h3>createTonemapReinhard</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapReinhard.html" title="class in org.opencv.photo">TonemapReinhard</a></span>&nbsp;<span class="element-name">createTonemapReinhard</span>()</div>
<div class="block">Creates TonemapReinhard object

 value, if 0 it's global, otherwise it's a weighted mean of this two cases.
 if 0 adaptation level is the same for each channel.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapMantiuk(float,float,float)">
<h3>createTonemapMantiuk</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></span>&nbsp;<span class="element-name">createTonemapMantiuk</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;scale,
 float&nbsp;saturation)</span></div>
<div class="block">Creates TonemapMantiuk object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>scale</code> - contrast scale factor. HVS response is multiplied by this parameter, thus compressing
 dynamic range. Values from 0.6 to 0.9 produce best results.</dd>
<dd><code>saturation</code> - saturation enhancement value. See createTonemapDrago</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapMantiuk(float,float)">
<h3>createTonemapMantiuk</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></span>&nbsp;<span class="element-name">createTonemapMantiuk</span><wbr><span class="parameters">(float&nbsp;gamma,
 float&nbsp;scale)</span></div>
<div class="block">Creates TonemapMantiuk object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap</dd>
<dd><code>scale</code> - contrast scale factor. HVS response is multiplied by this parameter, thus compressing
 dynamic range. Values from 0.6 to 0.9 produce best results.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapMantiuk(float)">
<h3>createTonemapMantiuk</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></span>&nbsp;<span class="element-name">createTonemapMantiuk</span><wbr><span class="parameters">(float&nbsp;gamma)</span></div>
<div class="block">Creates TonemapMantiuk object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>gamma</code> - gamma value for gamma correction. See createTonemap
 dynamic range. Values from 0.6 to 0.9 produce best results.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTonemapMantiuk()">
<h3>createTonemapMantiuk</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="TonemapMantiuk.html" title="class in org.opencv.photo">TonemapMantiuk</a></span>&nbsp;<span class="element-name">createTonemapMantiuk</span>()</div>
<div class="block">Creates TonemapMantiuk object

 dynamic range. Values from 0.6 to 0.9 produce best results.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createAlignMTB(int,int,boolean)">
<h3>createAlignMTB</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></span>&nbsp;<span class="element-name">createAlignMTB</span><wbr><span class="parameters">(int&nbsp;max_bits,
 int&nbsp;exclude_range,
 boolean&nbsp;cut)</span></div>
<div class="block">Creates AlignMTB object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max_bits</code> - logarithm to the base 2 of maximal shift in each dimension. Values of 5 and 6 are
 usually good enough (31 and 63 pixels shift respectively).</dd>
<dd><code>exclude_range</code> - range for exclusion bitmap that is constructed to suppress noise around the
 median value.</dd>
<dd><code>cut</code> - if true cuts images, otherwise fills the new regions with zeros.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createAlignMTB(int,int)">
<h3>createAlignMTB</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></span>&nbsp;<span class="element-name">createAlignMTB</span><wbr><span class="parameters">(int&nbsp;max_bits,
 int&nbsp;exclude_range)</span></div>
<div class="block">Creates AlignMTB object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max_bits</code> - logarithm to the base 2 of maximal shift in each dimension. Values of 5 and 6 are
 usually good enough (31 and 63 pixels shift respectively).</dd>
<dd><code>exclude_range</code> - range for exclusion bitmap that is constructed to suppress noise around the
 median value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createAlignMTB(int)">
<h3>createAlignMTB</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></span>&nbsp;<span class="element-name">createAlignMTB</span><wbr><span class="parameters">(int&nbsp;max_bits)</span></div>
<div class="block">Creates AlignMTB object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max_bits</code> - logarithm to the base 2 of maximal shift in each dimension. Values of 5 and 6 are
 usually good enough (31 and 63 pixels shift respectively).
 median value.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createAlignMTB()">
<h3>createAlignMTB</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="AlignMTB.html" title="class in org.opencv.photo">AlignMTB</a></span>&nbsp;<span class="element-name">createAlignMTB</span>()</div>
<div class="block">Creates AlignMTB object

 usually good enough (31 and 63 pixels shift respectively).
 median value.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateDebevec(int,float,boolean)">
<h3>createCalibrateDebevec</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></span>&nbsp;<span class="element-name">createCalibrateDebevec</span><wbr><span class="parameters">(int&nbsp;samples,
 float&nbsp;lambda,
 boolean&nbsp;random)</span></div>
<div class="block">Creates CalibrateDebevec object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - number of pixel locations to use</dd>
<dd><code>lambda</code> - smoothness term weight. Greater values produce smoother results, but can alter the
 response.</dd>
<dd><code>random</code> - if true sample pixel locations are chosen at random, otherwise they form a
 rectangular grid.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateDebevec(int,float)">
<h3>createCalibrateDebevec</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></span>&nbsp;<span class="element-name">createCalibrateDebevec</span><wbr><span class="parameters">(int&nbsp;samples,
 float&nbsp;lambda)</span></div>
<div class="block">Creates CalibrateDebevec object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - number of pixel locations to use</dd>
<dd><code>lambda</code> - smoothness term weight. Greater values produce smoother results, but can alter the
 response.
 rectangular grid.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateDebevec(int)">
<h3>createCalibrateDebevec</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></span>&nbsp;<span class="element-name">createCalibrateDebevec</span><wbr><span class="parameters">(int&nbsp;samples)</span></div>
<div class="block">Creates CalibrateDebevec object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>samples</code> - number of pixel locations to use
 response.
 rectangular grid.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateDebevec()">
<h3>createCalibrateDebevec</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateDebevec.html" title="class in org.opencv.photo">CalibrateDebevec</a></span>&nbsp;<span class="element-name">createCalibrateDebevec</span>()</div>
<div class="block">Creates CalibrateDebevec object

 response.
 rectangular grid.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateRobertson(int,float)">
<h3>createCalibrateRobertson</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></span>&nbsp;<span class="element-name">createCalibrateRobertson</span><wbr><span class="parameters">(int&nbsp;max_iter,
 float&nbsp;threshold)</span></div>
<div class="block">Creates CalibrateRobertson object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max_iter</code> - maximal number of Gauss-Seidel solver iterations.</dd>
<dd><code>threshold</code> - target difference between results of two successive steps of the minimization.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateRobertson(int)">
<h3>createCalibrateRobertson</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></span>&nbsp;<span class="element-name">createCalibrateRobertson</span><wbr><span class="parameters">(int&nbsp;max_iter)</span></div>
<div class="block">Creates CalibrateRobertson object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max_iter</code> - maximal number of Gauss-Seidel solver iterations.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createCalibrateRobertson()">
<h3>createCalibrateRobertson</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CalibrateRobertson.html" title="class in org.opencv.photo">CalibrateRobertson</a></span>&nbsp;<span class="element-name">createCalibrateRobertson</span>()</div>
<div class="block">Creates CalibrateRobertson object</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMergeDebevec()">
<h3>createMergeDebevec</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MergeDebevec.html" title="class in org.opencv.photo">MergeDebevec</a></span>&nbsp;<span class="element-name">createMergeDebevec</span>()</div>
<div class="block">Creates MergeDebevec object</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMergeMertens(float,float,float)">
<h3>createMergeMertens</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></span>&nbsp;<span class="element-name">createMergeMertens</span><wbr><span class="parameters">(float&nbsp;contrast_weight,
 float&nbsp;saturation_weight,
 float&nbsp;exposure_weight)</span></div>
<div class="block">Creates MergeMertens object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>contrast_weight</code> - contrast measure weight. See MergeMertens.</dd>
<dd><code>saturation_weight</code> - saturation measure weight</dd>
<dd><code>exposure_weight</code> - well-exposedness measure weight</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMergeMertens(float,float)">
<h3>createMergeMertens</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></span>&nbsp;<span class="element-name">createMergeMertens</span><wbr><span class="parameters">(float&nbsp;contrast_weight,
 float&nbsp;saturation_weight)</span></div>
<div class="block">Creates MergeMertens object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>contrast_weight</code> - contrast measure weight. See MergeMertens.</dd>
<dd><code>saturation_weight</code> - saturation measure weight</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMergeMertens(float)">
<h3>createMergeMertens</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></span>&nbsp;<span class="element-name">createMergeMertens</span><wbr><span class="parameters">(float&nbsp;contrast_weight)</span></div>
<div class="block">Creates MergeMertens object</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>contrast_weight</code> - contrast measure weight. See MergeMertens.</dd>
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMergeMertens()">
<h3>createMergeMertens</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MergeMertens.html" title="class in org.opencv.photo">MergeMertens</a></span>&nbsp;<span class="element-name">createMergeMertens</span>()</div>
<div class="block">Creates MergeMertens object</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createMergeRobertson()">
<h3>createMergeRobertson</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="MergeRobertson.html" title="class in org.opencv.photo">MergeRobertson</a></span>&nbsp;<span class="element-name">createMergeRobertson</span>()</div>
<div class="block">Creates MergeRobertson object</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>automatically generated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decolor(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>decolor</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">decolor</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;grayscale,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;color_boost)</span></div>
<div class="block">Transforms a color image to a grayscale image. It is a basic tool in digital printing, stylized
 black-and-white photograph rendering, and in many single channel image processing applications
 CITE: CL12 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>grayscale</code> - Output 8-bit 1-channel image.</dd>
<dd><code>color_boost</code> - Output 8-bit 3-channel image.

 This function is to be applied on color images.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="seamlessClone(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Point,org.opencv.core.Mat,int)">
<h3>seamlessClone</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">seamlessClone</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Point.html" title="class in org.opencv.core">Point</a>&nbsp;p,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;blend,
 int&nbsp;flags)</span></div>
<div class="block">Performs seamless cloning to blend a region from a source image into a destination image.
 This function is designed for local image editing, allowing changes restricted to a region
 (manually selected as the ROI) to be applied effortlessly and seamlessly. These changes can
 range from slight distortions to complete replacement by novel content CITE: PM03.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - The source image (8-bit 3-channel), from which a region will be blended into the destination.</dd>
<dd><code>dst</code> - The destination image (8-bit 3-channel), where the src image will be blended.</dd>
<dd><code>mask</code> - A binary mask (8-bit, 1, 3, or 4-channel) specifying the region in the source image to blend.
 Non-zero pixels indicate the region to be blended. If an empty Mat is provided, a mask with
 all non-zero pixels is created internally.</dd>
<dd><code>p</code> - The point where the center of the src image is placed in the dst image.</dd>
<dd><code>blend</code> - The output image that stores the result of the seamless cloning. It has the same size and type as <code>dst</code>.</dd>
<dd><code>flags</code> - Flags that control the type of cloning method, can take values of <code>cv::SeamlessCloneFlags</code>.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float,float)">
<h3>colorChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">colorChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;red_mul,
 float&nbsp;green_mul,
 float&nbsp;blue_mul)</span></div>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>red_mul</code> - R-channel multiply factor.</dd>
<dd><code>green_mul</code> - G-channel multiply factor.</dd>
<dd><code>blue_mul</code> - B-channel multiply factor.

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>colorChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">colorChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;red_mul,
 float&nbsp;green_mul)</span></div>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>red_mul</code> - R-channel multiply factor.</dd>
<dd><code>green_mul</code> - G-channel multiply factor.

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>colorChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">colorChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;red_mul)</span></div>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .</dd>
<dd><code>red_mul</code> - R-channel multiply factor.

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="colorChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>colorChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">colorChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Given an original color image, two differently colored versions of this image can be mixed
 seamlessly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src .

 Multiplication factor is between .5 to 2.5.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="illuminationChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>illuminationChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">illuminationChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;alpha,
 float&nbsp;beta)</span></div>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>alpha</code> - Value ranges between 0-2.</dd>
<dd><code>beta</code> - Value ranges between 0-2.

 This is useful to highlight under-exposed foreground objects or to reduce specular reflections.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="illuminationChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>illuminationChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">illuminationChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;alpha)</span></div>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>alpha</code> - Value ranges between 0-2.

 This is useful to highlight under-exposed foreground objects or to reduce specular reflections.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="illuminationChange(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>illuminationChange</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">illuminationChange</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Applying an appropriate non-linear transformation to the gradient field inside the selection and
 then integrating back with a Poisson solver, modifies locally the apparent illumination of an image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.

 This is useful to highlight under-exposed foreground objects or to reduce specular reflections.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float,int)">
<h3>textureFlattening</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">textureFlattening</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;low_threshold,
 float&nbsp;high_threshold,
 int&nbsp;kernel_size)</span></div>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>low_threshold</code> - %Range from 0 to 100.</dd>
<dd><code>high_threshold</code> - Value &gt; 100.</dd>
<dd><code>kernel_size</code> - The size of the Sobel kernel to be used.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>textureFlattening</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">textureFlattening</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;low_threshold,
 float&nbsp;high_threshold)</span></div>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>low_threshold</code> - %Range from 0 to 100.</dd>
<dd><code>high_threshold</code> - Value &gt; 100.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>textureFlattening</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">textureFlattening</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;low_threshold)</span></div>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>low_threshold</code> - %Range from 0 to 100.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="textureFlattening(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>textureFlattening</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">textureFlattening</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;mask,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">By retaining only the gradients at edge locations, before integrating with the Poisson solver, one
 washes out the texture of the selected region, giving its contents a flat aspect. Here Canny Edge %Detector is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>mask</code> - Input 8-bit 1 or 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.

 <b>Note:</b>
 The algorithm assumes that the color of the source image is close to that of the destination. This
 assumption means that when the colors don't match, the source image color gets tinted toward the
 color of the destination image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat,int,float,float)">
<h3>edgePreservingFilter</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">edgePreservingFilter</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</span></div>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
<dd><code>flags</code> - Edge preserving filters: cv::RECURS_FILTER or cv::NORMCONV_FILTER</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat,int,float)">
<h3>edgePreservingFilter</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">edgePreservingFilter</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags,
 float&nbsp;sigma_s)</span></div>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
<dd><code>flags</code> - Edge preserving filters: cv::RECURS_FILTER or cv::NORMCONV_FILTER</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat,int)">
<h3>edgePreservingFilter</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">edgePreservingFilter</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 int&nbsp;flags)</span></div>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
<dd><code>flags</code> - Edge preserving filters: cv::RECURS_FILTER or cv::NORMCONV_FILTER</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="edgePreservingFilter(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>edgePreservingFilter</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">edgePreservingFilter</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Filtering is the fundamental operation in image and video processing. Edge-preserving smoothing
 filters are used in many different applications CITE: EM11 .</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output 8-bit 3-channel image.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detailEnhance(org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>detailEnhance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detailEnhance</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</span></div>
<div class="block">This filter enhances the details of a particular image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detailEnhance(org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>detailEnhance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detailEnhance</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s)</span></div>
<div class="block">This filter enhances the details of a particular image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="detailEnhance(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>detailEnhance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">detailEnhance</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">This filter enhances the details of a particular image.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float,float)">
<h3>pencilSketch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pencilSketch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r,
 float&nbsp;shade_factor)</span></div>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
<dd><code>shade_factor</code> - %Range between 0 to 0.1.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>pencilSketch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pencilSketch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</span></div>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>pencilSketch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pencilSketch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2,
 float&nbsp;sigma_s)</span></div>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pencilSketch(org.opencv.core.Mat,org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>pencilSketch</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pencilSketch</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst1,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst2)</span></div>
<div class="block">Pencil-like non-photorealistic line drawing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst1</code> - Output 8-bit 1-channel image.</dd>
<dd><code>dst2</code> - Output image with the same size and type as src.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stylization(org.opencv.core.Mat,org.opencv.core.Mat,float,float)">
<h3>stylization</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stylization</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s,
 float&nbsp;sigma_r)</span></div>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism. Edge-aware filters are ideal for stylization, as they can abstract regions of low
 contrast while preserving, or enhancing, high-contrast features.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
<dd><code>sigma_r</code> - %Range between 0 to 1.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stylization(org.opencv.core.Mat,org.opencv.core.Mat,float)">
<h3>stylization</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stylization</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst,
 float&nbsp;sigma_s)</span></div>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism. Edge-aware filters are ideal for stylization, as they can abstract regions of low
 contrast while preserving, or enhancing, high-contrast features.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
<dd><code>sigma_s</code> - %Range between 0 to 200.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stylization(org.opencv.core.Mat,org.opencv.core.Mat)">
<h3>stylization</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stylization</span><wbr><span class="parameters">(<a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;src,
 <a href="../core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;dst)</span></div>
<div class="block">Stylization aims to produce digital imagery with a wide variety of effects not focused on
 photorealism. Edge-aware filters are ideal for stylization, as they can abstract regions of low
 contrast while preserving, or enhancing, high-contrast features.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>src</code> - Input 8-bit 3-channel image.</dd>
<dd><code>dst</code> - Output image with the same size and type as src.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Generated on 2025-01-09 09:33:49 / OpenCV 4.11.0</small></p>
</footer>
</div>
</div>
</body>
</html>
