package com.srthinker.bbnice.test;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;

import com.srthinker.bbnice.R;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.DeviceListResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.bean.LoginResponse;
import com.srthinker.bbnice.api.bean.MediaResponse;
import com.srthinker.bbnice.api.bean.QrCodeResponse;
import com.srthinker.bbnice.api.bean.RTCStartRequestData;
import com.srthinker.bbnice.api.bean.RTCStartResponse;
import com.srthinker.bbnice.api.bean.RTCTokenResponse;
import com.srthinker.bbnice.api.repository.AIRepository;
import com.srthinker.bbnice.api.repository.DeviceRepository;
import com.srthinker.bbnice.api.repository.LocationRepository;
import com.srthinker.bbnice.api.repository.MqttRepository;
import com.srthinker.bbnice.api.repository.ApiRepositoryProvider;
import com.srthinker.bbnice.core.Result;
import com.srthinker.bbnice.home.AbstractBaseAIActivity;

import java.io.File;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Repository接口测试Activity
 * 用于测试com.srthinker.bbnice.api.repository包下的接口
 */
public class RepositoryTestActivity extends AppCompatActivity {

    private static final String TAG = "RepositoryTest";

    // UI组件
    private TextView tvResult;
    private ScrollView scrollView;

    // Repository实例
    private DeviceRepository deviceRepository;
    private LocationRepository locationRepository;
    private MqttRepository mqttRepository;
    private AIRepository aiRepository;
    private ActivityResultLauncher<String> pickImageLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_repository_test);

        // 初始化UI组件
        tvResult = findViewById(R.id.tv_result);
        scrollView = findViewById(R.id.scroll_view);

        // 获取Repository实例
        deviceRepository = ApiRepositoryProvider.getInstance(this).getDeviceRepository();
        locationRepository = ApiRepositoryProvider.getInstance(this).getLocationRepository();
        mqttRepository = ApiRepositoryProvider.getInstance(this).getMqttRepository();
        aiRepository = ApiRepositoryProvider.getInstance(this).getAiRepository();

        // 设置按钮点击事件
        setupButtons();

        pickImageLauncher = registerForActivityResult(
                new ActivityResultContracts.GetContent(),
                uri -> {
                    if (uri != null) {

                        File file = new File(uri.getPath());
                        Log.d(TAG, "testUploadMedia1: file" + uri);
                        File[] files = new File[1];
                        files[0] = file;
                        deviceRepository.uploadMedia("image", files).observe(this, new Observer<Result<MediaResponse>>() {
                            @Override
                            public void onChanged(Result<MediaResponse> result) {
                                if (result.isLoading()) {
                                    appendLog("正在上传...");
                                } else if (result.isSuccess()) {
                                    MediaResponse response = result.getData();
                                    appendLog("上传成功: " + response.getData());
                                } else {
                                    Throwable error = result.getError();
                                    appendLog("上传失败: " + error.getMessage());
                                }
                            }
                        });
                    }
                });
    }

    /**
     * 设置按钮点击事件
     */
    private void setupButtons() {
        // 设备登录
        Button btnLogin = findViewById(R.id.btn_login);
        btnLogin.setOnClickListener(v -> testLogin());

        // 获取设备二维码
        Button btnQrCode = findViewById(R.id.btn_qr_code);
        btnQrCode.setOnClickListener(v -> testGetDeviceQrCode());

        // 获取设备列表
        Button btnDeviceList = findViewById(R.id.btn_device_list);
        btnDeviceList.setOnClickListener(v -> testGetDeviceList());

        // 上报设备状态
        Button btnReportStatus = findViewById(R.id.btn_report_status);
        btnReportStatus.setOnClickListener(v -> testReportDeviceStatus());

        // 上报位置
        Button btnReportLocation = findViewById(R.id.btn_report_location);
        btnReportLocation.setOnClickListener(v -> testReportLocation());

        // 获取位置
        Button btnGetLocation = findViewById(R.id.btn_get_location);
        btnGetLocation.setOnClickListener(v -> testGetLocation());

        // 上报命令状态
        Button btnReportCommand = findViewById(R.id.btn_report_command);
        btnReportCommand.setOnClickListener(v -> testReportCommandStatus());

        // 上传文件
        Button btnUploadMedia = findViewById(R.id.btn_upload_media);
        btnUploadMedia.setOnClickListener(v -> testUploadMedia1());

        // 上传文件
        Button btnAiAgent = findViewById(R.id.btn_ai_agent);
        btnAiAgent.setOnClickListener(v -> testAIAgent());

        // 清除结果
        Button btnClear = findViewById(R.id.btn_clear);
        btnClear.setOnClickListener(v -> tvResult.setText(""));


    }

    /**
     * 测试设备登录
     */
    private void testLogin() {
        appendLog("正在测试设备登录...");

        deviceRepository.login().observe(this, new Observer<Result<LoginResponse>>() {
            @Override
            public void onChanged(Result<LoginResponse> result) {
                if (result.isLoading()) {
                    appendLog("登录请求中...");
                } else if (result.isSuccess()) {
                    LoginResponse response = result.getData();
                    appendLog("登录成功: " + response.getMessage());

                    // 保存Token
                    if (response.getData() != null) {
                        String token = response.getData().getLoginInfo().getAccessToken();
                        ApiRepositoryProvider.getInstance(RepositoryTestActivity.this).setAuthToken(token);
                        appendLog("Token已保存: " + token);
                    }
                } else {
                    Throwable error = result.getError();
                    appendLog("登录失败: " + error.getMessage());
                }
            }
        });
    }

    /**
     * 测试获取设备二维码
     */
    private void testGetDeviceQrCode() {
        appendLog("正在测试获取设备二维码...");

        deviceRepository.getDeviceQrCode().observe(this, new Observer<Result<QrCodeResponse>>() {
            @Override
            public void onChanged(Result<QrCodeResponse> result) {
                if (result.isLoading()) {
                    appendLog("获取二维码请求中...");
                } else if (result.isSuccess()) {
                    QrCodeResponse response = result.getData();
                    appendLog("获取二维码成功: " + response.getMessage());

                    if (response.getData() != null) {
                        appendLog("二维码内容: " + response.getData());
                    }
                } else {
                    Throwable error = result.getError();
                    appendLog("获取二维码失败: " + error.getMessage());
                }
            }
        });
    }

    /**
     * 测试获取设备列表
     */
    private void testGetDeviceList() {
        appendLog("正在测试获取设备列表...");

        deviceRepository.getDeviceList().observe(this, new Observer<Result<DeviceListResponse>>() {
            @Override
            public void onChanged(Result<DeviceListResponse> result) {
                if (result.isLoading()) {
                    appendLog("获取设备列表请求中...");
                } else if (result.isSuccess()) {
                    DeviceListResponse response = result.getData();
                    appendLog("获取设备列表成功: " + response.getMessage());

                    if (response.getData() != null) {
                        appendLog("设备数量: " + response.getData().size());
                        for (int i = 0; i < response.getData().size(); i++) {
                            appendLog("设备" + (i + 1) + ": " + response.getData().get(i).getDeviceId() +
                                    " - " + response.getData().get(i).getPetName());
                        }
                    }
                } else {
                    Throwable error = result.getError();
                    appendLog("获取设备列表失败: " + error.getMessage());
                }
            }
        });
    }

    /**
     * 测试上报设备状态
     */
    private void testReportDeviceStatus() {
        appendLog("正在测试上报设备状态...");

        int batteryLevel = 85;
        String networkStatus = "4G";
        int signalStrength = 4;

        appendLog("电量: " + batteryLevel + "%");
        appendLog("网络状态: " + networkStatus);
        appendLog("信号强度: " + signalStrength);

        deviceRepository.reportDeviceStatus(batteryLevel, networkStatus, signalStrength)
                .observe(this, new Observer<Result<BaseResponse>>() {
                    @Override
                    public void onChanged(Result<BaseResponse> result) {
                        if (result.isLoading()) {
                            appendLog("上报设备状态请求中...");
                        } else if (result.isSuccess()) {
                            BaseResponse response = result.getData();
                            appendLog("上报设备状态成功: " + response.getMessage());
                        } else {
                            Throwable error = result.getError();
                            appendLog("上报设备状态失败: " + error.getMessage());
                        }
                    }
                });
    }

    /**
     * 测试上报位置
     */
    private void testReportLocation() {
        appendLog("正在测试上报位置...");

        // 创建位置数据
        LocationData locationData = new LocationData();
        locationData.setLatitude(39.9042); // 北京天安门
        locationData.setLongitude(116.4074);
        locationData.setAltitude(50.0);
        locationData.setAccuracy(10.0f);
        locationData.setSpeed(0.0f);
        locationData.setBearing(0.0f);
        locationData.setTime(System.currentTimeMillis());

        appendLog("位置: 纬度=" + locationData.getLatitude() + ", 经度=" + locationData.getLongitude());
        appendLog("精度: " + locationData.getAccuracy() + "米");
        appendLog("时间戳: " + formatTimestamp(locationData.getTime()));

        locationRepository.reportLocation(locationData).observe(this, new Observer<Result<BaseResponse>>() {
            @Override
            public void onChanged(Result<BaseResponse> result) {
                if (result.isLoading()) {
                    appendLog("上报位置请求中...");
                } else if (result.isSuccess()) {
                    BaseResponse response = result.getData();
                    appendLog("上报位置成功: " + response.getMessage());
                } else {
                    Throwable error = result.getError();
                    appendLog("上报位置失败: " + error.getMessage());
                }
            }
        });
    }

    private void testGetLocation() {
        appendLog("正在测试上报获取...");
        locationRepository.getCurrentLocation().observe(this, new Observer<Result<LocationData>>() {

            @Override
            public void onChanged(Result<LocationData> result) {
                if (result.isLoading()) {
                    appendLog("正在获取位置...");
                } else if (result.isSuccess()) {
                    LocationData response = result.getData();
                    appendLog("位置获取成功: " + response.toString());
                } else {
                    Throwable error = result.getError();
                    appendLog("位置获取失败: " + error.getMessage());
                }
            }
        });
    }


    private void testUploadMedia() {

        try (InputStream ips = getAssets().open("panda.jpg")) {
            byte[] data = new byte[ips.available()];
            ips.read(data);
            deviceRepository.uploadMedia("image", data).observe(this, new Observer<Result<MediaResponse>>() {
                @Override
                public void onChanged(Result<MediaResponse> result) {
                    if (result.isLoading()) {
                        appendLog("正在上传...");
                    } else if (result.isSuccess()) {
                        MediaResponse response = result.getData();
                        appendLog("上传成功: " + response.getData());
                    } else {
                        Throwable error = result.getError();
                        appendLog("上传失败: " + error.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            appendLog("位置获取失败: " + e.getMessage());
        }

    }

    private void testUploadMedia1() {
        pickImageLauncher.launch("image/*");
    }


    private void testAIAgent() {
        aiRepository.aiTokens("123").observe(RepositoryTestActivity.this, rtcTokenResponseResult -> {
            if (rtcTokenResponseResult.isSuccess()) {
                RTCTokenResponse rtcTokenResponse = rtcTokenResponseResult.getData();
                if (rtcTokenResponse.isSuccess()) {
                    appendLog(rtcTokenResponse.toString());
                    String token = rtcTokenResponse.getData().getToken();
                    RTCStartRequestData requestData = new RTCStartRequestData();
                    requestData.room_id = "345";
                    requestData.enable_vision = true;
                    requestData.enable_subtitle = true;
                    requestData.welcome_message = "nihao";
//                    requestData.system_message = roomMessage.systemMessage;
                    aiRepository.rtcStart(requestData).observe(RepositoryTestActivity.this, rtcStartResponseResult -> {
                        if (rtcStartResponseResult.isSuccess()) {
                            RTCStartResponse rtcStartResponse = rtcStartResponseResult.getData();
                            Log.d(TAG, "testAIAgent: " + rtcStartResponse);
                        }
                    });
                }
            }
        });
    }

    /**
     * 测试上报命令状态
     */
    private void testReportCommandStatus() {
        appendLog("正在测试上报命令状态...");

        String commandId = "cmd_" + System.currentTimeMillis();
        String status = "success";
        String message = "命令执行成功";
        long timestamp = System.currentTimeMillis();

        appendLog("命令ID: " + commandId);
        appendLog("状态: " + status);
        appendLog("消息: " + message);
        appendLog("时间戳: " + formatTimestamp(timestamp));

        mqttRepository.reportCommandStatus(commandId, status, message, timestamp)
                .observe(this, new Observer<Result<BaseResponse>>() {
                    @Override
                    public void onChanged(Result<BaseResponse> result) {
                        if (result.isLoading()) {
                            appendLog("上报命令状态请求中...");
                        } else if (result.isSuccess()) {
                            BaseResponse response = result.getData();
                            appendLog("上报命令状态成功: " + response.getMessage());
                        } else {
                            Throwable error = result.getError();
                            appendLog("上报命令状态失败: " + error.getMessage());
                        }
                    }
                });
    }


    /**
     * 添加日志
     * @param message 日志消息
     */
    private void appendLog(String message) {
        Log.d(TAG, message);

        String timestamp = new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(new Date());
        String log = timestamp + " - " + message + "\n";

        tvResult.append(log);

        // 滚动到底部
        scrollView.post(() -> scrollView.fullScroll(View.FOCUS_DOWN));
    }

    /**
     * 格式化时间戳
     * @param timestamp 时间戳
     * @return 格式化后的时间
     */
    private String formatTimestamp(long timestamp) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(new Date(timestamp));
    }
}
