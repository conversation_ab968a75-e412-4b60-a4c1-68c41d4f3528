package com.srthinker.bbnice.location;

import android.content.Context;
import android.util.Log;

import com.srthinker.bbnice.api.ApiCallback;
import com.srthinker.bbnice.api.ApiError;
import com.srthinker.bbnice.api.BaseResponse;
import com.srthinker.bbnice.api.bean.LocationData;
import com.srthinker.bbnice.api.repository.LocationRepository;
import com.srthinker.bbnice.api.repository.RepositoryProvider;

/**
 * 位置上报示例类
 * 演示如何上报位置信息
 */
public class LocationReportExample {
    private static final String TAG = "LocationReportExample";

    /**
     * 上报当前位置
     * @param context 上下文
     */
    public static void reportCurrentLocation(Context context) {
        // 获取位置管理器
        LocationManager locationManager = LocationManager.getInstance(context);

        // 获取当前位置
        locationManager.getCurrentLocation(new ApiCallback<LocationData>() {
            @Override
            public void onSuccess(LocationData locationData) {
                Log.d(TAG, "获取当前位置成功: " + locationData);

                // 上报位置
                reportLocation(context, locationData);
            }

            @Override
            public void onError(ApiError error) {
                Log.e(TAG, "获取当前位置失败: " + error.getMessage());
            }
        });
    }

    /**
     * 上报位置
     * @param context 上下文
     * @param locationData 位置数据
     */
    public static void reportLocation(Context context, LocationData locationData) {
        // 获取LocationRepository
        LocationRepository locationRepository = RepositoryProvider.getInstance(context).getLocationRepository();

        // 上报位置
        locationRepository.reportLocation(locationData).observeForever(result -> {
            if (result.isLoading()) {
                return;
            }

            if (result.isSuccess()) {
                BaseResponse response = result.getData();
                if (response.isSuccess()) {
                    Log.d(TAG, "位置上报成功");
                } else {
                    Log.e(TAG, "位置上报失败: " + response.getMessage());
                }
            } else {
                Throwable error = result.getError();
                Log.e(TAG, "位置上报错误: " + (error != null ? error.getMessage() : "Unknown error"));
            }

            // 移除观察者，避免内存泄漏
            locationRepository.reportLocation(locationData).removeObserver(result1 -> {});
        });
    }

    /**
     * 启动位置服务
     * @param context 上下文
     */
    public static void startLocationService(Context context) {
        LocationService.startService(context);
        Log.d(TAG, "位置服务已启动");
    }

    /**
     * 停止位置服务
     * @param context 上下文
     */
    public static void stopLocationService(Context context) {
        LocationService.stopService(context);
        Log.d(TAG, "位置服务已停止");
    }
}
